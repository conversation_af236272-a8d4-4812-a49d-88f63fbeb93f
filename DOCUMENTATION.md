# Onbord Financial Dashboard Documentation

## Overview

This document provides a comprehensive guide to the Onbord Financial Dashboard, including its architecture, data model, features, and development guidelines.

## Documentation Structure

All documentation is organized in the `docs` directory with the following structure:

### [User Guide](./docs/user-guide/README.md)
Documentation for end users of the application:
- [Getting Started](./docs/user-guide/getting-started/README.md)
- [Cashflow Projection](./docs/user-guide/features/cashflow/README.md)
- [Estimates](./docs/user-guide/features/estimates/README.md)
- [CRM and Deal Management](./docs/user-guide/features/crm/README.md)
- [Reports](./docs/user-guide/features/reports/README.md)
- [Troubleshooting](./docs/user-guide/troubleshooting/README.md)

### [Technical Documentation](./docs/technical/README.md)
Documentation for developers:
- [Architecture Overview](./docs/technical/architecture/README.md)
- [Data Model](./docs/technical/data-model/README.md)
- [Repository Pattern](./docs/technical/data-model/repository-pattern.md)
- [Component Guides](./docs/technical/components/README.md)
- [Integration Guides](./docs/technical/integrations/README.md)

### [API Reference](./docs/api-reference/README.md)
Reference documentation for external APIs:
- [Xero API Integration](./docs/api-reference/xero/README.md)
- [Harvest API Integration](./docs/api-reference/harvest/README.md)

## Key Concepts

### Unified Data Model
The application uses a unified data model that consolidates data from multiple sources (HubSpot, Harvest, Xero) into a single coherent structure. This approach provides:
- Single source of truth for all entity data
- Multi-system integration with field-level source tracking
- Audit trails for all changes
- Flexible relationships between entities

### Repository Pattern
All data access is handled through repository classes that provide:
- Type-safe interfaces
- Consistent error handling
- Transaction support
- Query optimization
- Table existence checking

### Smart Forecast System
The Smart Forecast system automatically generates income projections based on:
- Remaining project budgets from Harvest
- Uninvoiced work already completed
- Project-specific invoice frequencies and payment terms
- Historical payment patterns

## Development Guidelines

### Code Organization
- **Frontend**: React with TypeScript (`src/frontend/`)
- **Backend**: Express.js with TypeScript (`src/api/`)
- **Database**: SQLite with repository pattern (`src/api/repositories/`)
- **Services**: Business logic layer (`src/services/`)
- **Types**: Shared TypeScript definitions (`src/types/`)

### Best Practices
1. **Always use repositories** for data access
2. **Check table existence** before querying (especially for newer tables)
3. **Use transactions** for operations that modify multiple tables
4. **Include audit information** - always set created_by/updated_by fields
5. **Respect field ownership** - only update fields your system owns
6. **Write tests** for new features and bug fixes

### Database Schema
The database uses SQLite and includes the following core tables:
- `company` - Unified company data
- `contact` - Individual contacts
- `deal` - Sales opportunities
- `estimate` - Project estimates
- Various junction tables for relationships

See [Data Model Documentation](./docs/technical/data-model/unified-data-model.md) for complete schema details.

## Configuration

### Environment Variables
Key environment variables include:
- `NODE_ENV` - Development/production mode
- `XERO_CLIENT_ID` - Xero OAuth client ID
- `XERO_CLIENT_SECRET` - Xero OAuth client secret
- `HARVEST_ACCOUNT_ID` - Harvest account ID
- `HARVEST_ACCESS_TOKEN` - Harvest API token
- `HUBSPOT_ACCESS_TOKEN` - HubSpot API token (optional)

### Database Configuration
- **Development**: `./data/upstream.db`
- **Production**: `/data/upstream.db` (Render persistent disk)
- **Testing**: In-memory SQLite

## Deployment

The application is deployed on Render.com with:
- Persistent disk for SQLite database
- Automatic builds from GitHub
- Environment-specific configuration
- Custom deployment scripts in `scripts/`

See [Deployment Guide](./DEPLOYMENT-GUIDE.md) for detailed instructions.

## Testing

### Test Structure
- **Unit Tests**: `tests/unit/`
- **Integration Tests**: `tests/integration/`
- **E2E Tests**: `tests/e2e/` (Playwright)

### Running Tests
```bash
npm test              # All tests
npm test:unit        # Unit tests only
npm test:e2e         # E2E tests
npm test:real-data   # Tests with real API data
```

See [Testing Strategy](./TESTING-STRATEGY.md) for comprehensive testing guidelines.

## Troubleshooting

### Common Issues

1. **Database Errors**
   - Run `node scripts/fix-data-model.js` to create missing tables
   - Check repository queries match schema
   - Verify TypeScript types align with database

2. **API Integration Issues**
   - Verify API credentials are set
   - Check rate limits
   - Review error logs for specific API errors

3. **Build/Deploy Issues**
   - Clear dist folder and rebuild
   - Check TypeScript errors
   - Verify all dependencies are installed

## Contributing

1. Create a feature branch from `main`
2. Make your changes following the coding standards
3. Write/update tests as needed
4. Update documentation
5. Submit a pull request

## Support

For questions or issues:
- Check existing documentation
- Review [Troubleshooting Guide](./docs/user-guide/troubleshooting/README.md)
- Contact the development team

## Version History

See [constants/versionHistory.ts](./src/constants/versionHistory.ts) for detailed version history and changelog.