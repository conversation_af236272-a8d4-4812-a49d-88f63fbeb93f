# UX Review V2: Visual Diagrams & User Flows

## Overview

This document provides visual representations of the V2 structure, including information architecture, user flows, and interface concepts.

## 1. Information Architecture

### Current vs V2 Structure

**Current Structure (Feature-Based)**
```
Root
├── Cashflow
├── Smart Forecast
├── Expenses
├── Deals
├── Leads
│   ├── Radar
│   ├── Relationships
│   └── WA Govt
├── Estimates
├── Reports
└── CRM (Hidden Submenu)
    ├── Companies
    ├── Contacts
    ├── HubSpot
    └── Data Management
```

**V2 Structure (Business Function-Based)**
```
Command Center (Dashboard)
├── Operations Hub
│   ├── Cash Position
│   │   ├── Current Balance
│   │   ├── Projections
│   │   └── Scenarios
│   ├── Payables
│   │   ├── Bills
│   │   ├── Expenses
│   │   └── Payroll
│   └── Receivables
│       ├── Invoices
│       ├── Collections
│       └── Aging
├── Growth Hub
│   ├── Pipeline
│   │   ├── Opportunities
│   │   ├── Stages
│   │   └── Forecasting
│   ├── Clients
│   │   ├── Profiles
│   │   ├── Radar View
│   │   └── Relationships
│   └── Estimates
│       ├── Create
│       ├── Library
│       └── Tracking
└── Intelligence Hub
    ├── Analytics
    │   ├── Financial
    │   ├── Operational
    │   └── Strategic
    ├── Forecasting
    │   ├── Smart Forecast
    │   ├── Scenarios
    │   └── What-If
    └── Reports
        ├── Standard
        ├── Custom
        └── Executive
```

## 2. Dashboard Layout Concept

```
┌─────────────────────────────────────────────────────────────────┐
│ [Logo] Upstream                    [Search]  [Alerts] [Profile] │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  Welcome back, Sarah                          Tuesday, May 28   │
│                                                                 │
│  ┌──────────────────┐  ┌──────────────────┐  ┌──────────────┐ │
│  │  CASH POSITION   │  │  PIPELINE HEALTH  │  │ UTILIZATION  │ │
│  │                  │  │                   │  │              │ │
│  │    $248,530     │  │   12 Opportunities│  │     78%      │ │
│  │    ▲ 3.2%       │  │   $1.2M Total     │  │   ▲ 5pts     │ │
│  │                  │  │                   │  │              │ │
│  │ [Mini Chart]    │  │  [Pipeline Chart] │  │ [Team Chart] │ │
│  └──────────────────┘  └──────────────────┘  └──────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────┐  ┌──────────────────────┐ │
│  │      ACTION REQUIRED             │  │   QUICK ACTIONS      │ │
│  │                                  │  │                      │ │
│  │  ⚠️  3 Overdue invoices ($45k)   │  │  [+] New Opportunity │ │
│  │  📋 2 Estimates expiring soon    │  │  [+] Record Expense  │ │
│  │  💰 Payroll due tomorrow         │  │  [+] Create Estimate │ │
│  │  📊 Q2 Report ready for review   │  │  [+] Add Client Note │ │
│  │                                  │  │                      │ │
│  │            [View All]            │  │                      │ │
│  └─────────────────────────────────┘  └──────────────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                 CASH FLOW TREND                          │  │
│  │  [Interactive line chart showing 30-day projection]      │  │
│  │                                                           │  │
│  │  Worst: $125k | Expected: $248k | Best: $380k           │  │
│  └─────────────────────────────────────────────────────────┘  │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 3. Navigation Design

### Desktop Navigation (Left Sidebar)
```
┌────────────────┐
│   [Logo]       │
│ Command Center │
├────────────────┤
│ OPERATIONS     │
│ ├ Cash Position│
│ ├ Payables     │
│ └ Receivables  │
├────────────────┤
│ GROWTH         │
│ ├ Pipeline     │
│ ├ Clients      │
│ └ Estimates    │
├────────────────┤
│ INTELLIGENCE   │
│ ├ Analytics    │
│ ├ Forecasting  │
│ └ Reports      │
├────────────────┤
│ [Settings]     │
│ [Help]         │
└────────────────┘
```

### Mobile Navigation (Bottom Tab Bar)
```
┌─────────────────────────────────┐
│         Current View            │
│                                 │
│         [Content Area]          │
│                                 │
├─────┬─────┬─────┬─────┬───────┤
│ 🏠  │ 💰  │ 📈  │ 🧠  │  ⚙️   │
│Home │ Ops │Growth│Intel│ More  │
└─────┴─────┴─────┴─────┴───────┘
```

## 4. Key User Flows

### Flow 1: Check Morning Cash Position
```
Dashboard → Operations Hub → Cash Position
    │
    ├─→ View current balance
    ├─→ Check 30-day projection
    ├─→ Review upcoming transactions
    └─→ Identify potential issues
```

### Flow 2: Create Estimate from Opportunity
```
Growth Hub → Pipeline → Select Opportunity
    │
    ├─→ Click "Create Estimate"
    ├─→ Auto-populate client/project info
    ├─→ Configure team & rates
    ├─→ Save as draft or publish
    └─→ Link back to opportunity
```

### Flow 3: Weekly Business Review
```
Intelligence Hub → Analytics
    │
    ├─→ Financial Performance
    │   ├─→ Revenue vs Budget
    │   ├─→ Expense Analysis
    │   └─→ Profitability
    │
    ├─→ Operational Metrics
    │   ├─→ Team Utilization
    │   ├─→ Project Status
    │   └─→ Delivery Health
    │
    └─→ Export Executive Summary
```

## 5. Component Concepts

### Unified Client Profile
```
┌─────────────────────────────────────────────────────────┐
│  ACME Corporation                          ⭐ Strategic │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  Overview | Opportunities | Projects | Financials | Notes│
│  ═══════════════════════════════════════════════════════│
│                                                         │
│  ┌─────────────────┐  ┌──────────────────────────────┐ │
│  │  Client Summary │  │  Relationship Timeline        │ │
│  │                 │  │                               │ │
│  │  Industry: Tech │  │  May 28: Proposal sent       │ │
│  │  Since: 2019    │  │  May 15: Discovery call      │ │
│  │  Value: $450k   │  │  May 01: Initial contact     │ │
│  │  Health: 🟢     │  │  Apr 15: Referral received   │ │
│  └─────────────────┘  └──────────────────────────────┘ │
│                                                         │
│  Active Opportunities (3)              Total: $125,000  │
│  ┌─────────────────────────────────────────────────┐  │
│  │ 🔵 Website Redesign      Proposal    $50,000   │  │
│  │ 🔵 API Integration       Discovery   $35,000   │  │
│  │ 🟡 Support Contract      Negotiation  $40,000   │  │
│  └─────────────────────────────────────────────────┘  │
│                                                         │
│  Current Projects (2)                   Utilization: 65%│
│  ┌─────────────────────────────────────────────────┐  │
│  │ Mobile App Phase 2       75% Complete  $80,000  │  │
│  │ Cloud Migration          30% Complete  $120,000 │  │
│  └─────────────────────────────────────────────────┘  │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### Enhanced Pipeline Board
```
┌─────────────────────────────────────────────────────────┐
│  Pipeline Overview                    Total: $1,234,567  │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  Discovery      Proposal       Contract      Closed Won │
│  $234,000      $456,000       $321,000      $223,567   │
│                                                         │
│  ┌─────────┐   ┌─────────┐   ┌─────────┐   ┌─────────┐│
│  │ Deal 1  │   │ Deal 4  │   │ Deal 7  │   │ Deal 9  ││
│  │ $50k    │   │ $120k   │   │ $75k    │   │ $100k   ││
│  │ [✓] Est │   │ [!] Est │   │ [✓] Est │   │ ✓ Won   ││
│  ├─────────┤   ├─────────┤   ├─────────┤   ├─────────┤│
│  │ Deal 2  │   │ Deal 5  │   │ Deal 8  │   │ Deal 10 ││
│  │ $84k    │   │ $156k   │   │ $246k   │   │ $123k   ││
│  │ [ ] Est │   │ [✓] Est │   │ [✓] Est │   │ ✓ Won   ││
│  ├─────────┤   ├─────────┤   └─────────┘   └─────────┘│
│  │ Deal 3  │   │ Deal 6  │                             │
│  │ $100k   │   │ $180k   │                             │
│  │ [!] Est │   │ [ ] Est │                             │
│  └─────────┘   └─────────┘                             │
│                                                         │
│  Legend: [✓] Has estimate  [!] Needs estimate  [ ] None│
└─────────────────────────────────────────────────────────┘
```

## 6. Mobile-First Responsive Design

### Breakpoint Strategy
- Mobile: < 768px (Bottom navigation, stacked layout)
- Tablet: 768px - 1024px (Collapsible sidebar, 2-column grid)
- Desktop: > 1024px (Fixed sidebar, multi-column layout)

### Touch Interactions
- Swipe between hub sections
- Pull-to-refresh on data views
- Long-press for quick actions
- Pinch-to-zoom on charts

## 7. Visual Design System

### Color Palette
```
Primary Actions:    #2563EB (Blue)
Success/Income:     #10B981 (Green)
Warning/Attention:  #F59E0B (Amber)
Error/Expenses:     #EF4444 (Red)
Neutral/Base:       #6B7280 (Gray)

Hub Colors:
Operations:         #7C3AED (Purple)
Growth:             #3B82F6 (Blue)
Intelligence:       #059669 (Emerald)
```

### Typography Scale
```
Heading 1:  32px/40px - Page titles
Heading 2:  24px/32px - Section headers
Heading 3:  18px/24px - Card titles
Body:       16px/24px - Regular text
Small:      14px/20px - Secondary info
Caption:    12px/16px - Labels, hints
```

### Component Library Extension
- Enhanced Cards with status indicators
- Interactive Charts with drill-down
- Responsive Tables with mobile views
- Floating Action Buttons for quick tasks
- Progressive Disclosure panels

## 8. Transition States

### Loading States
```
┌─────────────────────┐
│  ░░░░░░░░░░░░░░░░  │  <- Skeleton loader
│  ░░░░░░░░░░░░      │     for content
│  ░░░░░░░░░░░░░░░░  │
└─────────────────────┘
```

### Empty States
```
┌─────────────────────┐
│                     │
│     [Icon]          │
│                     │
│  No data yet        │
│  [Primary Action]   │
│                     │
└─────────────────────┘
```

### Error States
```
┌─────────────────────┐
│  ⚠️ Unable to load  │
│                     │
│  [Try Again]        │
│  [Get Help]         │
└─────────────────────┘
```

## 9. Accessibility Considerations

### Keyboard Navigation
- Tab order follows visual hierarchy
- Skip links to main content
- Keyboard shortcuts for common actions
- Focus indicators on all interactive elements

### Screen Reader Support
- Semantic HTML structure
- ARIA labels for icons
- Live regions for updates
- Descriptive link text

### Visual Accessibility
- WCAG AA contrast ratios
- Colorblind-safe palettes
- Scalable interface (up to 200%)
- Reduced motion options

## 10. Performance Optimization

### Progressive Loading
```
1. Critical CSS inline
2. Navigation shell
3. Dashboard skeleton
4. Widget data parallel fetch
5. Interactive features
6. Nice-to-have enhancements
```

### Caching Strategy
- Service Worker for offline access
- Local storage for user preferences
- Session storage for temporary data
- API response caching

## Conclusion

These visual diagrams illustrate how the V2 structure creates a more intuitive, efficient interface that aligns with how professional services businesses actually operate. The three-hub model provides clear mental models while the unified dashboard gives users immediate insight into their business health.

The key is progressive disclosure—showing the right information at the right time, with clear pathways to dig deeper when needed. Combined with a mobile-first responsive design and robust accessibility features, this creates an interface that works for all users in all contexts.

---

**Related Documents**:
- [V2 Executive Summary](./ux-review-v2-executive-summary.md)
- [V2 Conceptual Model](./ux-review-v2-conceptual-model.md)
- [V2 Implementation Roadmap](./ux-review-v2-implementation-roadmap.md)