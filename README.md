# Upstream

A web application for financial planning, cashflow projection, and project estimation, integrating with Xero and Harvest.

## Overview

Upstream is a comprehensive financial management platform designed specifically for agencies and service businesses. It provides real-time visibility into your current financial position while offering powerful forecasting tools to help you make informed business decisions.

By seamlessly integrating with Xero for accounting data and Harvest for project management, Upstream eliminates the need for manual financial tracking across multiple systems. The platform combines actual financial data with intelligent projections to give you a complete picture of your company's financial future, helping you anticipate cash flow challenges before they occur and make strategic decisions with confidence.

What sets Upstream apart is its Smart Forecast system, which automatically generates income projections based on project budgets and timelines rather than requiring manual draft invoice creation. This approach provides more accurate forecasts while saving significant time. The platform also offers detailed project estimation tools, custom expense management, and comprehensive tax planning features including GST/BAS prediction and payroll tax tracking.

### Key Features

- **Cashflow Projection**: Visualize daily bank balances over configurable timeframes (30/60/90 days)
- **Smart Forecast System**: Automated project-based income projection without manual draft invoices
- **Custom Expenses Management**: Create, edit, and manage recurring expenses with different types and frequencies
- **CRM & Deal Management**: Track deals, contacts, and companies with a Kanban board, comprehensive deal editing, and strategic client radar with enhanced company cards
- **Harvest Integration**: Pull invoice data to include in cashflow projections
- **Xero Integration**: Fetch bank account balances, transactions, and create expenses with robust identification
- **Transaction Breakdown**: Detailed view of all upcoming financial events
- **Projection Audit Log**: Transparency feature for understanding projection filtering decisions
- **Staff Utilisation Report**: Track team utilisation based on billable hours vs. weekly capacity
- **Estimate Feature**: Create, manage, and track project estimates integrated with Harvest.
  - **UI:** Modern interface (`EstimatePage.tsx`) featuring floating action panels (`FloatingEstimateActions`, `FloatingFinancialSummary`) and interactive tables/grids (`TeamMembersTable`, `TimeAllocationGrid`) for configuration and review. Includes a visual `WeeklyEffortHistogram` with improved alignment, tooltips, and spacing.
  - **Functionality:** Define project scope (client, dates, name), allocate staff (fetched from Harvest), define proposed daily rates, and input weekly estimated effort (days). Supports discounts (both percentage and fixed amount).
  - **Calculations:** Automatically calculates key financial metrics (Total Cost, Fees, Gross Margin $, GM %) per staff member and for the overall project, including comparisons against target rates and discount applications. Logic resides in `utils.ts` and `useEstimateStaffManagement.ts`.
  - **Draft Storage:** Save estimates as internal drafts (`SQLite` database via backend API `/api/estimates/drafts`) for later refinement. Supports creating, reading, updating, and deleting drafts. Database schema includes discount fields.
  - **Harvest Integration:** Publish finalized estimates to Harvest (via backend proxy `/api/harvest/estimates`) with comprehensive terms and conditions. Clear distinction between saving drafts and publishing to Harvest, with separate workflows and explicit terminology throughout the UI. Tax flag is set on all line items.
  - **CSV Export:** Export estimate data to CSV for further analysis in spreadsheet software. Includes client/project details, staff allocations by week, costs, rates, and financial summaries in a structured format optimized for data modeling.
  - **Architecture:** Utilizes a robust frontend architecture based on custom React Hooks (`useEstimate*`) for state management and logic encapsulation, promoting maintainability.

## Smart Forecast System

The Smart Forecast system automates financial projections by analyzing project budgets in Harvest rather than requiring manual draft invoice creation.

### Key Benefits

- **Automated Projections**: Eliminates manual creation and maintenance of draft invoices
- **Improved Accuracy**: Forecasts based on project budgets, payment terms, and uninvoiced work
- **Project-Specific Settings**: Configure invoice frequency and payment terms per project
- **Transparency**: Projection Audit Log shows filtering decisions and reasoning

### Core Functionality

- Calculates future income from remaining project budgets
- Factors in uninvoiced work already completed
- Includes outstanding invoices already sent to clients
- Distributes remaining budget across invoice periods through project completion
- Calculates payment dates based on configurable payment terms

### Filtering Rules

- Excludes projected income falling within payment terms period to prevent double-counting
- Removes projected invoices when real invoices exist within 5 days
- Clearly marks recently created invoices with a visual indicator

For detailed technical information about the Smart Forecast system, refer to the [DEVELOPMENT-GUIDE.md](DEVELOPMENT-GUIDE.md).

## Quick Start

### Prerequisites

- Node.js (v18+)
- npm (v9+)
- Modern browser with CSS Grid and dark mode support

### Installation

1. Clone the repository

   ```bash
   git clone https://github.com/onbord/financial-analysis-prototype.git
   cd onbord-financial-analysis-prototype
   ```

1. Install dependencies

   ```bash
   npm install
   ```

1. Configure environment variables

   Create a `.env` file in the root directory with the following content:

   ```env
   # Development environment
   NODE_ENV=development
   API_PORT=3002
   FRONTEND_PORT=5173

   # Base URLs
   FRONTEND_URL=http://localhost:5173

   # Xero API
   XERO_CLIENT_ID=your_client_ID_here
   XERO_CLIENT_SECRET=your_client_secret_here
   XERO_REDIRECT_URI=http://localhost:3002/api/xero/callback
   XERO_SCOPES=openid profile email accounting.transactions accounting.reports.read accounting.settings offline_access payroll.employees payroll.payruns

   # Harvest API
   HARVEST_ACCESS_TOKEN=your_harvest_access_token_here
   HARVEST_ACCOUNT_ID=your_harvest_account_id_here

   # Session
   SESSION_SECRET=your_session_secret_change_this
   ```

   Replace the placeholder values with your actual API credentials.

### Running the Application

The application uses fixed ports to ensure compatibility with Xero's authentication:

- API Server: **3002**
- Frontend: **5173**

To start the application:

```bash
npm run dev
```

Or, if you need to free up ports first:

```bash
./kill-ports.sh && npm run dev
```

This will:

- Start both the API and frontend servers concurrently
- The application will be available at <http://localhost:5173>

## Features in Detail

### Cashflow Projection

The cashflow projection feature provides a visual representation of your financial future:

- **Interactive Chart**: Line chart showing daily bank balance over time with hover tooltips
- **Configurable Timeframe**: View projections for 30, 60, or 90 days via dropdown
- **Financial Summary**: Total money in, money out, and projected end balance
- **Color-coded Events**: Easily identify different types of financial events
- **Minimum Balance Threshold**: Set and visualize a critical minimum balance on the chart
- **Transaction Highlighting**: Hover on chart points to highlight transactions in the table below

### Custom Expenses Management

The custom expenses feature allows you to create and manage recurring expenses:

- **Multiple Expense Types**:
  - Monthly Payroll (blue)
  - Software (purple)
  - Superannuation (amber)
  - Insurances (pink)
  - Taxes (indigo)
  - Fees (rose)
  - Other (gray)

- **Flexible Frequencies**:
  - Weekly (blue)
  - Monthly (violet)
  - Quarterly (orange)
  - One-off (slate)

- **Expense Management**:
  - Create, edit, and delete expenses
  - Set specific dates for each expense
  - View monthly equivalent calculation
  - Filter expenses by type and frequency

- **Data Storage**:
  - SQLite database for reliable, persistent storage
  - Atomic database transactions for better data integrity
  - Consistent data types and validation

### Harvest Integration

The application integrates with Harvest to include invoice data in your cashflow projections and track team performance:

- **Dual Projection Systems**: Choose between classic draft invoice system and Smart Forecast
- **Enhanced Projection**: Calculates future income from project budgets, frequency settings, and uninvoiced work
- **Invoice Retrieval**: Fetches open invoices from Harvest
- **Due Date Projection**: Includes expected payment dates in the cashflow
- **Project Settings**: Configure invoice frequency and payment terms for each project
- **Automatic Transformation**: Converts invoice data to transaction format
- **Staff Utilisation Report**: Track team utilisation with accurate calculation based on billable hours vs. weekly capacity
  - **Accurate Calculation**: Uses billable hours divided by weekly capacity (from Harvest Users API)
  - **Workday-Based**: Accounts for actual workdays (excluding weekends) for multi-week periods
  - **Comparison View**: Shows both our calculation and Harvest's calculation (billable/total) side by side
  - **Flexible Time Periods**: View utilisation by week, month, quarter, year, or custom date range
  - **Visual Indicators**: Color-coded progress bars for quick assessment of utilisation levels
  - **Compact Summary**: Visual overview showing staff sorted by highest to lowest utilisation
  - **Detailed Breakdown**: Toggle between simple view and detailed task breakdown
  - **Consistent Coloring**: Green (≥70%), Yellow (50-69%), Red (<50%), Light Red (0%)

### Xero Integration

The Xero integration provides the foundation for accurate financial data:

- **Expense Types**: Separate expense types for Net Pay, Tax (PAYGW), Superannuation, and GST Payments (BAS)
- **GST Prediction**: Smart prediction of GST liability based on current quarter sales data with selectable accrued or predicted amounts
- **Accurate Payment Timing**: Tax payments set to the 21st of the following month, and GST payments to the 28th of the month following each quarter

- **Bank Account Balances**: Retrieves current bank balances as the starting point
- **Bank Transactions**: Includes upcoming bank transactions in projections
- **Bills Integration**: View and selectively import bills from Xero into your custom expenses
  - **Filtering Options**: Filter bills by status and time period (30/60/90/180 days)
  - **Selective Import**: Choose which bills to add to your cashflow projections
  - **Status Tracking**: Visual indicators show which bills have been added to the system
- **Payroll Integration**: View and create recurring expenses from Xero payroll data
  - **Automatic Data Retrieval**: System automatically displays your most recent payroll data
  - **Detailed Breakdown**: See net pay, superannuation, tax, and reimbursements
  - **One-Click Template**: Create recurring monthly payroll expenses with a single click
  - **Automatic Calculations**: Total cost to business calculated from all components
- **GST Integration**: Track and forecast GST payments from Xero
  - **Profit and Loss Integration**: Calculates GST as 10% of total sales from Xero Profit and Loss report
  - **Smart Prediction**: Intelligently projects final GST amount based on current quarter data
  - **Quarterly Due Dates**: Automatically calculates the correct quarterly payment due dates (28th of month following quarter)
  - **One-Click Sync**: Add GST payments to your expenses with a single click
- **OAuth Authentication**: Secure connection to your Xero account
  - **Dedicated Auth Screen**: Clean authentication experience before accessing the dashboard
  - **Logout Functionality**: Easily disconnect from your Xero account when needed
- **Robust Error Handling**: Graceful degradation with fallbacks when API calls fail

### CRM & Deal Management

The CRM feature provides a comprehensive system for managing deals, contacts, and companies:

- **Deal Kanban Board**: Visualize deals by stage with drag-and-drop functionality
- **Comprehensive Deal Editing**: Dedicated page for managing all aspects of a deal
  - **Custom Fields**: Add, edit, and remove custom fields that are preserved during HubSpot imports
  - **Contact Management**: Associate contacts with deals and manage relationships
  - **Estimate Linking**: Connect deals to both Upstream draft estimates and Harvest published estimates
  - **Notes & Activity**: Track all activities related to a deal
- **Client Radar**: Strategic view of client portfolio with enhanced company cards showing:
  - Company name with colored avatar and industry tags
  - Last interaction dates and active deal pipeline information
  - **Calculated total spend** (manual entries + Harvest invoices) with auto-refresh
  - Quick website access and drag-and-drop positioning across strategic quadrants
  - **Performance-optimized spend data** via Harvest invoice cache with 6-hour TTL
- **HubSpot Integration**: Import deals, contacts, and companies from HubSpot with real-time progress tracking
- **Data Management Interface**: Comprehensive system for managing external integrations:
  - Company linking status overview with visual indicators
  - **Direct HubSpot API integration** for real-time company linking (no complex filtering)
  - Link/unlink companies between HubSpot and Harvest systems with reliable relinking
  - Bulk operations and relationship management
- **Deal Pipeline Reports**: Visualize deal pipeline with weighted values based on probability
- **Compact View Option**: Toggle between normal and compact views for the Kanban board

### Transaction Breakdown

The transaction breakdown provides a detailed view of all upcoming financial events:

- **Comprehensive Listing**: All transactions in chronological order with cash flow direction icons
- **Color-coded Types**: Easy identification of transaction types with consistent color scheme
- **Interactive Highlighting**: Hover over chart points to see and highlight related transactions
- **Detailed Information**: Cash flow direction, ID, date, description, amount, and source
- **Percentage Change**: Shows percentage change in running balance for each transaction
- **Inline Expense Management**: Add and edit expenses directly in the table

## Development

For detailed development information, refer to [DEVELOPMENT-GUIDE.md](DEVELOPMENT-GUIDE.md).

For a complete history of changes, see [CHANGELOG.md](CHANGELOG.md).

### Key Development Commands

- `npm run dev`: Start frontend and backend concurrently
- `npm run build`: Build full project using the production build script
- `npm run lint`: Run ESLint on src directory
- `npm test`: Run all unit tests with Jest
- `npm run test:e2e`: Run end-to-end tests with Playwright
- `npm run deploy:preview`: Deploy to preview environment
- `npm run deploy:production`: Deploy to production environment

### Database Migrations

The application uses a simple migration system to manage database schema changes:

- Migrations run automatically when the server starts
- To run migrations manually: `node scripts/run-migrations.js`
- To revert a specific migration: `node scripts/run-migrations.js revert <migration-name>`

Migration files are stored in `src/api/migrations/` and follow a consistent pattern with `up()` and `down()` functions.

## Deployment

This project uses a simplified deployment process with pre-built branches for Render.

For detailed deployment instructions, see the [Deployment Guide v2](docs/DEPLOYMENT-V2.md).

Key deployment commands:

```bash
# Deploy to preview (staging) environment
npm run deploy:preview

# Deploy to production environment
npm run deploy:production
```

The deployment script handles:

- Building the application with TypeScript compilation
- Bundling the frontend with Vite
- Copying essential database scripts
- Creating a minimal deployment package
- Pushing to pre-built branches that trigger Render deployments

### Standard Render Deployment

This application is also configured for standard deployment on Render, which provides a simple deployment process with built-in persistent storage for the SQLite database.

1. Push your repository to GitHub.
2. Create an account on [Render](https://render.com).
3. In the Render dashboard, click "New +" and select "Blueprint".
4. Connect your GitHub repository and select it.
5. Render will automatically detect the `render.yaml` configuration file and set up your service.
6. Enter values for environment variables marked with `sync: false` in the render.yaml file.
7. Click "Apply" to start the deployment process.
8. Once the deployment is complete, Render will provide a URL for your application.
9. Update your Xero developer portal with the correct callback URL.

### Key Features of the Render Deployment

1. **Persistent Storage**: 1GB persistent disk mounted at `/data` to store the SQLite database.
2. **Automatic Deployments**: Changes pushed to your main branch automatically trigger a new deployment.
3. **Environment Variables**: Securely stored and managed through the Render dashboard.
4. **SSL/TLS**: Automatic SSL certificates for your application.

## Troubleshooting

### Port Conflicts

If you encounter "Port already in use" errors during development:

1. Run the kill-ports script: `./kill-ports.sh`
2. Manually kill processes using the required ports: `lsof -ti:3002,5173 | xargs kill -9`

### Authentication Issues

If you're having Xero authentication problems:

1. Ensure the application is running on the correct ports
2. Verify your Xero client ID and client secret
3. Make sure the callback URL in the Xero Developer Portal exactly matches your environment
4. Try re-authenticating if you see "Token Refresh Failed" errors
5. Use the Logout button to force re-authentication if needed

### API Rate Limiting

The application includes robust handling for API rate limits with graceful degradation, returning partial data when throttled instead of crashing.

## Recent Updates

For detailed information about recent fixes and development progress, see [CHANGELOG.md](CHANGELOG.md).

## License

This project is proprietary software owned by Onbord Digital Consulting.

## Contact

For questions or support, contact <<EMAIL>>
