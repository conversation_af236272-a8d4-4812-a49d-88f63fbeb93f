# Upstream Financial Dashboard - Notion Documentation Structure

This document provides the complete content structure for documenting the Upstream Financial Dashboard project in Notion.

## 📋 Notion Documentation Structure

### 1. Main Project Page
### 2. Technical Documentation
### 3. User Guide Pages
### 4. Development Guide
### 5. API Reference

---

## 1. MAIN PROJECT PAGE CONTENT

# 🏦 Upstream Financial Dashboard

**A comprehensive financial management platform for agencies and service businesses**

## 📋 Project Overview

Upstream is a web application for financial planning, cashflow projection, and project estimation, integrating with Xero, Harvest, and HubSpot. It provides real-time visibility into current financial position while offering powerful forecasting tools to help make informed business decisions.

### 🎯 Key Features

- **Smart Forecast System** - Automated financial projections based on project budgets
- **Cashflow Projection** - Visual day-by-day forecast of bank balance
- **Deal & Estimate Management** - CRM integration with HubSpot
- **Expense Tracking** - Custom expenses and Xero integration
- **Staff Utilization Reports** - Project and team performance insights
- **Xero MCP Integration** - Natural language interface to accounting data

### 🏗️ Technology Stack

**Frontend:**
- React (TypeScript)
- Tailwind CSS
- React Query
- Recharts
- Vite

**Backend:**
- Node.js with Express
- SQLite (better-sqlite3)
- OAuth2 authentication

**Integrations:**
- Xero API (accounting data)
- Harvest API (project management)
- HubSpot API (CRM data)

### 🚀 Deployment

- **Production**: https://upstream.onbord.au
- **Platform**: Render.com
- **Database**: SQLite with persistent storage

## 🔧 Core Components

### Smart Forecast System
Automates financial projections by analyzing:
- Project budgets from Harvest
- Uninvoiced work completed
- Outstanding invoices
- Project-specific payment terms

### Cashflow Projection
- Interactive charts with 30/60/90 day views
- Color-coded transaction events
- Minimum balance thresholds
- Scenario comparison (optimistic/realistic/pessimistic)

### CRM Integration
- HubSpot deal synchronization
- Deal-estimate linking
- Pipeline value calculations
- Weighted deal amounts

### Expense Management
- Custom expense creation
- Xero expense synchronization
- Recurring expense tracking
- Tax payment scheduling

## 📊 Business Logic

### Australian Financial Year
- Q1: Jul-Sep, Q2: Oct-Dec, Q3: Jan-Mar, Q4: Apr-Jun
- Tax payments: 21st of following month
- BAS payments: 26th of month following quarter end

### Deal Management
- One Upstream Estimate per deal
- One Harvest Estimate per deal
- Probability-based weighted calculations

### Staff Costing
- Non-contractor staff: Fixed weekly capacity rate (37.5 hours max)
- Contractor staff: Actual hours worked

## 🔄 Development Workflow

### Branch Strategy
- `main` - Production branch
- `preview` - Staging/preview branch
- Feature branches from `preview`

### Key Commands
```bash
npm run dev              # Start frontend and backend
npm run build           # Production build
npm run test            # Unit tests
npm run test:e2e        # End-to-end tests
npm run deploy:preview  # Deploy to preview
npm run deploy:production # Deploy to production
```

### Testing Strategy
- Unit tests with Jest
- Integration tests
- End-to-end tests with Playwright
- Real data API connection tests

## 📁 Project Structure

```
src/
├── frontend/           # React application
│   ├── components/     # React components
│   ├── contexts/       # React contexts
│   ├── hooks/         # Custom hooks
│   └── styles/        # CSS files
├── api/               # Express backend
│   ├── routes/        # API endpoints
│   ├── repositories/  # Data access layer
│   └── services/      # Business logic
└── types/             # Shared TypeScript types

docs/                  # Documentation
├── user-guide/        # User documentation
├── technical/         # Technical documentation
└── api-reference/     # API documentation

tests/                 # Test files
├── unit/             # Unit tests
├── integration/      # Integration tests
└── e2e/              # End-to-end tests
```

## 🔐 Environment Configuration

### Required Environment Variables
- `XERO_CLIENT_ID` - Xero OAuth client ID
- `XERO_CLIENT_SECRET` - Xero OAuth client secret
- `HARVEST_ACCESS_TOKEN` - Harvest API token
- `HUBSPOT_ACCESS_TOKEN` - HubSpot API token
- `SESSION_SECRET` - Session encryption secret

### Database
- SQLite database with better-sqlite3
- Repository pattern for data access
- Unified data model approach

## 📈 Key Metrics & Performance

### Build Targets
- Build time: ≤3.0 seconds
- Bundle size optimization
- Accessibility-first design

### User Base
- Small application (3 users)
- <100 companies in database
- Australian market focus

## 🔗 External Integrations

### Xero Integration
- Bank account data
- Invoice and bill management
- Financial reports
- Real-time synchronization

### Harvest Integration
- Project budgets and timesheets
- Client and project data
- Uninvoiced work tracking
- Estimate management

### HubSpot Integration
- Deal pipeline data
- Contact management
- Custom field mapping
- Probability calculations

## 📋 Current Development Phase

**Phase 2D - Form System Modernization**
- Container queries at 768px breakpoint
- Consistent component variants
- Zero breaking changes approach
- Accessibility improvements

## 🎯 Future Roadmap

- Enhanced reporting capabilities
- Mobile application
- Advanced forecasting algorithms
- Multi-currency support
- Team collaboration features

---

*Last Updated: January 2025*
*Repository: https://github.com/adrian-dotco/onbord-financial-dashboard*

---

## 2. TECHNICAL DOCUMENTATION PAGES

### 2.1 Architecture Overview Page

# 🏗️ Architecture Overview

## System Architecture

Upstream follows a modern web application architecture with clear separation of concerns:

**Frontend Layer**
- React with TypeScript for type safety
- Component-based architecture with hooks
- Tailwind CSS for styling
- React Query for data fetching and caching

**Backend Layer**
- Express.js REST API
- Repository pattern for data access
- Service layer for business logic
- SQLite database with better-sqlite3

**Integration Layer**
- OAuth2 authentication flows
- RESTful API clients for external services
- Real-time data synchronization
- Error handling and retry mechanisms

## Data Flow

1. **User Interaction** → React components
2. **API Calls** → React Query hooks
3. **Backend Processing** → Express routes → Services → Repositories
4. **External APIs** → Xero/Harvest/HubSpot integrations
5. **Database Operations** → SQLite with better-sqlite3
6. **Response Flow** → JSON responses → React Query cache → UI updates

## Security Architecture

- **Authentication**: OAuth2 flows for external services
- **Session Management**: Express sessions with Redis (production)
- **API Security**: CORS configuration, credential handling
- **Environment Variables**: Secure configuration management

### 2.2 Data Model Page

# 🗄️ Data Model

## Database Schema

Upstream uses a unified data model approach with SQLite:

**Core Entities**
- Companies (consolidated from multiple sources)
- Deals (HubSpot integration with strict association validation)
- Estimates (Upstream and Harvest)
- Expenses (Custom and Xero)
- Projects (Harvest integration)
- Users and authentication

**Relationship Models**
- Deal-Estimate linking (1:1 constraints)
- Company-Deal associations (enforced via HubSpot associations API)
- Project-Estimate connections
- User-Company permissions

**HubSpot Integration**
- Association-based import system (no fallback company creation)
- Sequential import order: Companies → Deals → Contacts
- Strict validation: deals without company associations are rejected
- Real relationship preservation from HubSpot

## Repository Pattern

Each entity has a dedicated repository:
- `CompanyRepository` - Company data management
- `DealRepository` - CRM deal operations
- `EstimateRepository` - Estimate CRUD operations
- `ExpenseRepository` - Expense tracking
- `ProjectRepository` - Project data from Harvest

**Base Repository**
Common functionality for all repositories:
- Database connection management
- Transaction handling
- Error logging and handling
- Query optimization

### 2.3 Integration Details Page

# 🔗 Integration Architecture

## Xero Integration

**Authentication Flow**
- OAuth2 authorization code flow
- Token refresh management
- Multi-tenant support

**Data Synchronization**
- Bank accounts and balances
- Invoices and bills
- Payment tracking
- Financial reports

**API Endpoints Used**
- `/Accounts` - Chart of accounts
- `/BankTransactions` - Transaction history
- `/Invoices` - Invoice management
- `/Reports` - Financial reporting

## Harvest Integration

**Authentication**
- Personal access tokens
- Account-level permissions

**Data Sources**
- Project budgets and timesheets
- Client and project metadata
- Uninvoiced work calculations
- Estimate management

## HubSpot Integration

**CRM Data**
- Deal pipeline synchronization
- Contact management
- Custom field mapping
- Probability calculations

**Webhook Support**
- Real-time deal updates
- Status change notifications
- Data consistency maintenance

---

## 3. USER GUIDE PAGES

### 3.1 Getting Started Page

# 🚀 Getting Started with Upstream

## Welcome to Upstream

Upstream is your comprehensive financial management platform designed specifically for agencies and service businesses. This guide will help you get started quickly.

## First Steps

### 1. Authentication Setup
- Access the application at https://upstream.onbord.au
- Complete OAuth authentication for Xero
- Configure Harvest integration
- Set up HubSpot connection (optional)

### 2. Initial Configuration
- Review company settings
- Configure minimum balance thresholds
- Set up project payment terms
- Add custom recurring expenses

### 3. Navigation Overview
- **Dashboard** - Financial overview and key metrics
- **Cashflow** - Projection charts and transaction lists
- **CRM** - Deal management and pipeline tracking
- **Estimates** - Project estimation and management
- **Expenses** - Custom and Xero expense tracking
- **Reports** - Staff utilization and financial reports

## Key Features Overview

### Smart Forecast System
Automatically generates income projections based on:
- Remaining project budgets from Harvest
- Uninvoiced work already completed
- Outstanding invoices and payment terms
- Historical payment patterns

### Cashflow Projection
Visual representation of your financial future:
- Interactive charts with hover details
- Configurable timeframes (30/60/90 days)
- Scenario comparison capabilities
- Transaction filtering and audit trails

### 3.2 Cashflow Feature Page

# 📊 Cashflow Projection Guide

## Overview

The cashflow projection is the heart of Upstream, providing a visual forecast of your bank balance over time. It combines actual data from Xero with intelligent projections from Harvest projects.

## Using the Cashflow Chart

### Chart Elements
- **Blue line**: Your projected daily bank balance
- **Green dots**: Income transactions
- **Red dots**: Expense transactions
- **Orange dots**: Mixed transaction days
- **Red horizontal line**: Minimum balance threshold

### Interactive Features
- **Hover tooltips**: Detailed transaction information
- **Timeframe selector**: 30, 60, or 90-day views
- **Scenario toggle**: Optimistic/Realistic/Pessimistic projections
- **Transaction highlighting**: Click chart points to highlight related transactions

## Smart Forecast Configuration

### Project Settings
For each active project, configure:

**Invoice Frequency**
- Weekly: Invoices sent every week
- Bi-weekly: Invoices sent every two weeks
- Monthly: Invoices sent monthly

**Payment Terms**
- 7 days: Payment expected within a week
- 14 days: Payment expected within two weeks
- 20 days: Payment expected within 20 days
- 30 days: Payment expected within a month

**Include in Projections**
Toggle whether the project should be included in cashflow forecasts.

### Projection Rules

The Smart Forecast system applies intelligent filtering:

1. **Uninvoiced Work Rule**: Excludes uninvoiced work when projected income exists within ±3 days
2. **Real Invoice Rule**: Excludes projected income when actual invoices exist within ±5 days
3. **Payment Terms Rule**: Excludes projected income if within payment terms from today

## Transaction List

Below the chart, review all transactions:
- **Source indicators**: Xero, Harvest, Smart Forecast, Custom
- **Direction arrows**: Green (income) or Red (expense)
- **Running balance**: Percentage change impact
- **Filtering options**: By source, date range, or amount

## Projection Audit

Access the "View Projection Audit" to understand filtering decisions:
- See which projections were included/excluded
- Understand the reasoning behind each decision
- Review related invoice information
- Verify projection accuracy

### 3.3 CRM Feature Page

# 👥 CRM & Deal Management

## Deal Pipeline Overview

Upstream integrates with HubSpot to provide comprehensive deal tracking and pipeline management.

## Deal Management Features

### Pipeline Visualization
- Deal stages mapped from HubSpot
- Probability-based weighting
- Weighted deal amount calculations
- Pipeline value summaries

### Deal-Estimate Integration
- Link deals to Upstream estimates (1:1)
- Link deals to Harvest estimates (1:1)
- Automatic value synchronization
- Estimate-controlled field locking

### Deal Information
Each deal displays:
- Company and contact details
- Deal stage and probability
- Estimated close date
- Deal value and weighted amount
- Associated estimates
- Recent activity timeline

## Working with Estimates

### Creating Estimates
1. Navigate to the Estimates section
2. Click "Create New Estimate"
3. Fill in project details and line items
4. Set payment terms and schedule
5. Link to relevant deal (optional)

### Estimate-Deal Linking
- One Upstream estimate per deal maximum
- One Harvest estimate per deal maximum
- Linked estimates control deal values
- Prevents manual editing of estimate-controlled fields

### Estimate Status Tracking
- Draft: Initial creation phase
- Sent: Delivered to client
- Accepted: Client approval received
- Rejected: Client declined
- Expired: Past validity date

## Pipeline Reporting

### Key Metrics
- Total pipeline value
- Weighted pipeline amount
- Average deal size
- Win rate by stage
- Sales velocity metrics

### Filtering Options
- By deal stage
- By probability range
- By close date
- By assigned owner
- By company size

---

## 4. DEVELOPMENT GUIDE PAGES

### 4.1 Development Setup Page

# 💻 Development Setup

## Prerequisites

- Node.js (v18 or higher)
- npm or yarn package manager
- Git for version control
- SQLite3 for database

## Local Development Setup

### 1. Clone Repository
```bash
git clone https://github.com/adrian-dotco/onbord-financial-dashboard.git
cd onbord-financial-dashboard
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create `.env` file with required variables:
```env
XERO_CLIENT_ID=your_xero_client_id
XERO_CLIENT_SECRET=your_xero_client_secret
HARVEST_ACCESS_TOKEN=your_harvest_token
HUBSPOT_ACCESS_TOKEN=your_hubspot_token
SESSION_SECRET=your_session_secret
```

### 4. Database Setup
```bash
npm run db:init
npm run db:migrate
```

### 5. Start Development Server
```bash
npm run dev
```

This starts both frontend (port 5173) and backend (port 3002) concurrently.

## Development Workflow

### Branch Strategy
- `main`: Production-ready code
- `preview`: Staging environment
- Feature branches: Created from `preview`

### Pull Request Process
1. Create feature branch from `preview`
2. Implement changes with tests
3. Submit PR to `preview` (not `main`)
4. Ensure all tests pass
5. Get code review approval
6. Merge to `preview`

### Code Style Guidelines

**TypeScript**
- Strict type checking enabled
- Interface definitions in dedicated files
- Proper error handling with try/catch

**React Components**
- Functional components with hooks
- Props interfaces defined
- Proper component composition

**Backend**
- Repository pattern for data access
- Service layer for business logic
- Proper error handling and logging

### 4.2 Testing Guide Page

# 🧪 Testing Strategy

## Test Types

### Unit Tests
- Component testing with React Testing Library
- Service layer testing
- Repository testing with mock data
- Utility function testing

### Integration Tests
- API endpoint testing
- Database integration testing
- External service integration testing
- End-to-end workflow testing

### End-to-End Tests
- User journey testing with Playwright
- Cross-browser compatibility
- Mobile responsiveness testing
- Performance testing

## Running Tests

### All Tests
```bash
npm test
```

### Unit Tests Only
```bash
npm run test:unit
```

### Integration Tests
```bash
npm run test:integration
```

### E2E Tests
```bash
npm run test:e2e
npm run test:e2e:ui      # With UI
npm run test:e2e:headed  # With browser
```

### Real Data Tests
```bash
npm run test:real-data
npm run test:api-check
```

## Test Structure

### Unit Test Example
```typescript
describe('CashflowService', () => {
  it('should calculate projected balance correctly', () => {
    // Test implementation
  });
});
```

### Integration Test Example
```typescript
describe('API Integration', () => {
  it('should sync Xero data successfully', async () => {
    // Test implementation
  });
});
```

### E2E Test Example
```typescript
test('user can view cashflow projection', async ({ page }) => {
  // Test implementation
});
```

---

## 5. API REFERENCE PAGES

### 5.1 Xero API Reference Page

# 🔗 Xero API Integration

## Authentication

Upstream uses OAuth2 authorization code flow for Xero integration.

### OAuth Flow
1. User initiates connection
2. Redirect to Xero authorization
3. Authorization code exchange
4. Token storage and refresh

### Token Management
- Access tokens: 30-minute expiry
- Refresh tokens: 60-day expiry
- Automatic token refresh
- Error handling for expired tokens

## API Endpoints Used

### Accounts API
**Endpoint**: `/Accounts`
**Purpose**: Chart of accounts and bank account information
**Data Retrieved**:
- Account codes and names
- Account types and balances
- Bank account details

### Bank Transactions API
**Endpoint**: `/BankTransactions`
**Purpose**: Transaction history and bank feeds
**Data Retrieved**:
- Transaction dates and amounts
- Reference information
- Bank account associations

### Invoices API
**Endpoint**: `/Invoices`
**Purpose**: Invoice management and tracking
**Data Retrieved**:
- Invoice details and line items
- Payment status and dates
- Customer information

### Reports API
**Endpoint**: `/Reports`
**Purpose**: Financial reporting data
**Data Retrieved**:
- Profit and loss statements
- Balance sheet information
- Cash flow reports

## Rate Limiting

- 60 calls per minute per organization
- 5000 calls per day per organization
- Automatic retry with exponential backoff
- Rate limit monitoring and alerting

## Error Handling

### Common Error Codes
- `401`: Unauthorized (token expired)
- `403`: Forbidden (insufficient permissions)
- `429`: Rate limit exceeded
- `500`: Xero server error

### Retry Strategy
- Exponential backoff for rate limits
- Token refresh for 401 errors
- Circuit breaker for persistent failures
- Comprehensive error logging

### 5.2 Harvest API Reference Page

# 🌾 Harvest API Integration

## Authentication

Harvest integration uses personal access tokens for API authentication.

### Token Configuration
- Account ID required
- Personal access token
- User-Agent header specification
- Rate limit compliance

## API Endpoints Used

### Projects API
**Endpoint**: `/projects`
**Purpose**: Project information and budgets
**Data Retrieved**:
- Project names and codes
- Budget amounts and spent amounts
- Project status and dates
- Client associations

### Time Entries API
**Endpoint**: `/time_entries`
**Purpose**: Timesheet data and uninvoiced work
**Data Retrieved**:
- Time entry details
- Task and project associations
- Billable status
- Invoice associations

### Invoices API
**Endpoint**: `/invoices`
**Purpose**: Invoice tracking and management
**Data Retrieved**:
- Invoice amounts and dates
- Payment status
- Project associations
- Line item details

### Estimates API
**Endpoint**: `/estimates`
**Purpose**: Project estimates and proposals
**Data Retrieved**:
- Estimate amounts and dates
- Acceptance status
- Project associations
- Line item breakdown

## Data Synchronization

### Sync Frequency
- Real-time for critical data
- Hourly for project updates
- Daily for historical data
- Manual refresh available

### Sync Strategy
- Incremental updates based on timestamps
- Full sync for data integrity checks
- Conflict resolution for concurrent updates
- Sync status monitoring

## Rate Limiting

- 100 requests per 15 seconds
- Automatic throttling
- Request queuing for burst handling
- Rate limit monitoring

### 5.3 HubSpot API Reference Page

# 🎯 HubSpot API Integration

## Authentication

HubSpot integration uses private app access tokens.

### Token Configuration
- Private app token
- Required scopes configuration
- API version specification
- Rate limit compliance

## API Endpoints Used

### Deals API
**Endpoint**: `/crm/v3/objects/deals`
**Purpose**: Deal pipeline management
**Data Retrieved**:
- Deal names and amounts
- Pipeline stages
- Close dates and probabilities
- Associated companies and contacts

### Companies API
**Endpoint**: `/crm/v3/objects/companies`
**Purpose**: Company information
**Data Retrieved**:
- Company names and details
- Industry and size information
- Contact associations
- Custom properties

### Custom Properties API
**Endpoint**: `/crm/v3/properties/deals`
**Purpose**: Custom field definitions
**Data Retrieved**:
- Property names and types
- Option values for dropdowns
- Field validation rules
- Property groups

## Webhook Integration

### Webhook Events
- Deal property changes
- Deal stage updates
- Company updates
- Contact associations

### Webhook Processing
- Event validation and verification
- Asynchronous processing
- Error handling and retry
- Event deduplication

## Rate Limiting

- 100 requests per 10 seconds
- Daily limits based on subscription
- Automatic retry with backoff
- Rate limit monitoring

---

## 📝 Implementation Instructions

To implement this documentation in Notion:

1. **Create Main Project Page**: Use the content from section 1
2. **Create Technical Documentation Section**: Add pages from section 2
3. **Create User Guide Section**: Add pages from section 3
4. **Create Development Guide Section**: Add pages from section 4
5. **Create API Reference Section**: Add pages from section 5

### Notion Page Structure
```
🏦 Upstream Financial Dashboard (Main Page)
├── 🏗️ Technical Documentation
│   ├── Architecture Overview
│   ├── Data Model
│   └── Integration Details
├── 📚 User Guide
│   ├── Getting Started
│   ├── Cashflow Projection
│   └── CRM & Deal Management
├── 💻 Development Guide
│   ├── Development Setup
│   └── Testing Strategy
└── 🔗 API Reference
    ├── Xero API
    ├── Harvest API
    └── HubSpot API
```

### Additional Notion Features to Use
- **Database views** for project tracking
- **Templates** for consistent documentation
- **Callout blocks** for important information
- **Code blocks** for technical examples
- **Toggle lists** for detailed sections
- **Linked databases** for cross-references
