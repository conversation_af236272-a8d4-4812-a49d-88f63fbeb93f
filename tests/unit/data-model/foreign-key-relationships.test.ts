/**
 * Foreign Key Relationship Tests
 * 
 * These tests specifically verify the integrity of foreign key relationships in the database schema.
 * This is crucial for maintaining data integrity across tables.
 */

import { DBSchemaValidator, createTestDatabase } from './db-schema-validator';
import { initializeSchema } from '../../../src/database/schema';
import BetterSqlite3 from 'better-sqlite3';

describe('Foreign Key Relationship Tests', () => {
  let validator: DBSchemaValidator;
  
  beforeAll(() => {
    // Create an in-memory database for testing
    const db = new BetterSqlite3(':memory:');
    
    // Enable foreign key constraints
    db.pragma('foreign_keys = ON');
    
    // Initialize the schema
    initializeSchema(db);
    
    // Create a validator using the initialized database
    validator = new DBSchemaValidator();
    validator.runSchemaScript(db.serialize());
  });
  
  afterAll(() => {
    validator.close();
  });
  
  describe('CRM Relationships', () => {
    it('should have correct foreign key from contact to company', () => {
      expect(validator.foreignKeyExists(
        'contact',
        'company_id',
        'company',
        'id'
      )).toBe(true);
    });
    
    it('should have correct foreign keys in deal_estimate junction table', () => {
      expect(validator.foreignKeyExists(
        'deal_estimate',
        'deal_id',
        'deal',
        'id'
      )).toBe(true);
      
      expect(validator.foreignKeyExists(
        'deal_estimate',
        'estimate_id',
        'estimate',
        'id'
      )).toBe(true);
    });
    
    it('should have correct foreign key from note to deal', () => {
      expect(validator.foreignKeyExists(
        'note',
        'deal_id',
        'deal',
        'id'
      )).toBe(true);
    });
    
    it('should handle deal_contact or contact_role table correctly', () => {
      // Check if contact_role table exists (new schema)
      const contactRoleExists = validator.tableExists('contact_role');
      
      if (contactRoleExists) {
        // Test foreign keys for contact_role
        expect(validator.foreignKeyExists(
          'contact_role',
          'deal_id',
          'deal',
          'id'
        )).toBe(true);
        
        expect(validator.foreignKeyExists(
          'contact_role',
          'contact_id',
          'contact',
          'id'
        )).toBe(true);
      } else {
        // Test foreign keys for deal_contact (old schema)
        expect(validator.foreignKeyExists(
          'deal_contact',
          'deal_id',
          'deal',
          'id'
        )).toBe(true);
        
        expect(validator.foreignKeyExists(
          'deal_contact',
          'contact_id',
          'contact',
          'id'
        )).toBe(true);
      }
    });
  });
  
  describe('Advanced Relationship Tables', () => {
    it('should have correct foreign keys in company_relationship table if it exists', () => {
      const companyRelationshipExists = validator.tableExists('company_relationship');
      
      if (companyRelationshipExists) {
        expect(validator.foreignKeyExists(
          'company_relationship',
          'parent_company_id',
          'company',
          'id'
        )).toBe(true);
        
        expect(validator.foreignKeyExists(
          'company_relationship',
          'child_company_id',
          'company',
          'id'
        )).toBe(true);
      } else {
        console.log('company_relationship table not found, skipping test');
      }
    });
    
    it('should have correct foreign keys in contact_company table if it exists', () => {
      const contactCompanyExists = validator.tableExists('contact_company');
      
      if (contactCompanyExists) {
        expect(validator.foreignKeyExists(
          'contact_company',
          'contact_id',
          'contact',
          'id'
        )).toBe(true);
        
        expect(validator.foreignKeyExists(
          'contact_company',
          'company_id',
          'company',
          'id'
        )).toBe(true);
      } else {
        console.log('contact_company table not found, skipping test');
      }
    });
  });
  
  describe('Data Integrity Constraints', () => {
    it('should verify ON DELETE CASCADE constraints', () => {
      /**
       * This test checks for ON DELETE CASCADE constraints
       * which are important for maintaining data integrity when
       * parent records are deleted
       */
      
      // deal_estimate should cascade on deal and estimate deletion
      const dealEstimateFK1 = validator.getForeignKeys('deal_estimate')
        .find(fk => fk.from === 'deal_id' && fk.table === 'deal');
      expect(dealEstimateFK1?.on_delete).toBe('CASCADE');
      
      const dealEstimateFK2 = validator.getForeignKeys('deal_estimate')
        .find(fk => fk.from === 'estimate_id' && fk.table === 'estimate');
      expect(dealEstimateFK2?.on_delete).toBe('CASCADE');
      
      // note should cascade on deal deletion
      const noteFK = validator.getForeignKeys('note')
        .find(fk => fk.from === 'deal_id' && fk.table === 'deal');
      expect(noteFK?.on_delete).toBe('CASCADE');
      
      // Check deal_contact or contact_role
      if (validator.tableExists('contact_role')) {
        const contactRoleFK1 = validator.getForeignKeys('contact_role')
          .find(fk => fk.from === 'deal_id' && fk.table === 'deal');
        expect(contactRoleFK1?.on_delete).toBe('CASCADE');
        
        const contactRoleFK2 = validator.getForeignKeys('contact_role')
          .find(fk => fk.from === 'contact_id' && fk.table === 'contact');
        expect(contactRoleFK2?.on_delete).toBe('CASCADE');
      } else if (validator.tableExists('deal_contact')) {
        const dealContactFK1 = validator.getForeignKeys('deal_contact')
          .find(fk => fk.from === 'deal_id' && fk.table === 'deal');
        expect(dealContactFK1?.on_delete).toBe('CASCADE');
        
        const dealContactFK2 = validator.getForeignKeys('deal_contact')
          .find(fk => fk.from === 'contact_id' && fk.table === 'contact');
        expect(dealContactFK2?.on_delete).toBe('CASCADE');
      }
    });
    
    it('should verify ON DELETE SET NULL constraints', () => {
      /**
       * This test checks for ON DELETE SET NULL constraints
       * which are important for gracefully handling deletion of
       * referenced records while preserving the referencing record
       */
      
      // contact.company_id should be set to NULL when company is deleted
      const contactFK = validator.getForeignKeys('contact')
        .find(fk => fk.from === 'company_id' && fk.table === 'company');
      expect(contactFK?.on_delete).toBe('SET NULL');
    });
    
    it('should verify relationship junction tables have proper constraints', () => {
      // Check for company_relationship if it exists
      if (validator.tableExists('company_relationship')) {
        const companyRelationshipFK1 = validator.getForeignKeys('company_relationship')
          .find(fk => fk.from === 'parent_company_id' && fk.table === 'company');
        expect(companyRelationshipFK1?.on_delete).toBe('CASCADE');
        
        const companyRelationshipFK2 = validator.getForeignKeys('company_relationship')
          .find(fk => fk.from === 'child_company_id' && fk.table === 'company');
        expect(companyRelationshipFK2?.on_delete).toBe('CASCADE');
      }
      
      // Check for contact_company if it exists
      if (validator.tableExists('contact_company')) {
        const contactCompanyFK1 = validator.getForeignKeys('contact_company')
          .find(fk => fk.from === 'contact_id' && fk.table === 'contact');
        expect(contactCompanyFK1?.on_delete).toBe('CASCADE');
        
        const contactCompanyFK2 = validator.getForeignKeys('contact_company')
          .find(fk => fk.from === 'company_id' && fk.table === 'company');
        expect(contactCompanyFK2?.on_delete).toBe('CASCADE');
      }
    });
  });
  
  describe('Relationship Cascade Tests (Data Operations)', () => {
    let db: BetterSqlite3.Database;
    
    beforeAll(() => {
      // Create a new in-memory database for these tests specifically
      db = new BetterSqlite3(':memory:');
      db.pragma('foreign_keys = ON'); // Enable foreign key constraints
      
      // Initialize the schema
      initializeSchema(db);
      
      // Insert test data
      const now = new Date().toISOString();
      
      // Insert a company
      db.prepare(`
        INSERT INTO company (
          id, name, created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        'company-1',
        'Test Company',
        now,
        now,
        'system',
        'system'
      );
      
      // Insert a contact linked to the company
      db.prepare(`
        INSERT INTO contact (
          id, first_name, last_name, email, company_id, created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        'contact-1',
        'John',
        'Doe',
        '<EMAIL>',
        'company-1',
        now,
        now,
        'system',
        'system'
      );
      
      // Insert a deal
      db.prepare(`
        INSERT INTO deal (
          id, name, stage, created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        'deal-1',
        'Test Deal',
        'discovery',
        now,
        now,
        'system',
        'system'
      );
      
      // Insert an estimate (required for deal_estimate test)
      db.prepare(`
        INSERT INTO estimate (
          id, name, total_amount, created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        'estimate-1',
        'Test Estimate',
        10000,
        now,
        now,
        'system',
        'system'
      );
      
      // Insert a deal_estimate relationship
      db.prepare(`
        INSERT INTO deal_estimate (
          id, deal_id, estimate_id, estimate_type, linked_at, linked_by
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        'deal-estimate-1',
        'deal-1',
        'estimate-1',
        'internal',
        now,
        'system'
      );
      
      // Insert a note for the deal
      db.prepare(`
        INSERT INTO note (
          id, deal_id, content, created_at, created_by
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        'note-1',
        'deal-1',
        'Test note',
        now,
        'system'
      );
      
      // Check if we're using contact_role or deal_contact
      const contactRoleExists = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='contact_role'
      `).get();
      
      if (contactRoleExists) {
        // Insert a contact_role relationship
        db.prepare(`
          INSERT INTO contact_role (
            deal_id, contact_id, role, created_at, created_by
          ) VALUES (?, ?, ?, ?, ?)
        `).run(
          'deal-1',
          'contact-1',
          'decision_maker',
          now,
          'system'
        );
      } else {
        // Insert a deal_contact relationship
        db.prepare(`
          INSERT INTO deal_contact (
            deal_id, contact_id, role
          ) VALUES (?, ?, ?)
        `).run(
          'deal-1',
          'contact-1',
          'decision_maker'
        );
      }
    });
    
    afterAll(() => {
      db.close();
    });
    
    it('should set company_id to NULL when a company is deleted', () => {
      // Delete the company
      db.prepare(`DELETE FROM company WHERE id = ?`).run('company-1');
      
      // Check if contact.company_id is set to NULL
      const contact = db.prepare(`SELECT * FROM contact WHERE id = ?`).get('contact-1');
      expect(contact).toBeDefined();
      expect(contact.company_id).toBeNull();
    });
    
    it('should cascade delete note when a deal is deleted', () => {
      // Verify the note exists
      const noteExists = db.prepare(`SELECT * FROM note WHERE id = ?`).get('note-1');
      expect(noteExists).toBeDefined();
      
      // Delete the deal
      db.prepare(`DELETE FROM deal WHERE id = ?`).run('deal-1');
      
      // Check if the note was deleted
      const noteDeleted = db.prepare(`SELECT * FROM note WHERE id = ?`).get('note-1');
      expect(noteDeleted).toBeUndefined();
    });
    
    it('should cascade delete deal_estimate when a deal is deleted', () => {
      // We need to insert a new deal and deal_estimate since the previous deal was deleted
      const now = new Date().toISOString();
      
      // Insert a new deal
      db.prepare(`
        INSERT INTO deal (
          id, name, stage, created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        'deal-2',
        'Test Deal 2',
        'discovery',
        now,
        now,
        'system',
        'system'
      );
      
      // Insert a new deal_estimate relationship
      db.prepare(`
        INSERT INTO deal_estimate (
          id, deal_id, estimate_id, estimate_type, linked_at, linked_by
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        'deal-estimate-2',
        'deal-2',
        'estimate-1',
        'internal',
        now,
        'system'
      );
      
      // Verify the deal_estimate exists
      const dealEstimateExists = db.prepare(`SELECT * FROM deal_estimate WHERE id = ?`).get('deal-estimate-2');
      expect(dealEstimateExists).toBeDefined();
      
      // Delete the deal
      db.prepare(`DELETE FROM deal WHERE id = ?`).run('deal-2');
      
      // Check if the deal_estimate was deleted
      const dealEstimateDeleted = db.prepare(`SELECT * FROM deal_estimate WHERE id = ?`).get('deal-estimate-2');
      expect(dealEstimateDeleted).toBeUndefined();
    });
  });
});