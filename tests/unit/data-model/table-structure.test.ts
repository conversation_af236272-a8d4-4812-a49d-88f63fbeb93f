/**
 * Table Structure Validation Tests
 * 
 * These tests validate the database table structures to ensure they match the expected schema.
 * This helps catch issues when tables or columns are renamed, added, or removed.
 */

import { DBSchemaValidator, createTestDatabase } from './db-schema-validator';
import { initializeSchema } from '../../../src/database/schema';
import BetterSqlite3 from 'better-sqlite3';
import path from 'path';

describe('Database Table Structure Validation', () => {
  let validator: DBSchemaValidator;
  
  beforeAll(() => {
    // Create an in-memory database for testing
    const db = new BetterSqlite3(':memory:');
    
    // Initialize the schema
    initializeSchema(db);
    
    // Create a validator using the initialized database
    validator = new DBSchemaValidator();
    validator.runSchemaScript(db.serialize());
  });
  
  afterAll(() => {
    validator.close();
  });
  
  describe('Core Tables', () => {
    it('should have schema_version table', () => {
      expect(validator.tableExists('schema_version')).toBe(true);
      
      const validation = validator.validateRequiredColumns('schema_version', [
        'version',
        'applied_at',
        'description'
      ]);
      
      expect(validation.valid).toBe(true);
      expect(validation.missing).toHaveLength(0);
      
      expect(validator.validatePrimaryKey('schema_version', 'version')).toBe(true);
      expect(validator.validateNotNullConstraint('schema_version', 'applied_at', true)).toBe(true);
      expect(validator.validateNotNullConstraint('schema_version', 'description', true)).toBe(true);
    });
  });
  
  describe('CRM Tables', () => {
    it('should have deal table with correct structure', () => {
      expect(validator.tableExists('deal')).toBe(true);
      
      const requiredColumns = [
        'id',
        'name',
        'stage',
        'value',
        'currency',
        'probability',
        'expected_close_date',
        'start_date',
        'end_date',
        'invoice_frequency',
        'payment_terms',
        'description',
        'source',
        'priority',
        'owner',
        'hubspot_id',
        'include_in_projections',
        'custom_fields',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by'
      ];
      
      const validation = validator.validateRequiredColumns('deal', requiredColumns);
      expect(validation.valid).toBe(true);
      expect(validation.missing).toHaveLength(0);
      
      expect(validator.validatePrimaryKey('deal', 'id')).toBe(true);
      expect(validator.validateNotNullConstraint('deal', 'name', true)).toBe(true);
      expect(validator.validateNotNullConstraint('deal', 'stage', true)).toBe(true);
      
      // Check indexes
      expect(validator.indexExists('deal', 'idx_deal_stage')).toBe(true);
      expect(validator.indexExists('deal', 'idx_deal_dates')).toBe(true);
      expect(validator.indexExists('deal', 'idx_deal_hubspot_id')).toBe(true);
      expect(validator.indexExists('deal', 'idx_deal_projections')).toBe(true);
      expect(validator.indexExists('deal', 'idx_deal_source')).toBe(true);
    });
    
    it('should have contact table with correct structure', () => {
      expect(validator.tableExists('contact')).toBe(true);
      
      const requiredColumns = [
        'id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'job_title',
        'company_id',
        'notes',
        'hubspot_id',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by'
      ];
      
      const validation = validator.validateRequiredColumns('contact', requiredColumns);
      expect(validation.valid).toBe(true);
      expect(validation.missing).toHaveLength(0);
      
      expect(validator.validatePrimaryKey('contact', 'id')).toBe(true);
      expect(validator.validateNotNullConstraint('contact', 'created_at', true)).toBe(true);
      expect(validator.validateNotNullConstraint('contact', 'updated_at', true)).toBe(true);
      
      // Check foreign key
      expect(validator.foreignKeyExists(
        'contact',
        'company_id',
        'company',
        'id'
      )).toBe(true);
      
      // Check indexes
      expect(validator.indexExists('contact', 'idx_contact_name')).toBe(true);
      expect(validator.indexExists('contact', 'idx_contact_email')).toBe(true);
      expect(validator.indexExists('contact', 'idx_contact_company_id')).toBe(true);
      expect(validator.indexExists('contact', 'idx_contact_hubspot_id')).toBe(true);
    });
    
    it('should have company table with correct structure', () => {
      expect(validator.tableExists('company')).toBe(true);
      
      const requiredColumns = [
        'id',
        'name',
        'industry',
        'size',
        'website',
        'address',
        'description',
        'hubspot_id',
        'harvest_id',
        'source',
        'radar_state',
        'priority',
        'current_spend',
        'potential_spend',
        'last_interaction_date',
        'contacts',
        'notes',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at'
      ];
      
      const validation = validator.validateRequiredColumns('company', requiredColumns);
      expect(validation.valid).toBe(true);
      expect(validation.missing).toHaveLength(0);
      
      expect(validator.validatePrimaryKey('company', 'id')).toBe(true);
      expect(validator.validateNotNullConstraint('company', 'name', true)).toBe(true);
      expect(validator.validateNotNullConstraint('company', 'created_at', true)).toBe(true);
      expect(validator.validateNotNullConstraint('company', 'updated_at', true)).toBe(true);
      
      // Check indexes
      expect(validator.indexExists('company', 'idx_company_name')).toBe(true);
      expect(validator.indexExists('company', 'idx_company_hubspot_id')).toBe(true);
      expect(validator.indexExists('company', 'idx_company_harvest_id')).toBe(true);
      expect(validator.indexExists('company', 'idx_company_source')).toBe(true);
      expect(validator.indexExists('company', 'idx_company_radar_state')).toBe(true);
      expect(validator.indexExists('company', 'idx_company_priority')).toBe(true);
      expect(validator.indexExists('company', 'idx_company_deleted_at')).toBe(true);
      expect(validator.indexExists('company', 'idx_company_last_interaction_date')).toBe(true);
    });
    
    it('should have note table with correct structure', () => {
      expect(validator.tableExists('note')).toBe(true);
      
      const requiredColumns = [
        'id',
        'deal_id',
        'content',
        'created_at',
        'created_by'
      ];
      
      const validation = validator.validateRequiredColumns('note', requiredColumns);
      expect(validation.valid).toBe(true);
      expect(validation.missing).toHaveLength(0);
      
      expect(validator.validatePrimaryKey('note', 'id')).toBe(true);
      expect(validator.validateNotNullConstraint('note', 'content', true)).toBe(true);
      expect(validator.validateNotNullConstraint('note', 'created_at', true)).toBe(true);
      
      // Check foreign key
      expect(validator.foreignKeyExists(
        'note',
        'deal_id',
        'deal',
        'id'
      )).toBe(true);
      
      // Check indexes
      expect(validator.indexExists('note', 'idx_note_deal_id')).toBe(true);
      expect(validator.indexExists('note', 'idx_note_created_at')).toBe(true);
    });
  });
  
  describe('Junction Tables', () => {
    it('should have deal_estimate junction table with correct structure', () => {
      expect(validator.tableExists('deal_estimate')).toBe(true);
      
      const requiredColumns = [
        'id',
        'deal_id',
        'estimate_id',
        'estimate_type',
        'linked_at',
        'linked_by'
      ];
      
      const validation = validator.validateRequiredColumns('deal_estimate', requiredColumns);
      expect(validation.valid).toBe(true);
      expect(validation.missing).toHaveLength(0);
      
      expect(validator.validatePrimaryKey('deal_estimate', 'id')).toBe(true);
      expect(validator.validateNotNullConstraint('deal_estimate', 'deal_id', true)).toBe(true);
      expect(validator.validateNotNullConstraint('deal_estimate', 'estimate_id', true)).toBe(true);
      expect(validator.validateNotNullConstraint('deal_estimate', 'estimate_type', true)).toBe(true);
      expect(validator.validateNotNullConstraint('deal_estimate', 'linked_at', true)).toBe(true);
      
      // Check foreign keys
      expect(validator.foreignKeyExists(
        'deal_estimate',
        'deal_id',
        'deal',
        'id'
      )).toBe(true);
      
      expect(validator.foreignKeyExists(
        'deal_estimate',
        'estimate_id',
        'estimate',
        'id'
      )).toBe(true);
      
      // Check indexes
      expect(validator.indexExists('deal_estimate', 'idx_deal_estimate_deal_id')).toBe(true);
      expect(validator.indexExists('deal_estimate', 'idx_deal_estimate_estimate_id')).toBe(true);
    });
    
    it('should have contact_role table with correct structure (previously deal_contact)', () => {
      // Note: This test checks for the new contact_role table that replaces deal_contact
      // This is key for the repository refactoring to ensure the new schema is correct
      
      // First, check if contact_role table exists (new schema)
      const contactRoleExists = validator.tableExists('contact_role');
      
      if (contactRoleExists) {
        // If we've migrated to the new schema, validate contact_role
        const requiredColumns = [
          'deal_id',
          'contact_id',
          'role',
          'created_at',
          'created_by'
        ];
        
        const validation = validator.validateRequiredColumns('contact_role', requiredColumns);
        expect(validation.valid).toBe(true);
        expect(validation.missing).toHaveLength(0);
        
        // Check that it has a composite primary key
        expect(validator.foreignKeyExists(
          'contact_role',
          'deal_id',
          'deal',
          'id'
        )).toBe(true);
        
        expect(validator.foreignKeyExists(
          'contact_role',
          'contact_id',
          'contact',
          'id'
        )).toBe(true);
      } else {
        // If we haven't migrated yet, check that deal_contact still exists
        expect(validator.tableExists('deal_contact')).toBe(true);
        
        const requiredColumns = [
          'deal_id',
          'contact_id',
          'role'
        ];
        
        const validation = validator.validateRequiredColumns('deal_contact', requiredColumns);
        expect(validation.valid).toBe(true);
        expect(validation.missing).toHaveLength(0);
        
        // Check foreign keys
        expect(validator.foreignKeyExists(
          'deal_contact',
          'deal_id',
          'deal',
          'id'
        )).toBe(true);
        
        expect(validator.foreignKeyExists(
          'deal_contact',
          'contact_id',
          'contact',
          'id'
        )).toBe(true);
        
        // Check indexes
        expect(validator.indexExists('deal_contact', 'idx_deal_contact_deal_id')).toBe(true);
        expect(validator.indexExists('deal_contact', 'idx_deal_contact_contact_id')).toBe(true);
      }
    });
    
    it('should have contact_company junction table with correct structure', () => {
      // This table might not exist if it's part of a migration that hasn't been applied yet
      const contactCompanyExists = validator.tableExists('contact_company');
      
      if (contactCompanyExists) {
        const requiredColumns = [
          'contact_id',
          'company_id',
          'role',
          'is_primary',
          'created_at',
          'created_by'
        ];
        
        const validation = validator.validateRequiredColumns('contact_company', requiredColumns);
        expect(validation.valid).toBe(true);
        expect(validation.missing).toHaveLength(0);
        
        // Check foreign keys
        expect(validator.foreignKeyExists(
          'contact_company',
          'contact_id',
          'contact',
          'id'
        )).toBe(true);
        
        expect(validator.foreignKeyExists(
          'contact_company',
          'company_id',
          'company',
          'id'
        )).toBe(true);
        
        // Check indexes
        expect(validator.indexExists('contact_company', 'idx_contact_company_contact_id')).toBe(true);
        expect(validator.indexExists('contact_company', 'idx_contact_company_company_id')).toBe(true);
        expect(validator.indexExists('contact_company', 'idx_contact_company_is_primary')).toBe(true);
      } else {
        // Skip this test if the table doesn't exist yet
        console.log('contact_company table not found, skipping test');
      }
    });
    
    it('should have company_relationship junction table with correct structure', () => {
      // This table might not exist if it's part of a migration that hasn't been applied yet
      const companyRelationshipExists = validator.tableExists('company_relationship');
      
      if (companyRelationshipExists) {
        const requiredColumns = [
          'parent_company_id',
          'child_company_id',
          'relationship_type',
          'created_at',
          'created_by'
        ];
        
        const validation = validator.validateRequiredColumns('company_relationship', requiredColumns);
        expect(validation.valid).toBe(true);
        expect(validation.missing).toHaveLength(0);
        
        // Check foreign keys
        expect(validator.foreignKeyExists(
          'company_relationship',
          'parent_company_id',
          'company',
          'id'
        )).toBe(true);
        
        expect(validator.foreignKeyExists(
          'company_relationship',
          'child_company_id',
          'company',
          'id'
        )).toBe(true);
        
        // Check indexes
        expect(validator.indexExists('company_relationship', 'idx_company_relationship_parent')).toBe(true);
        expect(validator.indexExists('company_relationship', 'idx_company_relationship_child')).toBe(true);
      } else {
        // Skip this test if the table doesn't exist yet
        console.log('company_relationship table not found, skipping test');
      }
    });
  });
});