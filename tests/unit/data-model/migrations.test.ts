/**
 * Database Migration Tests
 * 
 * These tests ensure that migrations run correctly and maintain data integrity.
 * They validate both the up and down migrations to ensure reversibility.
 */

import BetterSqlite3 from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { DBSchemaValidator } from './db-schema-validator';
import { v4 as uuidv4 } from 'uuid';

// Helper functions for migrations
function getMigrationFiles() {
  const migrationsPath = path.join(__dirname, '../../../src/database/migrations');
  
  // Check if directory exists
  if (!fs.existsSync(migrationsPath)) {
    return [];
  }
  
  // Get all JavaScript files in the directory
  const files = fs.readdirSync(migrationsPath)
    .filter(file => file.endsWith('.js'))
    .sort(); // Sort by name, which should be prefixed with date
  
  return files.map(file => path.join(migrationsPath, file));
}

function loadMigration(filePath: string) {
  try {
    // Delete cache to ensure we get a fresh copy
    delete require.cache[require.resolve(filePath)];
    return require(filePath);
  } catch (error) {
    console.error(`Error loading migration ${filePath}:`, error);
    return null;
  }
}

describe('Database Migration Tests', () => {
  let db: BetterSqlite3.Database;
  let validator: DBSchemaValidator;
  
  beforeEach(() => {
    // Create a fresh in-memory database for each test
    db = new BetterSqlite3(':memory:');
    
    // Enable foreign keys
    db.pragma('foreign_keys = ON');
    
    // Create schema_version table
    db.exec(`
      CREATE TABLE IF NOT EXISTS schema_version (
        version INTEGER PRIMARY KEY,
        applied_at TEXT NOT NULL,
        description TEXT NOT NULL
      )
    `);
    
    validator = new DBSchemaValidator();
    validator.runSchemaScript(db.serialize());
  });
  
  afterEach(() => {
    db.close();
    validator.close();
  });
  
  describe('Migration Integrity', () => {
    it('should load all migrations without errors', () => {
      const migrationFiles = getMigrationFiles();
      
      // This test simply verifies that all migrations can be loaded
      for (const filePath of migrationFiles) {
        const migration = loadMigration(filePath);
        expect(migration).not.toBeNull();
        expect(typeof migration.up).toBe('function');
        expect(typeof migration.down).toBe('function');
      }
    });
    
    it('should apply all migrations in sequence', () => {
      const migrationFiles = getMigrationFiles();
      
      // Apply migrations in sequence
      for (const filePath of migrationFiles) {
        const migration = loadMigration(filePath);
        
        if (!migration) {
          continue;
        }
        
        // Apply the up migration
        migration.up(db);
        
        // Record the migration in schema_version
        const fileName = path.basename(filePath);
        db.prepare(`
          INSERT INTO schema_version (version, applied_at, description)
          VALUES (?, ?, ?)
        `).run(
          Date.now(),
          new Date().toISOString(),
          `Applied ${fileName}`
        );
      }
      
      // Verify schema_version has entries for all migrations
      const versionCount = db.prepare(`SELECT COUNT(*) as count FROM schema_version`).get();
      expect(versionCount.count).toBe(migrationFiles.length);
    });
  });
  
  describe('Relationship Table Migrations', () => {
    it('should correctly create company_relationship table', () => {
      // Find the migration that creates company_relationship
      const migrationFiles = getMigrationFiles();
      const companyRelationshipMigration = migrationFiles.find(
        file => path.basename(file).includes('company_relationships')
      );
      
      if (!companyRelationshipMigration) {
        console.log('Migration for company_relationship not found, skipping test');
        return;
      }
      
      // Load and apply the migration
      const migration = loadMigration(companyRelationshipMigration);
      migration.up(db);
      
      // Verify the table was created
      validator.runSchemaScript(db.serialize());
      expect(validator.tableExists('company_relationship')).toBe(true);
      
      // Verify required columns
      const validation = validator.validateRequiredColumns('company_relationship', [
        'parent_company_id',
        'child_company_id',
        'relationship_type',
        'created_at',
        'created_by'
      ]);
      
      expect(validation.valid).toBe(true);
      
      // Test inserting data
      const now = new Date().toISOString();
      
      // Create test companies
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run('parent-co', 'Parent Company', now, now, 'system', 'system');
      
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run('child-co', 'Child Company', now, now, 'system', 'system');
      
      // Create relationship
      db.prepare(`
        INSERT INTO company_relationship (
          parent_company_id, child_company_id, relationship_type, created_at, created_by
        ) VALUES (?, ?, ?, ?, ?)
      `).run('parent-co', 'child-co', 'subsidiary', now, 'system');
      
      // Verify the relationship was created
      const relationship = db.prepare(`
        SELECT * FROM company_relationship 
        WHERE parent_company_id = ? AND child_company_id = ?
      `).get('parent-co', 'child-co');
      
      expect(relationship).toBeDefined();
      expect(relationship.relationship_type).toBe('subsidiary');
      
      // Test down migration
      migration.down(db);
      
      // Verify the table was dropped
      validator.runSchemaScript(db.serialize());
      expect(validator.tableExists('company_relationship')).toBe(false);
    });
    
    it('should correctly create contact_company table', () => {
      // Find the migration that creates contact_company
      const migrationFiles = getMigrationFiles();
      const contactCompanyMigration = migrationFiles.find(
        file => path.basename(file).includes('contact_company_junction')
      );
      
      if (!contactCompanyMigration) {
        console.log('Migration for contact_company not found, skipping test');
        return;
      }
      
      // Load and apply the migration
      const migration = loadMigration(contactCompanyMigration);
      migration.up(db);
      
      // Verify the table was created
      validator.runSchemaScript(db.serialize());
      expect(validator.tableExists('contact_company')).toBe(true);
      
      // Verify required columns
      const validation = validator.validateRequiredColumns('contact_company', [
        'contact_id',
        'company_id',
        'role',
        'is_primary',
        'created_at',
        'created_by'
      ]);
      
      expect(validation.valid).toBe(true);
      
      // Test inserting data
      const now = new Date().toISOString();
      
      // Create test company and contact
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run('test-co', 'Test Company', now, now, 'system', 'system');
      
      db.prepare(`
        INSERT INTO contact (id, first_name, last_name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run('test-contact', 'John', 'Doe', now, now, 'system', 'system');
      
      // Create relationship
      db.prepare(`
        INSERT INTO contact_company (
          contact_id, company_id, role, is_primary, created_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run('test-contact', 'test-co', 'employee', 1, now, 'system');
      
      // Verify the relationship was created
      const relationship = db.prepare(`
        SELECT * FROM contact_company 
        WHERE contact_id = ? AND company_id = ?
      `).get('test-contact', 'test-co');
      
      expect(relationship).toBeDefined();
      expect(relationship.role).toBe('employee');
      expect(relationship.is_primary).toBe(1);
      
      // Test down migration
      migration.down(db);
      
      // Verify the table was dropped
      validator.runSchemaScript(db.serialize());
      expect(validator.tableExists('contact_company')).toBe(false);
    });
    
    it('should correctly migrate from deal_contact to contact_role', () => {
      // This test is only applicable if the migration exists
      const migrationFiles = getMigrationFiles();
      const contactRoleMigration = migrationFiles.find(
        file => path.basename(file).includes('contact_role')
      );
      
      if (!contactRoleMigration) {
        console.log('Migration for contact_role not found, skipping test');
        return;
      }
      
      // Create a deal_contact table to simulate pre-migration state
      db.exec(`
        CREATE TABLE deal_contact (
          deal_id TEXT NOT NULL,
          contact_id TEXT NOT NULL,
          role TEXT,
          PRIMARY KEY (deal_id, contact_id)
        )
      `);
      
      // Create supporting tables needed for foreign keys
      db.exec(`
        CREATE TABLE deal (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          created_by TEXT,
          updated_by TEXT
        )
      `);
      
      db.exec(`
        CREATE TABLE contact (
          id TEXT PRIMARY KEY,
          first_name TEXT,
          last_name TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          created_by TEXT,
          updated_by TEXT
        )
      `);
      
      // Insert test data
      const now = new Date().toISOString();
      db.prepare(`INSERT INTO deal (id, name, created_at, updated_at) VALUES (?, ?, ?, ?)`).run(
        'test-deal', 'Test Deal', now, now
      );
      
      db.prepare(`INSERT INTO contact (id, first_name, last_name, created_at, updated_at) VALUES (?, ?, ?, ?, ?)`).run(
        'test-contact', 'John', 'Doe', now, now
      );
      
      db.prepare(`INSERT INTO deal_contact (deal_id, contact_id, role) VALUES (?, ?, ?)`).run(
        'test-deal', 'test-contact', 'decision_maker'
      );
      
      // Load and apply the migration
      const migration = loadMigration(contactRoleMigration);
      migration.up(db);
      
      // Verify the new table was created and data migrated
      validator.runSchemaScript(db.serialize());
      
      // Check if contact_role table exists
      if (validator.tableExists('contact_role')) {
        // Verify data was migrated
        const relationship = db.prepare(`
          SELECT * FROM contact_role 
          WHERE deal_id = ? AND contact_id = ?
        `).get('test-deal', 'test-contact');
        
        expect(relationship).toBeDefined();
        expect(relationship.role).toBe('decision_maker');
        expect(relationship.created_at).toBeDefined();
        expect(relationship.created_by).toBeDefined();
      } else {
        // If the table wasn't created, check if deal_contact was enhanced
        const columns = db.prepare(`PRAGMA table_info(deal_contact)`).all();
        const hasCreatedAt = columns.some((col: any) => col.name === 'created_at');
        const hasCreatedBy = columns.some((col: any) => col.name === 'created_by');
        
        expect(hasCreatedAt || hasCreatedBy).toBe(true);
      }
      
      // Test down migration if applicable
      migration.down(db);
      
      // Verify state after down migration
      validator.runSchemaScript(db.serialize());
    });
  });
  
  describe('Data Migration Tests', () => {
    it('should preserve data integrity during migrations', () => {
      // Create basic schema for testing
      db.exec(`
        CREATE TABLE company (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        );
        
        CREATE TABLE contact (
          id TEXT PRIMARY KEY,
          first_name TEXT,
          last_name TEXT,
          email TEXT,
          company_id TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (company_id) REFERENCES company(id)
        );
      `);
      
      // Insert sample data
      const now = new Date().toISOString();
      const companyIds = Array.from({ length: 5 }).map(() => uuidv4());
      
      for (let i = 0; i < 5; i++) {
        db.prepare(`
          INSERT INTO company (id, name, created_at, updated_at)
          VALUES (?, ?, ?, ?)
        `).run(
          companyIds[i],
          `Company ${i + 1}`,
          now,
          now
        );
        
        // Add 3 contacts per company
        for (let j = 0; j < 3; j++) {
          db.prepare(`
            INSERT INTO contact (id, first_name, last_name, email, company_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `).run(
            uuidv4(),
            `First${i}_${j}`,
            `Last${i}_${j}`,
            `contact_${i}_${j}@example.com`,
            companyIds[i],
            now,
            now
          );
        }
      }
      
      // Verify data is inserted correctly
      const companyCount = db.prepare(`SELECT COUNT(*) as count FROM company`).get();
      expect(companyCount.count).toBe(5);
      
      const contactCount = db.prepare(`SELECT COUNT(*) as count FROM contact`).get();
      expect(contactCount.count).toBe(15);
      
      // Simulate a migration that adds new fields to company
      db.exec(`
        ALTER TABLE company ADD COLUMN industry TEXT;
        ALTER TABLE company ADD COLUMN size TEXT;
        ALTER TABLE company ADD COLUMN website TEXT;
      `);
      
      // Update some records with the new fields
      db.prepare(`
        UPDATE company
        SET industry = ?, size = ?, website = ?
        WHERE id = ?
      `).run(
        'Technology',
        'Medium',
        'https://example.com',
        companyIds[0]
      );
      
      // Verify data integrity after migration
      const updatedCompany = db.prepare(`SELECT * FROM company WHERE id = ?`).get(companyIds[0]);
      expect(updatedCompany).toBeDefined();
      expect(updatedCompany.industry).toBe('Technology');
      expect(updatedCompany.size).toBe('Medium');
      expect(updatedCompany.website).toBe('https://example.com');
      
      // Verify related data is still intact
      const relatedContacts = db.prepare(`SELECT COUNT(*) as count FROM contact WHERE company_id = ?`).get(companyIds[0]);
      expect(relatedContacts.count).toBe(3);
    });
    
    it('should handle renamed tables correctly', () => {
      // This test simulates the deal_contact to contact_role migration
      
      // Create old schema
      db.exec(`
        CREATE TABLE deal (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        );
        
        CREATE TABLE contact (
          id TEXT PRIMARY KEY,
          first_name TEXT,
          last_name TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        );
        
        CREATE TABLE deal_contact (
          deal_id TEXT NOT NULL,
          contact_id TEXT NOT NULL,
          role TEXT,
          PRIMARY KEY (deal_id, contact_id),
          FOREIGN KEY (deal_id) REFERENCES deal(id),
          FOREIGN KEY (contact_id) REFERENCES contact(id)
        );
      `);
      
      // Insert sample data
      const now = new Date().toISOString();
      const dealId = uuidv4();
      const contactId = uuidv4();
      
      db.prepare(`INSERT INTO deal (id, name, created_at, updated_at) VALUES (?, ?, ?, ?)`).run(
        dealId, 'Test Deal', now, now
      );
      
      db.prepare(`INSERT INTO contact (id, first_name, last_name, created_at, updated_at) VALUES (?, ?, ?, ?, ?)`).run(
        contactId, 'John', 'Doe', now, now
      );
      
      db.prepare(`INSERT INTO deal_contact (deal_id, contact_id, role) VALUES (?, ?, ?)`).run(
        dealId, contactId, 'decision_maker'
      );
      
      // Simulate migration by creating new table and copying data
      db.exec(`
        CREATE TABLE contact_role (
          deal_id TEXT NOT NULL,
          contact_id TEXT NOT NULL,
          role TEXT,
          created_at TEXT NOT NULL,
          created_by TEXT NOT NULL,
          PRIMARY KEY (deal_id, contact_id),
          FOREIGN KEY (deal_id) REFERENCES deal(id),
          FOREIGN KEY (contact_id) REFERENCES contact(id)
        );
        
        INSERT INTO contact_role (deal_id, contact_id, role, created_at, created_by)
        SELECT deal_id, contact_id, role, '${now}', 'migration'
        FROM deal_contact;
      `);
      
      // Verify data was migrated correctly
      const migrated = db.prepare(`SELECT * FROM contact_role WHERE deal_id = ? AND contact_id = ?`).get(
        dealId, contactId
      );
      
      expect(migrated).toBeDefined();
      expect(migrated.role).toBe('decision_maker');
      expect(migrated.created_at).toBe(now);
      expect(migrated.created_by).toBe('migration');
      
      // Test that foreign keys still work
      const dealBefore = db.prepare(`SELECT * FROM deal WHERE id = ?`).get(dealId);
      expect(dealBefore).toBeDefined();
      
      // Delete the deal - this should cascade to contact_role if FK is set up correctly
      db.prepare(`DELETE FROM deal WHERE id = ?`).run(dealId);
      
      // Check that the contact_role record was deleted
      const deletedRole = db.prepare(`SELECT * FROM contact_role WHERE deal_id = ?`).get(dealId);
      expect(deletedRole).toBeUndefined();
    });
  });
});