/**
 * Database Schema Validator
 * 
 * This utility provides functions to validate the database schema against expectations.
 * It can check table existence, column definitions, foreign keys, and indexes.
 */

import BetterSqlite3, { Database } from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

interface ColumnInfo {
  cid: number;
  name: string;
  type: string;
  notnull: number;
  dflt_value: string | null;
  pk: number;
}

interface ForeignKeyInfo {
  id: number;
  seq: number;
  table: string;
  from: string;
  to: string;
  on_update: string;
  on_delete: string;
  match: string;
}

interface IndexInfo {
  seq: number;
  name: string;
  unique: number;
  origin: string;
  partial: number;
}

export class DBSchemaValidator {
  private db: Database;

  /**
   * Constructor
   * @param dbPath Path to the SQLite database file
   */
  constructor(dbPath?: string) {
    // If no path is provided, create an in-memory database
    if (!dbPath) {
      this.db = new BetterSqlite3(':memory:');
      return;
    }

    // Check if the file exists
    if (!fs.existsSync(dbPath)) {
      throw new Error(`Database file not found: ${dbPath}`);
    }

    // Open the database connection
    this.db = new BetterSqlite3(dbPath);
  }

  /**
   * Close the database connection
   */
  close() {
    this.db.close();
  }

  /**
   * Check if a table exists in the database
   * @param tableName Table name to check
   * @returns Boolean indicating if the table exists
   */
  tableExists(tableName: string): boolean {
    const result = this.db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name=?
    `).get(tableName);

    return !!result;
  }

  /**
   * Get column information for a table
   * @param tableName Table name
   * @returns Array of column info objects
   */
  getTableColumns(tableName: string): ColumnInfo[] {
    if (!this.tableExists(tableName)) {
      throw new Error(`Table '${tableName}' does not exist`);
    }

    return this.db.prepare(`PRAGMA table_info(${tableName})`).all() as ColumnInfo[];
  }

  /**
   * Check if a column exists in a table
   * @param tableName Table name
   * @param columnName Column name
   * @returns Boolean indicating if the column exists
   */
  columnExists(tableName: string, columnName: string): boolean {
    try {
      const columns = this.getTableColumns(tableName);
      return columns.some(col => col.name === columnName);
    } catch (error) {
      return false;
    }
  }

  /**
   * Get information about a specific column
   * @param tableName Table name
   * @param columnName Column name
   * @returns Column info object or null if the column doesn't exist
   */
  getColumnInfo(tableName: string, columnName: string): ColumnInfo | null {
    try {
      const columns = this.getTableColumns(tableName);
      return columns.find(col => col.name === columnName) || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get foreign key constraints for a table
   * @param tableName Table name
   * @returns Array of foreign key info objects
   */
  getForeignKeys(tableName: string): ForeignKeyInfo[] {
    if (!this.tableExists(tableName)) {
      throw new Error(`Table '${tableName}' does not exist`);
    }

    return this.db.prepare(`PRAGMA foreign_key_list(${tableName})`).all() as ForeignKeyInfo[];
  }

  /**
   * Check if a foreign key exists
   * @param tableName Table name
   * @param columnName Column name
   * @param referenceTable Reference table
   * @param referenceColumn Reference column
   * @returns Boolean indicating if the foreign key exists
   */
  foreignKeyExists(
    tableName: string,
    columnName: string,
    referenceTable: string,
    referenceColumn: string
  ): boolean {
    try {
      const foreignKeys = this.getForeignKeys(tableName);
      return foreignKeys.some(
        fk => fk.from === columnName && 
              fk.table === referenceTable &&
              fk.to === referenceColumn
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Get indexes for a table
   * @param tableName Table name
   * @returns Array of index info objects
   */
  getIndexes(tableName: string): IndexInfo[] {
    if (!this.tableExists(tableName)) {
      throw new Error(`Table '${tableName}' does not exist`);
    }

    return this.db.prepare(`PRAGMA index_list(${tableName})`).all() as IndexInfo[];
  }

  /**
   * Check if an index exists on a table
   * @param tableName Table name
   * @param indexName Index name
   * @returns Boolean indicating if the index exists
   */
  indexExists(tableName: string, indexName: string): boolean {
    try {
      const indexes = this.getIndexes(tableName);
      return indexes.some(idx => idx.name === indexName);
    } catch (error) {
      return false;
    }
  }

  /**
   * Run a schema creation script on the database
   * @param script SQL script to run
   */
  runSchemaScript(script: string): void {
    this.db.exec(script);
  }

  /**
   * Create a complete schema from the source files
   * @param schemaModulePath Path to the schema module
   */
  createSchemaFromSource(schemaModulePath: string): void {
    // This is a simplified implementation
    // In a real scenario, you would import and run the schema creation functions
    const schemaScript = fs.readFileSync(schemaModulePath, 'utf8');
    this.runSchemaScript(schemaScript);
  }

  /**
   * Validate required columns in a table
   * @param tableName Table name
   * @param requiredColumns Array of required column names
   * @returns Object with validation results
   */
  validateRequiredColumns(tableName: string, requiredColumns: string[]): {
    valid: boolean;
    missing: string[];
  } {
    try {
      const columns = this.getTableColumns(tableName);
      const columnNames = columns.map(col => col.name);
      
      const missing = requiredColumns.filter(col => !columnNames.includes(col));
      
      return {
        valid: missing.length === 0,
        missing
      };
    } catch (error) {
      return {
        valid: false,
        missing: requiredColumns
      };
    }
  }

  /**
   * Validate the column type in a table
   * @param tableName Table name
   * @param columnName Column name
   * @param expectedType Expected column type
   * @returns Boolean indicating if the type matches
   */
  validateColumnType(tableName: string, columnName: string, expectedType: string): boolean {
    const columnInfo = this.getColumnInfo(tableName, columnName);
    
    if (!columnInfo) {
      return false;
    }
    
    // SQLite type system is flexible, so we do case-insensitive comparison
    return columnInfo.type.toLowerCase() === expectedType.toLowerCase();
  }

  /**
   * Validate a not null constraint on a column
   * @param tableName Table name
   * @param columnName Column name
   * @param shouldBeNotNull Whether the column should have a NOT NULL constraint
   * @returns Boolean indicating if the constraint matches expectations
   */
  validateNotNullConstraint(tableName: string, columnName: string, shouldBeNotNull: boolean): boolean {
    const columnInfo = this.getColumnInfo(tableName, columnName);
    
    if (!columnInfo) {
      return false;
    }
    
    return (columnInfo.notnull === 1) === shouldBeNotNull;
  }

  /**
   * Validate a primary key constraint on a column
   * @param tableName Table name
   * @param columnName Column name
   * @returns Boolean indicating if the column is a primary key
   */
  validatePrimaryKey(tableName: string, columnName: string): boolean {
    const columnInfo = this.getColumnInfo(tableName, columnName);
    
    if (!columnInfo) {
      return false;
    }
    
    return columnInfo.pk === 1;
  }
}

/**
 * Create an in-memory database with the schema applied
 * @returns Database validator instance
 */
export function createTestDatabase(): DBSchemaValidator {
  const validator = new DBSchemaValidator();
  
  // Initialize schema version table
  validator.runSchemaScript(`
    CREATE TABLE IF NOT EXISTS schema_version (
      version INTEGER PRIMARY KEY,
      applied_at TEXT NOT NULL,
      description TEXT NOT NULL
    )
  `);
  
  return validator;
}