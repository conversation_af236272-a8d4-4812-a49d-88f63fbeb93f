#!/bin/bash
# Script to clean up test files while preserving infrastructure

# Create backup directory
BACKUP_DIR="./tests/backup-$(date +%Y%m%d%H%M%S)"
mkdir -p "$BACKUP_DIR"
echo "Created backup directory: $BACKUP_DIR"

# Copy all test files to backup
cp -r ./tests/unit "$BACKUP_DIR/unit"
cp -r ./tests/e2e "$BACKUP_DIR/e2e"
mkdir -p "$BACKUP_DIR/setup"
cp -r ./tests/setup "$BACKUP_DIR/setup"
cp -r ./tests/mocks "$BACKUP_DIR/mocks"
echo "Backed up all test files"

# Create new structure while preserving infrastructure
# Remove contents of unit tests (but keep directory structure)
mkdir -p ./tests/unit/frontend/components
mkdir -p ./tests/unit/frontend/utils
mkdir -p ./tests/unit/services/cashflow
mkdir -p ./tests/unit/services/xero
mkdir -p ./tests/unit/services/harvest

# Remove all test files but preserve directories
find ./tests/unit -name "*.test.ts" -o -name "*.test.tsx" | xargs rm -f
echo "Removed old unit test files"

# Create a new directory for real-data tests
mkdir -p ./tests/real-data
echo "Created directory for real-data tests"

# Preserve e2e utilities and infrastructure
mkdir -p ./tests/e2e/screenshots
cp "$BACKUP_DIR/e2e/utils.ts" ./tests/e2e/utils.ts
rm -f ./tests/e2e/*.spec.ts
echo "Preserved E2E infrastructure, removed spec files"

echo "Cleanup complete. Old tests backed up to $BACKUP_DIR"
echo "Ready to implement new real-data based tests"
