# Onbord Financial Dashboard Testing Guide

This guide explains the simplified testing approach for the Onbord Financial Dashboard, focusing on using real data instead of complex mocks.

## Testing Philosophy

Our testing approach follows these principles:

1. **Use Real Data**: Instead of complex mocks, we use real API data from test accounts
2. **Focus on Core Logic**: Test the essential business logic and calculations
3. **Simplicity Over Comprehensiveness**: Simple, maintainable tests are better than complex, brittle ones
4. **Targeted Test Types**: Different types of tests for different parts of the application

## Test Types

### 1. Real-Data Tests

These tests connect to actual APIs (Xero, Harvest) using test account credentials to verify integration and data processing:

```
npm run test:real-data
```

Requirements:
- Create a `.env.test` file with test account credentials (copy from `.env.test.example`)
- These tests use actual API calls, so they may be slower but are more reliable

Benefits:
- Catches real integration issues
- No need to maintain complex mocks
- Tests actual API behavior
- Verifies data transformations with real data

### 2. Unit Tests for Pure Logic

These tests verify pure functions and components without requiring API connections:

```
npm run test:unit
```

Focus areas:
- Data transformation functions
- Calculation logic
- Component rendering

### 3. Basic E2E Tests

These tests verify the application loads and basic navigation works:

```
npm run test:e2e:basic
```

For more comprehensive E2E tests that require authentication:

```
npm run test:e2e:ui
```

Note: Use the UI mode to manually authenticate once, then Playwright can save and reuse that authentication state.

## Setting Up Test Accounts

For full testing capability, create dedicated test accounts:

1. **Xero Test Account**: 
   - Create a developer account at https://developer.xero.com/
   - Set up a test organization
   - Generate API credentials (Client ID and Secret)

2. **Harvest Test Account**:
   - Create a test account at https://www.getharvest.com/
   - Create an API token at https://id.getharvest.com/developers
   - Note your Account ID

3. **Configure `.env.test`**:
   - Copy `.env.test.example` to `.env.test`
   - Fill in your test account credentials
   - This file is gitignored to keep credentials secure

## Adding New Tests

### Adding a Real-Data Test

1. Create a new file in `tests/real-data/` with a `.test.ts` extension
2. Use the utility functions in `tests/real-data/utils.ts`
3. Handle authentication and API connections
4. Focus on testing data processing, not specific values

Example:
```typescript
// tests/real-data/my-feature.test.ts
import { isTestEnvironmentConfigured, getTestDateRange } from './utils';
import { MyService } from '@/services/my-service';

// Skip tests if environment not configured
const runTests = isTestEnvironmentConfigured();

describe('My Feature with Real Data', () => {
  if (!runTests) {
    it.skip('Tests skipped due to missing credentials', () => {});
    return;
  }

  let myService: MyService;
  
  beforeAll(() => {
    myService = new MyService();
  });
  
  it('processes data correctly', async () => {
    const result = await myService.processData();
    expect(result).toBeDefined();
    // Test structure, not specific values
    expect(result).toHaveProperty('expectedField');
  });
});
```

### Adding a Unit Test

1. Create a new file in `tests/unit/` with a `.test.ts` or `.test.tsx` extension
2. Use explicit test data, not complex mocks
3. Test one unit of functionality per test file

Example:
```typescript
// tests/unit/services/my-service.test.ts
import { MyService } from '@/services/my-service';

describe('MyService', () => {
  const service = new MyService();
  
  it('transforms data correctly', () => {
    // Explicit test data
    const inputData = { key: 'value' };
    
    // Call the function directly
    const result = service.transformData(inputData);
    
    // Assert on the result
    expect(result).toEqual({ transformedKey: 'value' });
  });
});
```

## Running Tests in CI/CD

For CI/CD environments, configure environment secrets for your test accounts:

### GitHub Actions Example

```yaml
name: Tests

on:
  pull_request:
    branches: [preview, main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run unit tests
        run: npm run test:unit
      - name: Run basic E2E tests
        run: npm run test:e2e:basic
      # Only run real-data tests if credentials are provided
      - name: Run real-data tests
        if: ${{ env.TEST_XERO_CLIENT_ID }}
        run: npm run test:real-data
        env:
          XERO_CLIENT_ID: ${{ secrets.TEST_XERO_CLIENT_ID }}
          XERO_CLIENT_SECRET: ${{ secrets.TEST_XERO_CLIENT_SECRET }}
          HARVEST_ACCESS_TOKEN: ${{ secrets.TEST_HARVEST_ACCESS_TOKEN }}
          HARVEST_ACCOUNT_ID: ${{ secrets.TEST_HARVEST_ACCOUNT_ID }}
```

## Best Practices

1. **Test Structure, Not Values**: For real API data, test the structure and behavior, not specific values
2. **Keep Tests Independent**: Each test should run independently without relying on state from other tests
3. **Use try/catch for API Calls**: Handle potential API errors gracefully
4. **Avoid Testing External APIs**: Test your code's interaction with APIs, not the APIs themselves
5. **Clear Assertions**: Make it clear what each test is verifying
6. **Handle Authentication**: Either skip authenticated tests in CI or provide environment secrets

## Troubleshooting

### API Authentication Issues

- Ensure test account credentials are correct in `.env.test`
- Verify API keys have proper permissions
- Check network connectivity to API endpoints
- Look for rate limiting issues (especially in CI)

### Test Skipping Issues

- If tests are being skipped unexpectedly, check the `isTestEnvironmentConfigured()` function
- Ensure all required environment variables are defined

### Long-Running Tests

- Real API calls naturally take longer than mocked tests
- Use the `--testTimeout` parameter to increase timeout for API tests
- Consider adding retries for flaky API calls
