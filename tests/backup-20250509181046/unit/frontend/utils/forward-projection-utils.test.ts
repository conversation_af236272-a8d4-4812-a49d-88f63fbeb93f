import {
  formatCurrency,
  formatDate,
  formatChartData,
  calculateMonthlyEquivalent,
  calculatePercentageChange,
  ChartDataPoint
} from '@/frontend/components/ForwardProjection/utils';
import { CashflowForecast, CustomExpense, DailyCashflow, BankAccount, Transaction } from '@/types/financial'; // Import necessary types

describe('Forward Projection Utils', () => {

  // --- Tests for formatCurrency ---
  describe('formatCurrency', () => {
    it('should format positive numbers correctly', () => {
      expect(formatCurrency(1234.56)).toBe('$1,234.56');
      expect(formatCurrency(0)).toBe('$0.00');
      expect(formatCurrency(100)).toBe('$100.00');
    });

    it('should format negative numbers correctly', () => {
      expect(formatCurrency(-987.65)).toBe('-$987.65');
      expect(formatCurrency(-0.50)).toBe('-$0.50');
    });

    it('should handle large numbers', () => {
      expect(formatCurrency(1234567.89)).toBe('$1,234,567.89');
      expect(formatCurrency(-1000000)).toBe('-$1,000,000.00');
    });
  });

  // --- Tests for formatDate ---
  describe('formatDate', () => {
    it('should format valid date strings correctly', () => {
      expect(formatDate('2025-04-15')).toBe('15 Apr 2025');
      expect(formatDate('2024-12-01T10:00:00Z')).toBe('01 Dec 2024');
    });

    it('should format valid Date objects correctly', () => {
      expect(formatDate(new Date(2025, 3, 15))).toBe('15 Apr 2025'); // Month is 0-indexed
      expect(formatDate(new Date('2025-05-20T00:00:00.000Z'))).toBe('20 May 2025');
    });

    it('should return em dash for invalid date strings', () => {
      expect(formatDate('invalid-date')).toBe('—');
      expect(formatDate('2025-13-01')).toBe('—'); // Invalid month
    });

    it('should return em dash for null or undefined input', () => {
      expect(formatDate(null)).toBe('—');
      expect(formatDate(undefined)).toBe('—');
    });

     it('should return em dash for invalid Date objects', () => {
       expect(formatDate(new Date('not a date'))).toBe('—');
     });
  });

  // --- Tests for formatChartData ---
  describe('formatChartData', () => {
    // Helper to create a minimal valid CashflowForecast mock
    const createMockForecast = (dailyData: DailyCashflow[] | null): CashflowForecast => ({
      startDate: new Date('2025-04-01'),
      endDate: new Date('2025-04-03'),
      startingBalance: 1000,
      endingBalance: 1050,
      accounts: [] as BankAccount[], // Provide empty array for required property
      projectedTransactions: [] as Transaction[], // Provide empty array for required property
      dailyCashflow: dailyData || [], // Use provided data or empty array
      customExpenses: [] as CustomExpense[], // Provide empty array for required property
      // Add other required properties if the type definition changes
    });

    it('should transform CashflowForecast data correctly', () => {
      const mockDailyCashflow: DailyCashflow[] = [
        { date: new Date('2025-04-01'), balance: 1000, inflows: 0, outflows: 0, netFlow: 0, transactions: [] },
        { date: new Date('2025-04-02'), balance: 1100, inflows: 100, outflows: 50, netFlow: 50, transactions: [] },
        { date: new Date('2025-04-03'), balance: 1050, inflows: 0, outflows: 100, netFlow: -100, transactions: [] },
      ];
      // Use helper to create valid mock
      const mockProjectionData = createMockForecast(mockDailyCashflow);

      const expectedChartData: ChartDataPoint[] = [
        { date: '2025-04-01', balance: 1000, inflows: 0, outflows: -0 },
        { date: '2025-04-02', balance: 1100, inflows: 100, outflows: -50 }, // Outflows negated
        { date: '2025-04-03', balance: 1050, inflows: 0, outflows: -100 }, // Outflows negated
      ];

      const result = formatChartData(mockProjectionData);
      expect(result).toEqual(expectedChartData);
    });

    it('should return an empty array if projectionData is null', () => {
      expect(formatChartData(null)).toEqual([]);
    });

    it('should return an empty array if dailyCashflow is null or empty', () => {
      // Use helper to create valid mock with null/empty dailyCashflow
      expect(formatChartData(createMockForecast(null))).toEqual([]);
      expect(formatChartData(createMockForecast([]))).toEqual([]);
    });
  });

  // --- Tests for calculateMonthlyEquivalent ---
  describe('calculateMonthlyEquivalent', () => {
    it('should calculate monthly equivalent correctly for various frequencies', () => {
      // Use valid expense types from the enum
      const expenses: CustomExpense[] = [
        { id: '1', name: 'Rent', amount: 1200, frequency: 'monthly', date: new Date(), type: 'Other' }, // Was 'Fixed'
        { id: '2', name: 'Groceries', amount: 150, frequency: 'weekly', date: new Date(), type: 'Other' }, // Was 'Variable'
        { id: '3', name: 'Insurance', amount: 600, frequency: 'quarterly', date: new Date(), type: 'Insurances' }, // Was 'Fixed'
        { id: '4', name: 'Gym', amount: 50, frequency: 'monthly', date: new Date(), type: 'Other' }, // Was 'Variable'
        { id: '5', name: 'Car Wash', amount: 20, frequency: 'weekly', date: new Date(), type: 'Other' }, // Was 'Variable'
        { id: '6', name: 'One-off', amount: 500, frequency: 'one-off', date: new Date(), type: 'Other' },
      ];
      // Expected: 1200 (monthly) + (150 * 4.333) + (600 / 3) + 50 (monthly) + (20 * 4.333) + 500 (one-off)
      // Expected: 1200 + 649.95 + 200 + 50 + 86.66 + 500 = 2686.61
      const expectedMonthly = 1200 + (150 * 4.333) + (600 / 3) + 50 + (20 * 4.333) + 500;
      expect(calculateMonthlyEquivalent(expenses)).toBeCloseTo(expectedMonthly);
    });

    it('should return 0 for an empty expense array', () => {
      expect(calculateMonthlyEquivalent([])).toBe(0);
    });
  });

   // --- Tests for calculatePercentageChange ---
  describe('calculatePercentageChange', () => {
    it('should calculate positive percentage change', () => {
      expect(calculatePercentageChange(110, 100)).toBeCloseTo(10); // 10% increase
      expect(calculatePercentageChange(50, 25)).toBeCloseTo(100); // 100% increase
    });

    it('should calculate negative percentage change', () => {
      expect(calculatePercentageChange(90, 100)).toBeCloseTo(-10); // 10% decrease
      expect(calculatePercentageChange(25, 50)).toBeCloseTo(-50); // 50% decrease
    });

    it('should handle zero previous value', () => {
      expect(calculatePercentageChange(50, 0)).toBe(100); // Increase from 0
      expect(calculatePercentageChange(0, 0)).toBe(0); // No change from 0
      expect(calculatePercentageChange(-50, 0)).toBe(0); // Decrease from 0 (returns 0 based on logic)
    });

    it('should handle zero current value', () => {
      expect(calculatePercentageChange(0, 50)).toBeCloseTo(-100); // 100% decrease to 0
    });

    it('should handle negative values', () => {
      expect(calculatePercentageChange(-50, -100)).toBeCloseTo(50); // Increase from -100 to -50 is 50%
      expect(calculatePercentageChange(-150, -100)).toBeCloseTo(-50); // Decrease from -100 to -150 is -50%
      expect(calculatePercentageChange(50, -50)).toBeCloseTo(200); // Change from -50 to 50
      expect(calculatePercentageChange(-50, 50)).toBeCloseTo(-200); // Change from 50 to -50
    });

    it('should return 0 if values are equal', () => {
      expect(calculatePercentageChange(100, 100)).toBe(0);
      expect(calculatePercentageChange(-50, -50)).toBe(0);
    });
  });

});
