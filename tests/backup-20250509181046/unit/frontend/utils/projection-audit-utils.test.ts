import {
  safeString,
  resolveClientName,
  decisionToTransactionLike,
  convertEventToDecision,
  extractInvoiceId,
  generateSyntheticExclusions,
  determineRuleType,
  getDecisionDescription,
  getTypeColorForDecision,
  processDecision,
  normalizeDecisionReason,
  calculateSummary,
} from '@/frontend/components/ForwardProjection/utils/projection-audit-utils';
import { FilterDecision, ProjectionFilterEvent, FILTER_RULES } from '@/frontend/components/ForwardProjection/types/projection-audit-types';
import { Transaction } from '@/types/financial';

// Mock the transaction list utils dependency
jest.mock('@/frontend/components/ForwardProjection/utils/transactions-list-utils', () => ({
  getFormattedDescription: jest.fn((tx) => `${tx.metadata?.projectName || tx.description} | ${tx.metadata?.clientName || 'Unknown Client'}`),
}));


describe('Projection Audit Utils', () => {

  // --- safeString ---
  describe('safeString', () => {
    it('should return string for primitive types', () => {
      expect(safeString('hello')).toBe('hello');
      expect(safeString(123)).toBe('123');
      expect(safeString(true)).toBe('true');
    });
    it('should return empty string for null/undefined', () => {
      expect(safeString(null)).toBe('');
      expect(safeString(undefined)).toBe('');
    });
    it('should stringify objects', () => {
      expect(safeString({ a: 1 })).toBe('{"a":1}');
      expect(safeString([1, 2])).toBe('[1,2]');
    });
     it('should handle circular objects', () => {
       const circular: any = { name: 'circular' };
       circular.self = circular;
       // Stringify will throw, so it should return '[Object]'
       expect(safeString(circular)).toBe('[Object]');
     });
  });

  // --- resolveClientName ---
  describe('resolveClientName', () => {
    // Basic tests - more complex scenarios depend on Map structures
    it('should return existing client name if valid', () => {
      const decision = { clientName: 'Client Alpha' };
      expect(resolveClientName(decision)).toBe('Client Alpha');
    });
    it('should return undefined if no valid name and no maps provided', () => {
      const decision = { clientName: 'Unknown Client' };
      expect(resolveClientName(decision)).toBeUndefined();
      const decision2 = {};
      expect(resolveClientName(decision2)).toBeUndefined();
    });
    // TODO: Add tests with mocked transactionsById and projectClientMap if needed
  });

  // --- decisionToTransactionLike ---
  describe('decisionToTransactionLike', () => {
    it('should convert a decision to a transaction-like object', () => {
      const decision: FilterDecision = {
        id: 'future-work-123', projectId: 'p123', projectName: 'Project Gamma', clientName: 'Client Gamma',
        invoiceType: 'Projected Income', invoiceDate: '2025-05-10T00:00:00.000Z', paymentDate: '2025-05-24T00:00:00.000Z',
        amount: 1500, reason: 'Meets criteria', action: 'kept', rule: 'MEETS_CRITERIA',
        relatedInvoiceNumber: 'INV-999', relatedInvoiceDate: '2025-05-01T00:00:00.000Z'
      };
      const result = decisionToTransactionLike(decision);
      expect(result).toEqual({
        id: 'future-work-123',
        source: 'harvest',
        type: 'invoice',
        metadata: {
          clientName: 'Client Gamma',
          projectName: 'Project Gamma',
          projectId: 'p123',
          invoiceNumber: 'INV-999',
          issue_date: '2025-05-01T00:00:00.000Z'
        },
        description: 'Project Gamma',
        date: new Date('2025-05-24T00:00:00.000Z'),
        amount: 1500
      });
    });
     it('should handle missing optional fields', () => {
        const decision: FilterDecision = {
            projectId: 'p456', projectName: 'Project Delta',
            invoiceType: 'Uninvoiced Work', paymentDate: '2025-06-01T00:00:00.000Z',
            amount: 500, reason: 'Some reason', action: 'kept', rule: 'MEETS_CRITERIA'
        };
        const result = decisionToTransactionLike(decision);
        expect(result).toEqual({
            id: '', // Defaults to empty string
            source: 'harvest',
            type: 'invoice',
            metadata: {
                clientName: 'Unknown Client', // Default
                projectName: 'Project Delta',
                projectId: 'p456',
                invoiceNumber: undefined, // Not present
                issue_date: undefined // Not present
            },
            description: 'Project Delta',
            date: new Date('2025-06-01T00:00:00.000Z'),
            amount: 500
        });
     });
      it('should handle invalid date gracefully', () => {
        const decision: FilterDecision = {
            projectId: 'p789', projectName: 'Project Epsilon',
            invoiceType: 'Projected Income', paymentDate: 'invalid-date',
            amount: 100, reason: 'Reason', action: 'kept', rule: 'MEETS_CRITERIA'
        };
        const result = decisionToTransactionLike(decision);
        expect(result.date).toBeInstanceOf(Date); // Should default to new Date()
        // Check if the date is roughly 'now' (within a tolerance)
        expect(Math.abs(result.date.getTime() - Date.now())).toBeLessThan(1000);
      });
       it('should handle invalid amount gracefully', () => {
        const decision: FilterDecision = {
            projectId: 'p111', projectName: 'Project Zeta',
            invoiceType: 'Projected Income', paymentDate: '2025-07-01T00:00:00.000Z',
            amount: 'not-a-number' as any, reason: 'Reason', action: 'kept', rule: 'MEETS_CRITERIA'
        };
        const result = decisionToTransactionLike(decision);
        expect(result.amount).toBe(0); // Should default to 0
      });
  });

  // --- convertEventToDecision ---
  describe('convertEventToDecision', () => {
    it('should convert a basic kept event', () => {
      const event: ProjectionFilterEvent = {
        invoice: { id: 'future-work-1', projectId: 'p1', projectName: 'Proj 1', date: new Date('2025-05-15'), amount: 100, metadata: { clientName: 'Client 1' } },
        action: 'kept', reason: 'Meets projection criteria'
      };
      const decision = convertEventToDecision(event);
      expect(decision).toEqual(expect.objectContaining({
        projectId: 'p1', projectName: 'Proj 1', clientName: 'Client 1', invoiceType: 'Projected Income',
        paymentDate: new Date('2025-05-15').toISOString(), amount: 100, reason: 'Meets projection criteria',
        action: 'kept', id: 'future-work-1', rule: 'MEETS_CRITERIA',
        formattedDescription: 'Proj 1 | Client 1' // Check formatted description
      }));
    });

    it('should convert an excluded event with related invoice', () => {
       const event: ProjectionFilterEvent = {
        invoice: { id: 'future-work-2', projectId: 'p2', projectName: 'Proj 2', date: new Date('2025-06-20'), amount: 200, metadata: { clientName: 'Client 2' } },
        action: 'excluded', reason: 'Real invoice exists',
        relatedInvoice: { id: 'outstanding-invoice-99', what: 'Real Inv 99', date: new Date('2025-06-18'), metadata: { invoiceNumber: 'INV-99', clientName: 'Client 2' } }
      };
      const decision = convertEventToDecision(event);
      expect(decision).toEqual(expect.objectContaining({
        projectId: 'p2', projectName: 'Proj 2', clientName: 'Client 2', invoiceType: 'Projected Income', // Type overridden
        paymentDate: new Date('2025-06-20').toISOString(), amount: 200, reason: 'Real invoice exists',
        action: 'excluded', id: 'future-work-2', rule: 'REAL_INVOICE', // Rule set correctly
        relatedInvoice: 'Real Inv 99', relatedInvoiceDate: new Date('2025-06-18').toISOString(),
        relatedInvoiceId: '99', relatedInvoiceNumber: 'INV-99',
        formattedDescription: 'Proj 2 | Client 2'
      }));
    });

     it('should determine PAYMENT_TERMS rule', () => {
        const event: ProjectionFilterEvent = {
            invoice: { id: 'future-work-3', projectId: 'p3', projectName: 'Proj 3', date: new Date('2025-04-10'), amount: 300, metadata: { paymentTerms: 14 } },
            action: 'excluded', reason: 'Payment date 2025-04-10 is before cutoff 2025-04-24 (within payment terms of 14 days)'
        };
        const decision = convertEventToDecision(event);
        expect(decision?.rule).toBe('PAYMENT_TERMS');
        expect(decision?.paymentTermsDays).toBe(14);
        expect(decision?.cutoffDate).toBeDefined(); // Check cutoff date was added
     });

     it('should determine UNINVOICED_WORK rule', () => {
        const event: ProjectionFilterEvent = {
            invoice: { id: 'uninvoiced-4', projectId: 'p4', projectName: 'Proj 4', date: new Date('2025-07-01'), amount: 400 },
            action: 'excluded', reason: 'Projected income exists nearby'
        };
        const decision = convertEventToDecision(event);
        expect(decision?.rule).toBe('UNINVOICED_WORK');
        expect(decision?.invoiceType).toBe('Uninvoiced Work'); // Original type kept if no related invoice
     });

     it('should return null for invalid event data', () => {
        expect(convertEventToDecision(null as any)).toBeNull();
        expect(convertEventToDecision({} as any)).toBeNull();
        expect(convertEventToDecision({ invoice: null } as any)).toBeNull();
     });
  });

  // --- extractInvoiceId ---
  describe('extractInvoiceId', () => {
    it('should extract ID from prefixed strings', () => {
      expect(extractInvoiceId('harvest-invoice-12345')).toBe('12345');
      expect(extractInvoiceId('outstanding-invoice-67890')).toBe('67890');
      expect(extractInvoiceId('future-work-proj1-1')).toBe('1'); // Extracts last numeric part
      expect(extractInvoiceId('uninvoiced-proj2')).toBe('proj2'); // Returns last part if not numeric
    });
    it('should return numeric ID as is', () => {
      expect(extractInvoiceId('98765')).toBe('98765');
    });
    it('should return original string if no pattern matches', () => {
      expect(extractInvoiceId('abcde')).toBe('abcde');
      expect(extractInvoiceId('invoice-abc')).toBe('abc');
    });
     it('should return empty string for empty/null input', () => {
      expect(extractInvoiceId('')).toBe('');
      expect(extractInvoiceId(null as any)).toBe('');
      expect(extractInvoiceId(undefined as any)).toBe('');
    });
  });

  // --- generateSyntheticExclusions ---
  // describe('generateSyntheticExclusions', () => {
    // TODO: Add tests - requires careful mocking of Transaction and FilterDecision structures
  // });

  // --- determineRuleType ---
  describe('determineRuleType', () => {
    it('should return REAL_INVOICE if relatedInvoiceId exists', () => {
      const decision: FilterDecision = { relatedInvoiceId: '123' } as any;
      expect(determineRuleType(decision)).toBe('REAL_INVOICE');
    });
    it('should return PAYMENT_TERMS for payment terms reason', () => {
      const decision: FilterDecision = { reason: FILTER_RULES.PAYMENT_TERMS } as any;
      expect(determineRuleType(decision)).toBe('PAYMENT_TERMS');
      const decision2: FilterDecision = { reason: 'Payment date before cutoff' } as any;
      expect(determineRuleType(decision2)).toBe('PAYMENT_TERMS');
    });
    it('should return UNINVOICED_WORK for uninvoiced reason', () => {
      const decision: FilterDecision = { reason: FILTER_RULES.UNINVOICED_WORK } as any;
      expect(determineRuleType(decision)).toBe('UNINVOICED_WORK');
      const decision2: FilterDecision = { reason: 'Projected income exists nearby' } as any;
      expect(determineRuleType(decision2)).toBe('UNINVOICED_WORK');
       const decision3: FilterDecision = { invoiceType: 'Uninvoiced Work' } as any;
       expect(determineRuleType(decision3)).toBe('UNINVOICED_WORK');
    });
     it('should return MEETS_CRITERIA for kept actions without other matching rules', () => {
        const decision: FilterDecision = { action: 'kept', reason: 'Some other reason' } as any;
        expect(determineRuleType(decision)).toBe('MEETS_CRITERIA');
     });
      it('should return PAYMENT_TERMS as default for unknown excluded reasons', () => {
        const decision: FilterDecision = { action: 'excluded', reason: 'A completely unknown reason' } as any;
        expect(determineRuleType(decision)).toBe('PAYMENT_TERMS');
     });
      it('should prioritize explicitly set rule', () => {
        const decision: FilterDecision = { rule: 'REAL_INVOICE', reason: 'Something else' } as any;
        expect(determineRuleType(decision)).toBe('REAL_INVOICE');
      });
  });

  // --- getDecisionDescription ---
  describe('getDecisionDescription', () => {
    it('should format description using mocked util', () => {
      const decision: FilterDecision = { projectName: 'Project Omega', clientName: 'Client Omega' } as any;
      // Mock implementation from top of file: `${tx.metadata?.projectName || tx.description} | ${tx.metadata?.clientName || 'Unknown Client'}`
      expect(getDecisionDescription(decision)).toBe('Project Omega | Client Omega');
    });
  });

  // --- getTypeColorForDecision ---
  describe('getTypeColorForDecision', () => {
    it('should return green for kept decisions', () => {
      const decision: FilterDecision = { action: 'kept' } as any;
      expect(getTypeColorForDecision(decision)).toContain('green');
    });
    it('should return emerald for REAL_INVOICE excluded decisions', () => {
      const decision: FilterDecision = { action: 'excluded', relatedInvoiceId: '1' } as any;
      expect(getTypeColorForDecision(decision)).toContain('emerald');
    });
    it('should return cyan for UNINVOICED_WORK excluded decisions', () => {
      const decision: FilterDecision = { action: 'excluded', reason: FILTER_RULES.UNINVOICED_WORK } as any;
      expect(getTypeColorForDecision(decision)).toContain('cyan');
    });
    it('should return amber for PAYMENT_TERMS excluded decisions', () => {
      const decision: FilterDecision = { action: 'excluded', reason: FILTER_RULES.PAYMENT_TERMS } as any;
      expect(getTypeColorForDecision(decision)).toContain('amber');
    });
     it('should return gray as default for unknown excluded', () => {
        const decision: FilterDecision = { action: 'excluded', reason: 'unknown' } as any;
        // Currently defaults to PAYMENT_TERMS color (amber)
        expect(getTypeColorForDecision(decision)).toContain('amber');
     });
  });

  // --- processDecision ---
  // describe('processDecision', () => {
    // TODO: Add tests - requires careful setup of FilterDecision and expected ProcessedDecision
  // });

  // --- normalizeDecisionReason ---
  describe('normalizeDecisionReason', () => {
    it('should return the canonical reason text for a rule type', () => {
      const decision: FilterDecision = { relatedInvoiceId: '1' } as any; // REAL_INVOICE rule
      expect(normalizeDecisionReason(decision)).toBe(FILTER_RULES.REAL_INVOICE);
    });
  });

  // --- calculateSummary ---
  describe('calculateSummary', () => {
    it('should calculate summary correctly', () => {
      const decisions: FilterDecision[] = [
        { action: 'kept', reason: 'Meets criteria' } as any,
        { action: 'excluded', reason: FILTER_RULES.PAYMENT_TERMS } as any,
        { action: 'excluded', relatedInvoiceId: '1' } as any, // REAL_INVOICE
        { action: 'excluded', reason: FILTER_RULES.UNINVOICED_WORK } as any,
        { action: 'kept', reason: 'Another kept' } as any,
        { action: 'excluded', relatedInvoiceId: '2' } as any, // REAL_INVOICE
      ];
      const summary = calculateSummary(decisions);
      expect(summary.totalInvoices).toBe(6);
      expect(summary.keptInvoices).toBe(2);
      expect(summary.excludedInvoices).toBe(4);
      expect(summary.byReason).toEqual({
        [FILTER_RULES.MEETS_CRITERIA]: 2,
        [FILTER_RULES.PAYMENT_TERMS]: 1,
        [FILTER_RULES.REAL_INVOICE]: 2,
        [FILTER_RULES.UNINVOICED_WORK]: 1,
      });
    });
     it('should return zero counts for empty input', () => {
        const summary = calculateSummary([]);
        expect(summary.totalInvoices).toBe(0);
        expect(summary.keptInvoices).toBe(0);
        expect(summary.excludedInvoices).toBe(0);
        expect(summary.byReason).toEqual({});
     });
  });

});
