import React from 'react';
// Remove unused 'within' import
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

import TransactionsList from '@/frontend/components/ForwardProjection/TransactionsList';
import { Transaction } from '@/types/financial';

// Mock the hooks used by the component
jest.mock('@/frontend/components/ForwardProjection/ProjectionContext', () => ({
  useProjection: jest.fn(),
}));
jest.mock('@/frontend/components/ForwardProjection/hooks/useTransactions', () => ({
  useTransactions: jest.fn(),
}));

// Mock child components to isolate TransactionsList logic
jest.mock('@/frontend/components/ForwardProjection/Transactions/FilterHeader', () => () => <div data-testid="mock-filter-header">Filter Header</div>);
jest.mock('@/frontend/components/ForwardProjection/TransactionFilters', () => () => <div data-testid="mock-transaction-filters">Transaction Filters</div>);
jest.mock('@/frontend/components/ForwardProjection/Transactions/EmptyTransactions', () => () => <div data-testid="mock-empty-transactions">No Transactions</div>);
jest.mock('@/frontend/components/ForwardProjection/Transactions/TransactionCard', () => ({ transaction, isHighlighted }: { transaction: Transaction, isHighlighted: boolean }) => (
  <div data-testid={`mock-tx-card-${transaction.id}`} data-highlighted={isHighlighted}>
    {transaction.description} - Card
  </div>
));
jest.mock('@/frontend/components/ForwardProjection/Transactions/TransactionRow', () =>
  // Need React.forwardRef for the ref prop used in the component
  // Revert ref type to 'any' due to TS environment issues
  React.forwardRef(({ transaction, isHighlighted }: { transaction: Transaction, isHighlighted: boolean }, ref: any) => (
    <tr ref={ref} data-testid={`mock-tx-row-${transaction.id}`} data-highlighted={isHighlighted}>
      <td>{transaction.description} - Row</td>
    </tr>
  ))
);
// Assign display name to satisfy eslint rule if needed, but often ignored in tests
// (TransactionRow as any).displayName = 'MockTransactionRow';


describe('TransactionsList Component', () => {
  const mockTransactions: Transaction[] = [
    { id: 't1', date: new Date('2025-04-02'), amount: 100, description: 'Payment Received', type: 'payment', source: 'xero' },
    { id: 't2', date: new Date('2025-04-03'), amount: -50, description: 'Office Supplies', type: 'expense', source: 'manual' },
    { id: 't3', date: new Date('2025-04-03'), amount: 200, description: 'Invoice INV-001', type: 'invoice', source: 'harvest' },
  ];

  // Get references to the mocked hooks
  let mockedUseProjection: jest.Mock;
  let mockedUseTransactions: jest.Mock;

  beforeEach(() => {
    // Import the mocked modules
    const contextMock = jest.requireMock('@/frontend/components/ForwardProjection/ProjectionContext');
    const hooksMock = jest.requireMock('@/frontend/components/ForwardProjection/hooks/useTransactions');
    mockedUseProjection = contextMock.useProjection;
    mockedUseTransactions = hooksMock.useTransactions;

    // Reset mocks
    mockedUseProjection.mockClear();
    mockedUseTransactions.mockClear();

    // Set default return values
    mockedUseProjection.mockReturnValue({
      highlightedDate: null, // Default: no highlight
    });
    mockedUseTransactions.mockReturnValue({
      filteredTransactions: mockTransactions,
      filters: { types: [], sources: [], timeRange: [null, null], searchText: '', amountRange: [null, null] },
      setFilters: jest.fn(),
      filtersExpanded: false, // Default: filters collapsed
      setFiltersExpanded: jest.fn(),
      totalTransactions: mockTransactions.length,
    });
  });

  it('should render FilterHeader and transaction rows/cards when transactions exist', () => {
    render(<TransactionsList />);

    expect(screen.getByTestId('mock-filter-header')).toBeInTheDocument();
    expect(screen.queryByTestId('mock-transaction-filters')).not.toBeInTheDocument(); // Filters not expanded by default
    expect(screen.queryByTestId('mock-empty-transactions')).not.toBeInTheDocument();

    // Check for mocked cards (mobile view - might not be directly testable depending on CSS)
    expect(screen.getByTestId('mock-tx-card-t1')).toBeInTheDocument();
    expect(screen.getByTestId('mock-tx-card-t2')).toBeInTheDocument();
    expect(screen.getByTestId('mock-tx-card-t3')).toBeInTheDocument();

    // Check for mocked rows (desktop view)
    expect(screen.getByTestId('mock-tx-row-t1')).toBeInTheDocument();
    expect(screen.getByTestId('mock-tx-row-t2')).toBeInTheDocument();
    expect(screen.getByTestId('mock-tx-row-t3')).toBeInTheDocument();
  });

  it('should render TransactionFilters when filters are expanded', () => {
    mockedUseTransactions.mockReturnValue({
      // Use previous mock return value and override filtersExpanded
      ...mockedUseTransactions(),
      filtersExpanded: true, // Filters expanded
      filteredTransactions: mockTransactions, // Ensure transactions are still present
      totalTransactions: mockTransactions.length,
    });

    render(<TransactionsList />);

    expect(screen.getByTestId('mock-filter-header')).toBeInTheDocument();
    expect(screen.getByTestId('mock-transaction-filters')).toBeInTheDocument(); // Filters should be visible
    expect(screen.queryByTestId('mock-empty-transactions')).not.toBeInTheDocument();
  });

  it('should render EmptyTransactions when filteredTransactions is empty but totalTransactions > 0', () => {
    mockedUseTransactions.mockReturnValue({
      ...mockedUseTransactions(),
      filteredTransactions: [], // No transactions match filter
      totalTransactions: mockTransactions.length, // But there are transactions overall
    });

    render(<TransactionsList />);

    expect(screen.getByTestId('mock-filter-header')).toBeInTheDocument();
    expect(screen.getByTestId('mock-empty-transactions')).toBeInTheDocument();
    expect(screen.queryByTestId('mock-tx-card-t1')).not.toBeInTheDocument();
    expect(screen.queryByTestId('mock-tx-row-t1')).not.toBeInTheDocument();
  });

   it('should render EmptyTransactions when totalTransactions is 0', () => {
    mockedUseTransactions.mockReturnValue({
      ...mockedUseTransactions(),
      filteredTransactions: [],
      totalTransactions: 0, // No transactions at all
    });

    render(<TransactionsList />);
    expect(screen.getByTestId('mock-empty-transactions')).toBeInTheDocument();
  });


  it('should pass isHighlighted prop correctly based on highlightedDate', () => {
    const highlightDateString = '2025-04-03';
    mockedUseProjection.mockReturnValue({
      highlightedDate: highlightDateString, // Highlight this date
    });
     mockedUseTransactions.mockReturnValue({
      ...mockedUseTransactions(),
      filteredTransactions: mockTransactions, // Use original transactions
      totalTransactions: mockTransactions.length,
    });

    render(<TransactionsList />);

    // Check cards
    expect(screen.getByTestId('mock-tx-card-t1')).toHaveAttribute('data-highlighted', 'false');
    expect(screen.getByTestId('mock-tx-card-t2')).toHaveAttribute('data-highlighted', 'true'); // Matches date
    expect(screen.getByTestId('mock-tx-card-t3')).toHaveAttribute('data-highlighted', 'true'); // Matches date

    // Check rows
    expect(screen.getByTestId('mock-tx-row-t1')).toHaveAttribute('data-highlighted', 'false');
    expect(screen.getByTestId('mock-tx-row-t2')).toHaveAttribute('data-highlighted', 'true'); // Matches date
    expect(screen.getByTestId('mock-tx-row-t3')).toHaveAttribute('data-highlighted', 'true'); // Matches date
  });
});
