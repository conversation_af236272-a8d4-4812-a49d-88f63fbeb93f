import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

import Cashflow<PERSON>hart from '@/frontend/components/ForwardProjection/CashflowChart';
import { DailyCashflow } from '@/types/financial';
// useProjection is mocked via the index file, no need to import it here
// import { useProjection } from '@/frontend/components/ForwardProjection';

// Mock recharts library
jest.mock('recharts', () => {
  const MockResponsiveContainer = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
  const MockLineChart = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
  const MockCartesianGrid = () => <div data-testid="mock-cartesian-grid" />;
  const MockXAxis = () => <div data-testid="mock-x-axis" />;
  const MockYAxis = () => <div data-testid="mock-y-axis" />;
  const MockTooltip = () => <div data-testid="mock-tooltip" />; // Base Tooltip mock
  const MockLegend = () => <div data-testid="mock-legend" />;
  const MockLine = () => <div data-testid="mock-line" />;
  const MockReferenceLine = () => <div data-testid="mock-reference-line" />;

  return {
    ResponsiveContainer: MockResponsiveContainer,
    LineChart: MockLineChart,
    CartesianGrid: MockCartesianGrid,
    XAxis: MockXAxis,
    YAxis: MockYAxis,
    Tooltip: MockTooltip, // Export base mock
    Legend: MockLegend,
    Line: MockLine,
    ReferenceLine: MockReferenceLine,
    Area: () => <div data-testid="mock-area" />,
    ComposedChart: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  };
});

// Mock the index file from where the component imports its dependencies
jest.mock('@/frontend/components/ForwardProjection/index', () => ({
  useProjection: jest.fn(),
  formatChartData: jest.fn((data) => data?.dailyCashflow || []),
  // Mock ChartTooltip simply as a div, as its content won't be rendered within the base Tooltip mock
  ChartTooltip: () => <div data-testid="mock-chart-tooltip-component" />,
}));


describe('CashflowChart Component', () => {
  // Define mock data structure returned by useProjection
  const mockProjectionData = {
    dailyCashflow: [
      { date: new Date('2025-04-01'), balance: 1000, inflows: 0, outflows: 0, netFlow: 0, transactions: [] },
      { date: new Date('2025-04-02'), balance: 1100, inflows: 100, outflows: 0, netFlow: 100, transactions: [/* mock tx */] },
      { date: new Date('2025-04-03'), balance: 1050, inflows: 0, outflows: 50, netFlow: -50, transactions: [/* mock tx */] },
    ] as DailyCashflow[],
    threshold: 500,
  };

  const mockSetHighlightedDate = jest.fn();

  // Get references to the mocked functions *after* jest.mock has run
  let mockedUseProjection: jest.Mock;
  let mockedFormatChartData: jest.Mock;

  beforeEach(() => {
    // Import the mocked module to access its exports
    const indexMock = jest.requireMock('@/frontend/components/ForwardProjection/index');
    mockedUseProjection = indexMock.useProjection;
    mockedFormatChartData = indexMock.formatChartData;

    // Reset mocks
    mockSetHighlightedDate.mockClear();
    mockedUseProjection.mockClear();
    mockedFormatChartData.mockClear();

    // Set default return values
    mockedUseProjection.mockReturnValue({
      projectionData: mockProjectionData,
      setHighlightedDate: mockSetHighlightedDate,
    });
    mockedFormatChartData.mockImplementation((data) => data?.dailyCashflow || []);
  });

  it('should render the mocked chart components when data is available', () => {
    render(<CashflowChart />);
    expect(screen.getByTestId('mock-cartesian-grid')).toBeInTheDocument();
    expect(screen.getByTestId('mock-x-axis')).toBeInTheDocument();
    expect(screen.getByTestId('mock-y-axis')).toBeInTheDocument();
    // Check for the base recharts tooltip mock, not the custom content mock
    expect(screen.getByTestId('mock-tooltip')).toBeInTheDocument();
    expect(screen.getByTestId('mock-legend')).toBeInTheDocument();
    expect(screen.getAllByTestId('mock-line').length).toBeGreaterThan(0);
    expect(screen.getByTestId('mock-area')).toBeInTheDocument();
    expect(screen.getByTestId('mock-reference-line')).toBeInTheDocument();
  });

  it('should render gracefully when projectionData is null', () => {
    mockedUseProjection.mockReturnValue({
      projectionData: null,
      setHighlightedDate: mockSetHighlightedDate,
    });
    const { container } = render(<CashflowChart />);
    expect(container.firstChild).toBeNull();
  });

   it('should render gracefully when dailyCashflow data is empty', () => {
     mockedUseProjection.mockReturnValue({
       projectionData: { ...mockProjectionData, dailyCashflow: [] },
       setHighlightedDate: mockSetHighlightedDate,
     });
     render(<CashflowChart />);
     expect(screen.getByTestId('mock-cartesian-grid')).toBeInTheDocument();
   });
});
