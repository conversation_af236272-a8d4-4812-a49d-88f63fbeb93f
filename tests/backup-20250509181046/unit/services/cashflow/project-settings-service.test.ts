import { ProjectSettingsService } from '@/services/cashflow/project-settings-service';
import fs from 'fs';
import path from 'path';

// Mock the 'fs' module
jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

// Mock path.join to make assertions easier, though not strictly necessary if we control fs mocks
// jest.spyOn(path, 'join'); // Optional: if we want to assert path construction

describe('ProjectSettingsService', () => {
  let settingsService: ProjectSettingsService;
  const originalNodeEnv = process.env.NODE_ENV; // Store original NODE_ENV

  beforeEach(() => {
    // Reset mocks before each test
    jest.resetAllMocks();
    settingsService = new ProjectSettingsService();
    // Restore original NODE_ENV before each test run
    process.env.NODE_ENV = originalNodeEnv;
  });

  afterAll(() => {
    // Restore original NODE_ENV after all tests
    process.env.NODE_ENV = originalNodeEnv;
  });

  describe('getProjectSettings', () => {
    const mockSettingsData = [{ id: 'proj-a', setting: 'value1' }, { id: 'proj-b', setting: 'value2' }];
    const mockSettingsJson = JSON.stringify(mockSettingsData);
    const devDataDir = path.resolve(__dirname, '../../../../data'); // Resolve path relative to this test file
    const prodDataDir = '/data';

    it('should return settings from file in development environment', async () => {
      // Arrange
      process.env.NODE_ENV = 'development'; // Set env for test
      const expectedPath = path.join(devDataDir, 'project_settings.json');
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readFileSync.mockReturnValue(mockSettingsJson);
      const consoleSpy = jest.spyOn(console, 'log'); // Spy on console.log

      // Act
      const result = await settingsService.getProjectSettings();

      // Assert
      expect(result).toEqual(mockSettingsData);
      expect(mockFs.existsSync).toHaveBeenCalledWith(expectedPath);
      expect(mockFs.readFileSync).toHaveBeenCalledWith(expectedPath, 'utf8');
      expect(consoleSpy).toHaveBeenCalledWith(`Loaded ${mockSettingsData.length} project settings for projection calculations`);
      consoleSpy.mockRestore(); // Clean up spy
    });

    it('should return settings from file in production environment', async () => {
      // Arrange
      process.env.NODE_ENV = 'production'; // Set env for test
      const expectedPath = path.join(prodDataDir, 'project_settings.json');
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readFileSync.mockReturnValue(mockSettingsJson);
      const consoleSpy = jest.spyOn(console, 'log');

      // Act
      const result = await settingsService.getProjectSettings();

      // Assert
      expect(result).toEqual(mockSettingsData);
      expect(mockFs.existsSync).toHaveBeenCalledWith(expectedPath);
      expect(mockFs.readFileSync).toHaveBeenCalledWith(expectedPath, 'utf8');
      expect(consoleSpy).toHaveBeenCalledWith(`Loaded ${mockSettingsData.length} project settings for projection calculations`);
      consoleSpy.mockRestore();
    });

    it('should return an empty array if the settings file does not exist', async () => {
      // Arrange
      process.env.NODE_ENV = 'development'; // Use dev path for consistency
      const expectedPath = path.join(devDataDir, 'project_settings.json');
      mockFs.existsSync.mockReturnValue(false);
      const consoleSpy = jest.spyOn(console, 'log');

      // Act
      const result = await settingsService.getProjectSettings();

      // Assert
      expect(result).toEqual([]);
      expect(mockFs.existsSync).toHaveBeenCalledWith(expectedPath);
      expect(mockFs.readFileSync).not.toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith('No project settings file found - using empty settings array');
      consoleSpy.mockRestore();
    });

    it('should return an empty array and handle error if JSON parsing fails', async () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const expectedPath = path.join(devDataDir, 'project_settings.json');
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readFileSync.mockReturnValue('{invalid json'); // Malformed JSON
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(); // Suppress console.error output

      // Act
      const result = await settingsService.getProjectSettings();

      // Assert
      expect(result).toEqual([]); // Fallback value due to error handling
      expect(mockFs.existsSync).toHaveBeenCalledWith(expectedPath);
      expect(mockFs.readFileSync).toHaveBeenCalledWith(expectedPath, 'utf8');
      expect(consoleErrorSpy).toHaveBeenCalled(); // Error should be logged by withErrorHandling
      consoleErrorSpy.mockRestore();
    });

    it('should return an empty array and handle error if readFileSync throws', async () => {
        // Arrange
        process.env.NODE_ENV = 'development';
        const expectedPath = path.join(devDataDir, 'project_settings.json');
        mockFs.existsSync.mockReturnValue(true);
        const readError = new Error('Disk read error');
        mockFs.readFileSync.mockImplementation(() => { throw readError; });
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(); // Suppress console.error output

        // Act
        const result = await settingsService.getProjectSettings();

        // Assert
        expect(result).toEqual([]); // Fallback value
        expect(mockFs.existsSync).toHaveBeenCalledWith(expectedPath);
        expect(mockFs.readFileSync).toHaveBeenCalledWith(expectedPath, 'utf8');
        expect(consoleErrorSpy).toHaveBeenCalledWith(expect.stringContaining('getProjectSettings failed'), readError);
        consoleErrorSpy.mockRestore();
      });
  });
});
