import { TransactionService } from '@/services/cashflow/transaction-service';
import { Transaction } from '@/types/financial';

describe('TransactionService', () => {
  let transactionService: TransactionService;

  beforeEach(() => {
    transactionService = new TransactionService();
    // Mock Date.now() and Math.random() for predictable IDs if needed, but usually not necessary for unit tests unless IDs are critical for logic being tested.
    // jest.spyOn(Date, 'now').mockReturnValue(1700000000000);
    // jest.spyOn(Math, 'random').mockReturnValue(0.5);
  });

  afterEach(() => {
    // Restore mocks if Date/Math were mocked
    // jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(transactionService).toBeDefined();
  });

  // --- Test Cases for transformExpensesToTransactions ---
  describe('transformExpensesToTransactions', () => {
    it('should transform valid expense data correctly', () => {
      const rawExpenses = [
        { id: 'exp1', type: 'expense', reference: 'Office Supplies', date: new Date('2025-04-10'), source: 'Manual', amount: 150.75 },
        { name: 'Software Subscription', date: new Date('2025-04-15'), amount: 49.99 }, // Missing id, type, reference, source
      ];
      const expectedTransactions: Transaction[] = [
        { id: 'exp1', type: 'expense', description: 'Office Supplies', date: new Date('2025-04-10'), source: 'Manual', amount: 150.75 },
        expect.objectContaining({ // Use objectContaining for generated ID
          type: 'expense', description: 'Software Subscription', date: new Date('2025-04-15'), source: 'Expense', amount: 49.99
        }),
      ];

      const result = transactionService.transformExpensesToTransactions(rawExpenses);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(expectedTransactions[0]);
      expect(result[1]).toEqual(expectedTransactions[1]);
      expect(result[1].id).toMatch(/^expense-\d+-\d+(\.\d+)?$/); // Check generated ID format
    });

    it('should return an empty array for empty input', () => {
      expect(transactionService.transformExpensesToTransactions([])).toEqual([]);
      expect(transactionService.transformExpensesToTransactions(null as any)).toEqual([]); // Handle null input
      expect(transactionService.transformExpensesToTransactions(undefined as any)).toEqual([]); // Handle undefined input
    });

    it('should handle missing or invalid fields with defaults', () => {
      const rawExpenses = [
        { reference: 'Lunch', date: 'invalid-date' }, // Invalid date, missing amount
        { amount: 'not-a-number' } // Invalid amount
      ];
      const result = transactionService.transformExpensesToTransactions(rawExpenses);

      expect(result).toHaveLength(2);
      // Check first item defaults
      expect(result[0].description).toBe('Lunch');
      expect(result[0].date).toBeInstanceOf(Date); // Should default to new Date()
      expect(result[0].amount).toBe(0); // Should default to 0
      expect(result[0].type).toBe('expense');
      expect(result[0].source).toBe('Expense');
      expect(result[0].id).toMatch(/^expense-\d+-\d+(\.\d+)?$/);

      // Check second item defaults
      expect(result[1].description).toBe('Expense'); // Default description
      expect(result[1].date).toBeInstanceOf(Date);
      expect(result[1].amount).toBe(0); // Default amount
      expect(result[1].id).toMatch(/^expense-\d+-\d+(\.\d+)?$/);
    });
  });

  // --- Test Cases for transformBillsToTransactions ---
  describe('transformBillsToTransactions', () => {
    it('should transform valid bill data correctly', () => {
      const rawBills = [
        { id: 'bill1', type: 'repeating_bill', reference: 'Rent', date: new Date('2025-05-01'), amount: 2500 },
        { reference: 'Utilities', date: new Date('2025-05-05'), amount: 300.50 }, // Missing id, type
      ];
      const expectedTransactions: Transaction[] = [
        { id: 'bill1', type: 'bill', description: 'Rent', date: new Date('2025-05-01'), source: 'xero', amount: 2500, metadata: { originalType: 'repeating_bill' } },
        expect.objectContaining({
          type: 'bill', description: 'Utilities', date: new Date('2025-05-05'), source: 'xero', amount: 300.50, metadata: { originalType: 'repeating_bill' }
        }),
      ];

      const result = transactionService.transformBillsToTransactions(rawBills);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(expectedTransactions[0]);
      expect(result[1]).toEqual(expectedTransactions[1]);
      expect(result[1].id).toMatch(/^bill-\d+-\d+(\.\d+)?$/);
    });

     it('should return an empty array for empty input', () => {
      expect(transactionService.transformBillsToTransactions([])).toEqual([]);
      expect(transactionService.transformBillsToTransactions(null as any)).toEqual([]);
      expect(transactionService.transformBillsToTransactions(undefined as any)).toEqual([]);
    });

    it('should handle missing or invalid fields with defaults', () => {
      const rawBills = [
        { date: 'invalid-date' }, // Missing reference, amount, invalid date
        { amount: 'not-a-number', type: 'ACCPAY' } // Invalid amount, different type
      ];
      const result = transactionService.transformBillsToTransactions(rawBills);

      expect(result).toHaveLength(2);
      // Check first item defaults
      expect(result[0].description).toBe('Repeating Bill');
      expect(result[0].date).toBeInstanceOf(Date);
      expect(result[0].amount).toBe(0);
      expect(result[0].type).toBe('bill');
      expect(result[0].source).toBe('xero');
      expect(result[0].metadata).toEqual({ originalType: 'repeating_bill' });
      expect(result[0].id).toMatch(/^bill-\d+-\d+(\.\d+)?$/);

      // Check second item defaults
      expect(result[1].description).toBe('Repeating Bill');
      expect(result[1].date).toBeInstanceOf(Date);
      expect(result[1].amount).toBe(0);
      expect(result[1].metadata).toEqual({ originalType: 'ACCPAY' }); // Preserves original type
      expect(result[1].id).toMatch(/^bill-\d+-\d+(\.\d+)?$/);
    });
  });

  // --- Test Cases for transformInvoicesToTransactions ---
  describe('transformInvoicesToTransactions', () => {
    it('should transform valid invoice data correctly', () => {
      const rawInvoices = [
        { id: 'inv1', what: 'Project Alpha', date: new Date('2025-04-20'), amount: 5000, metadata: { client: 'Client A' } },
        { what: 'Consulting Services', date: new Date('2025-04-25'), amount: 1200 }, // Missing id, metadata
      ];
      const expectedTransactions: Transaction[] = [
        { id: 'inv1', type: 'invoice', description: 'Project Alpha', date: new Date('2025-04-20'), source: 'harvest', amount: 5000, metadata: { client: 'Client A' } },
        expect.objectContaining({
          type: 'invoice', description: 'Consulting Services', date: new Date('2025-04-25'), source: 'harvest', amount: 1200, metadata: {}
        }),
      ];

      const result = transactionService.transformInvoicesToTransactions(rawInvoices);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(expectedTransactions[0]);
      expect(result[1]).toEqual(expectedTransactions[1]);
      expect(result[1].id).toMatch(/^invoice-\d+-\d+(\.\d+)?$/);
    });

    it('should return an empty array for empty input', () => {
      expect(transactionService.transformInvoicesToTransactions([])).toEqual([]);
      expect(transactionService.transformInvoicesToTransactions(null as any)).toEqual([]);
      expect(transactionService.transformInvoicesToTransactions(undefined as any)).toEqual([]);
    });

    it('should handle missing or invalid fields with defaults', () => {
      const rawInvoices = [
        { date: 'invalid-date' }, // Missing what, amount, invalid date
        { amount: 'not-a-number', metadata: null } // Invalid amount, null metadata
      ];
      const result = transactionService.transformInvoicesToTransactions(rawInvoices);

      expect(result).toHaveLength(2);
      // Check first item defaults
      expect(result[0].description).toBe('Invoice');
      expect(result[0].date).toBeInstanceOf(Date);
      expect(result[0].amount).toBe(0);
      expect(result[0].type).toBe('invoice');
      expect(result[0].source).toBe('harvest');
      expect(result[0].metadata).toEqual({});
      expect(result[0].id).toMatch(/^invoice-\d+-\d+(\.\d+)?$/);

      // Check second item defaults
      expect(result[1].description).toBe('Invoice');
      expect(result[1].date).toBeInstanceOf(Date);
      expect(result[1].amount).toBe(0);
      expect(result[1].metadata).toEqual({}); // Defaults to empty object if metadata is null
      expect(result[1].id).toMatch(/^invoice-\d+-\d+(\.\d+)?$/);
    });
  });
});
