import { ExpenseService } from '@/services/cashflow/expense-service';
import { CustomExpense } from '@/types/financial';

describe('ExpenseService', () => {
  let expenseService: ExpenseService;

  beforeEach(() => {
    // No dependencies to mock for this service based on source code
    expenseService = new ExpenseService();
  });

  it('should be defined', () => {
    expect(expenseService).toBeDefined();
  });

  // --- Test Cases for ExpenseService methods ---

  describe('calculateExpenseOccurrences', () => {
    const startDate = new Date('2025-04-01');
    const endDate = new Date('2025-06-30');

    it('should calculate monthly occurrences correctly', () => {
      const expense: CustomExpense = { id: 'm1', name: 'Monthly Rent', type: 'Other', amount: 1000, date: new Date('2025-03-15'), frequency: 'monthly' };
      const occurrences = expenseService.calculateExpenseOccurrences(expense, startDate, endDate);
      expect(occurrences).toEqual([
        new Date('2025-04-15'),
        new Date('2025-05-15'),
        new Date('2025-06-15'),
      ]);
    });

    it('should calculate weekly occurrences correctly', () => {
      const expense: CustomExpense = { id: 'w1', name: 'Weekly Snacks', type: 'Other', amount: 50, date: new Date('2025-03-28'), frequency: 'weekly' }; // A Friday
      const occurrences = expenseService.calculateExpenseOccurrences(expense, startDate, endDate);
      // Expecting Fridays in April, May, June starting from Apr 4
      expect(occurrences).toEqual([
        new Date('2025-04-04'), new Date('2025-04-11'), new Date('2025-04-18'), new Date('2025-04-25'),
        new Date('2025-05-02'), new Date('2025-05-09'), new Date('2025-05-16'), new Date('2025-05-23'), new Date('2025-05-30'),
        new Date('2025-06-06'), new Date('2025-06-13'), new Date('2025-06-20'), new Date('2025-06-27'),
      ]);
    });

    it('should calculate quarterly occurrences correctly', () => {
       const expense: CustomExpense = { id: 'q1', name: 'Quarterly Tax', type: 'Taxes', amount: 5000, date: new Date('2025-01-20'), frequency: 'quarterly' };
       const occurrences = expenseService.calculateExpenseOccurrences(expense, startDate, endDate);
       expect(occurrences).toEqual([
         new Date('2025-04-20'), // Jan + 3 months
         // July 20 would be next, which is outside the range
       ]);
    });

    it('should handle one-off occurrences correctly', () => {
      const expenseInside: CustomExpense = { id: 'o1', name: 'One-off Purchase', type: 'Other', amount: 200, date: new Date('2025-05-10'), frequency: 'one-off' };
      const expenseOutside: CustomExpense = { id: 'o2', name: 'Another One-off', type: 'Other', amount: 300, date: new Date('2025-07-10'), frequency: 'one-off' };
      const occurrencesInside = expenseService.calculateExpenseOccurrences(expenseInside, startDate, endDate);
      const occurrencesOutside = expenseService.calculateExpenseOccurrences(expenseOutside, startDate, endDate);
      expect(occurrencesInside).toEqual([new Date('2025-05-10')]);
      expect(occurrencesOutside).toEqual([]);
    });

     it('should handle expense start date within the range', () => {
      const expense: CustomExpense = { id: 'm2', name: 'New Subscription', type: 'Software', amount: 100, date: new Date('2025-05-05'), frequency: 'monthly' };
      const occurrences = expenseService.calculateExpenseOccurrences(expense, startDate, endDate);
      expect(occurrences).toEqual([
        new Date('2025-05-05'),
        new Date('2025-06-05'),
      ]);
    });
  });

  describe('generateCustomExpenseTransactions', () => {
     it('should generate correct transaction objects for calculated occurrences', () => {
        // Arrange
        const startDate = new Date('2025-04-01');
        const endDate = new Date('2025-04-30');
        const customExpenses: CustomExpense[] = [
          { id: 'm1', name: 'Monthly Rent', type: 'Other', amount: 1000, date: new Date('2025-03-15'), frequency: 'monthly' }, // Occurs Apr 15
          { id: 'w1', name: 'Weekly Snacks', type: 'Other', amount: 50, date: new Date('2025-03-28'), frequency: 'weekly' }, // Occurs Apr 4, 11, 18, 25
          { id: 'o1', name: 'One-off', type: 'Fees', amount: 200, date: new Date('2025-07-10'), frequency: 'one-off' }, // Outside range
        ];

        // Act
        const transactions = expenseService.generateCustomExpenseTransactions(startDate, endDate, customExpenses);

        // Assert
        expect(transactions).toHaveLength(5); // 1 monthly + 4 weekly

        // Check monthly rent transaction
        const rentTx = transactions.find(t => t.reference === 'Monthly Rent');
        expect(rentTx).toBeDefined();
        expect(rentTx).toEqual(expect.objectContaining({
          id: expect.stringContaining('custom-expense-m1'),
          type: 'custom_expense_Other',
          name: 'Monthly Rent',
          date: new Date('2025-04-15'),
          source: 'Custom Expense (Other)',
          amount: -1000, // Ensure amount is negative
        }));

        // Check weekly snacks transactions
        const snackTxs = transactions.filter(t => t.reference === 'Weekly Snacks');
        expect(snackTxs).toHaveLength(4);
        expect(snackTxs.map(t => t.date)).toEqual([
          new Date('2025-04-04'),
          new Date('2025-04-11'),
          new Date('2025-04-18'),
          new Date('2025-04-25'),
        ]);
        snackTxs.forEach(tx => {
          expect(tx.amount).toBe(-50);
          expect(tx.type).toBe('custom_expense_Other');
          expect(tx.source).toBe('Custom Expense (Other)');
        });

        // Ensure one-off outside range was not included
        expect(transactions.find(t => t.reference === 'One-off')).toBeUndefined();
     });

     it('should return an empty array if no expenses occur in the range', () => {
        // Arrange
        const startDate = new Date('2025-04-01');
        const endDate = new Date('2025-04-30');
         const customExpenses: CustomExpense[] = [
          { id: 'm1', name: 'Monthly Rent', type: 'Other', amount: 1000, date: new Date('2025-05-15'), frequency: 'monthly' }, // Occurs May 15
          { id: 'o1', name: 'One-off', type: 'Fees', amount: 200, date: new Date('2025-07-10'), frequency: 'one-off' }, // Outside range
        ];

         // Act
        const transactions = expenseService.generateCustomExpenseTransactions(startDate, endDate, customExpenses);

        // Assert
        expect(transactions).toHaveLength(0);
     });
  });
});
