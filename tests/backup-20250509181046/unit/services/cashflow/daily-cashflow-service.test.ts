import { DailyCashflowService } from '@/services/cashflow/daily-cashflow-service';
import { Transaction } from '@/types/financial'; // Assuming types are here

// Mock dependencies if needed (e.g., other services)
// jest.mock('@/services/cashflow/transaction-service');

describe('DailyCashflowService', () => {
  let dailyCashflowService: DailyCashflowService;

  beforeEach(() => {
    // Initialize the service before each test
    // May need mock dependencies injected
    dailyCashflowService = new DailyCashflowService(/* pass mocks if needed */);
  });

  it('should be defined', () => {
    expect(dailyCashflowService).toBeDefined();
  });

  // --- Test Cases for calculateDailyCashflow ---

  describe('calculateDailyCashflow', () => {
    // TODO: Add tests for the core daily cashflow calculation logic:
    // 1. Correctly calculates daily inflows, outflows, net flow, and balance.
    // 2. Handles empty transaction arrays.
    // 3. Handles date ranges correctly (start/end dates).
    // 4. Integrates starting balance correctly.
    // 5. Groups transactions by day accurately.

    it('should calculate daily balances correctly with simple transactions', () => {
      // Arrange
      const transactions: Transaction[] = [
        { id: 't1', date: new Date('2025-04-02'), amount: 100, description: 'Inflow 1', type: 'payment', source: 'xero' },
        { id: 't2', date: new Date('2025-04-03'), amount: -50, description: 'Outflow 1', type: 'expense', source: 'manual' },
        { id: 't3', date: new Date('2025-04-03'), amount: 200, description: 'Inflow 2', type: 'payment', source: 'xero' },
      ];
      const startDate = new Date('2025-04-01');
      const endDate = new Date('2025-04-05');
      const startingBalance = 1000;

      // Act
      const result = dailyCashflowService.generateDailyCashflow(transactions, startingBalance, startDate, endDate); // Corrected method name and parameter order

      // Assert
      expect(result).toHaveLength(5); // 5 days from Apr 1 to Apr 5 inclusive
      // Day 1 (Apr 1)
      expect(result[0].date).toEqual(startDate);
      expect(result[0].balance).toBe(startingBalance);
      expect(result[0].inflows).toBe(0);
      expect(result[0].outflows).toBe(0);
      // Day 2 (Apr 2)
      expect(result[1].date).toEqual(new Date('2025-04-02'));
      expect(result[1].balance).toBe(1100); // 1000 + 100
      expect(result[1].inflows).toBe(100);
      expect(result[1].outflows).toBe(0);
      expect(result[1].transactions).toHaveLength(1);
      // Day 3 (Apr 3)
      expect(result[2].date).toEqual(new Date('2025-04-03'));
      expect(result[2].balance).toBe(1250); // 1100 - 50 + 200
      expect(result[2].inflows).toBe(200);
      expect(result[2].outflows).toBe(50); // Outflows should be positive
      expect(result[2].transactions).toHaveLength(2);
       // Day 4 (Apr 4)
      expect(result[3].date).toEqual(new Date('2025-04-04'));
      expect(result[3].balance).toBe(1250); // No change
      expect(result[3].inflows).toBe(0);
      expect(result[3].outflows).toBe(0);
       // Day 5 (Apr 5)
      expect(result[4].date).toEqual(endDate);
      expect(result[4].balance).toBe(1250); // No change
      expect(result[4].inflows).toBe(0);
      expect(result[4].outflows).toBe(0);

    });

    it('should handle an empty transaction list', () => {
       // Arrange
      const transactions: Transaction[] = [];
      const startDate = new Date('2025-04-01');
      const endDate = new Date('2025-04-03');
      const startingBalance = 500;

      // Act
      const result = dailyCashflowService.generateDailyCashflow(transactions, startingBalance, startDate, endDate); // Corrected method name and parameter order

      // Assert
      expect(result).toHaveLength(3);
      expect(result[0].balance).toBe(startingBalance);
      expect(result[1].balance).toBe(startingBalance);
      expect(result[2].balance).toBe(startingBalance);
      expect(result.every(d => d.inflows === 0 && d.outflows === 0)).toBe(true);
    });

    it('should ignore transactions outside the specified date range', () => {
      // Arrange
      const transactions: Transaction[] = [
        // Before start date (should be ignored)
        { id: 't0', date: new Date('2025-03-31'), amount: 1000, description: 'Too early', type: 'payment', source: 'xero' },
        // Within range
        { id: 't1', date: new Date('2025-04-01'), amount: 100, description: 'Inflow 1', type: 'payment', source: 'xero' },
        { id: 't2', date: new Date('2025-04-02'), amount: -50, description: 'Outflow 1', type: 'expense', source: 'manual' },
        // After end date (should be ignored)
        { id: 't3', date: new Date('2025-04-04'), amount: 200, description: 'Too late', type: 'payment', source: 'xero' },
      ];
      const startDate = new Date('2025-04-01');
      const endDate = new Date('2025-04-03');
      const startingBalance = 1000;

      // Act
      const result = dailyCashflowService.generateDailyCashflow(transactions, startingBalance, startDate, endDate);

      // Assert
      expect(result).toHaveLength(3); // Apr 1, 2, 3
      // Day 1 (Apr 1) - Includes t1
      expect(result[0].balance).toBe(1100); // 1000 + 100
      expect(result[0].inflows).toBe(100);
      expect(result[0].outflows).toBe(0);
      expect(result[0].transactions).toHaveLength(1);
      expect(result[0].transactions[0].id).toBe('t1');
      // Day 2 (Apr 2) - Includes t2
      expect(result[1].balance).toBe(1050); // 1100 - 50
      expect(result[1].inflows).toBe(0);
      expect(result[1].outflows).toBe(50);
      expect(result[1].transactions).toHaveLength(1);
      expect(result[1].transactions[0].id).toBe('t2');
      // Day 3 (Apr 3) - No transactions within range
      expect(result[2].balance).toBe(1050); // No change
      expect(result[2].inflows).toBe(0);
      expect(result[2].outflows).toBe(0);
      expect(result[2].transactions).toHaveLength(0);
    });

    it('should handle a negative starting balance correctly', () => {
      // Arrange
      const transactions: Transaction[] = [
        { id: 't1', date: new Date('2025-04-01'), amount: 100, description: 'Inflow 1', type: 'payment', source: 'xero' },
        { id: 't2', date: new Date('2025-04-02'), amount: -50, description: 'Outflow 1', type: 'expense', source: 'manual' },
      ];
      const startDate = new Date('2025-04-01');
      const endDate = new Date('2025-04-03');
      const startingBalance = -200; // Negative start

      // Act
      const result = dailyCashflowService.generateDailyCashflow(transactions, startingBalance, startDate, endDate);

      // Assert
      expect(result).toHaveLength(3);
      // Day 1 (Apr 1)
      expect(result[0].balance).toBe(-100); // -200 + 100
      expect(result[0].inflows).toBe(100);
      expect(result[0].outflows).toBe(0);
      // Day 2 (Apr 2)
      expect(result[1].balance).toBe(-150); // -100 - 50
      expect(result[1].inflows).toBe(0);
      expect(result[1].outflows).toBe(50);
      // Day 3 (Apr 3)
      expect(result[2].balance).toBe(-150); // No change
      expect(result[2].inflows).toBe(0);
      expect(result[2].outflows).toBe(0);
    });

    it('should correctly aggregate multiple transactions on the same day', () => {
      // Arrange
      const transactions: Transaction[] = [
        { id: 't1', date: new Date('2025-04-02'), amount: 100, description: 'Inflow 1', type: 'payment', source: 'xero' },
        { id: 't2', date: new Date('2025-04-02'), amount: -30, description: 'Outflow 1', type: 'expense', source: 'manual' },
        { id: 't3', date: new Date('2025-04-02'), amount: 200, description: 'Inflow 2', type: 'payment', source: 'xero' },
        { id: 't4', date: new Date('2025-04-03'), amount: -70, description: 'Outflow 2', type: 'expense', source: 'manual' },
      ];
      const startDate = new Date('2025-04-01');
      const endDate = new Date('2025-04-03');
      const startingBalance = 1000;

      // Act
      const result = dailyCashflowService.generateDailyCashflow(transactions, startingBalance, startDate, endDate);

      // Assert
      expect(result).toHaveLength(3);
      // Day 1 (Apr 1)
      expect(result[0].balance).toBe(1000);
      expect(result[0].inflows).toBe(0);
      expect(result[0].outflows).toBe(0);
      expect(result[0].transactions).toHaveLength(0);
      // Day 2 (Apr 2) - Aggregated t1, t2, t3
      expect(result[1].date).toEqual(new Date('2025-04-02'));
      expect(result[1].balance).toBe(1270); // 1000 + 100 - 30 + 200
      expect(result[1].inflows).toBe(300); // 100 + 200
      expect(result[1].outflows).toBe(30);
      expect(result[1].transactions).toHaveLength(3);
      expect(result[1].transactions.map(t => t.id).sort()).toEqual(['t1', 't2', 't3']);
      // Day 3 (Apr 3) - Includes t4
      expect(result[2].date).toEqual(new Date('2025-04-03'));
      expect(result[2].balance).toBe(1200); // 1270 - 70
      expect(result[2].inflows).toBe(0);
      expect(result[2].outflows).toBe(70);
      expect(result[2].transactions).toHaveLength(1);
      expect(result[2].transactions[0].id).toBe('t4');
    });

    // Add more tests for edge cases:
    // - Transactions exactly on start/end dates (covered)

  });

  // --- Add tests for other public methods of DailyCashflowService ---
});
