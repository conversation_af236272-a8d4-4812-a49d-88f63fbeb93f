import { HarvestInvoiceService } from '@/services/harvest/invoice-service';
import { HarvestClient } from '@/api/integrations/harvest'; // Import the dependency
import { jest } from '@jest/globals'; // Import Jest types for mocking

// Mock the HarvestClient dependency
jest.mock('@/api/integrations/harvest');
const MockHarvestClient = HarvestClient as jest.MockedClass<typeof HarvestClient>;

describe('HarvestInvoiceService', () => {
  let invoiceService: HarvestInvoiceService;
  let mockHarvestClient: jest.Mocked<HarvestClient>; // Typed mock instance

  beforeEach(() => {
    // Clear previous mock usage data
    MockHarvestClient.mockClear();
    // Create a new mock instance for each test
    mockHarvestClient = new MockHarvestClient() as jest.Mocked<HarvestClient>;
    // Initialize service with the mock client
    invoiceService = new HarvestInvoiceService(mockHarvestClient);
  });

  it('should be defined', () => {
    expect(invoiceService).toBeDefined();
  });

  // --- Test Cases for HarvestInvoiceService methods ---

  describe('getOpenInvoicesAsTransactions', () => {
    // Rename method to match service implementation
    describe('getInvoices', () => {
      it('should fetch open invoices and transform them into transactions', async () => {
        // Arrange
        const startDate = new Date('2025-04-01');
        const endDate = new Date('2025-04-30');
        // More complete mock data matching HarvestInvoice type
        const mockApiResponse: any[] = [ // Use any[] for mock flexibility, or define a partial type
          {
            id: 123, number: 'INV-001', client: { id: 1, name: 'Client A' }, amount: 1000, state: 'open', issue_date: '2025-03-15', due_date: '2025-04-14', subject: 'Project X Work', line_items: [{ id: 1, project: { id: 10, name: 'Project X' }, kind: 'Service', description: '', quantity: 1, unit_price: 1000, amount: 1000, taxed: false, taxed2: false }],
            purchase_order: '', due_amount: 1000, tax: 0, tax_amount: 0, tax2: 0, tax2_amount: 0, discount: 0, discount_amount: 0, notes: '', currency: 'AUD', period_start: '', period_end: '', payment_term: '', sent_at: '', paid_at: null, paid_date: null, closed_at: null, created_at: '', updated_at: ''
          },
          {
            id: 456, number: 'INV-002', client: { id: 2, name: 'Client B' }, amount: 2000, state: 'paid', issue_date: '2025-03-20', due_date: '2025-04-19', subject: 'Project Y Work', line_items: [{ id: 2, project: { id: 20, name: 'Project Y' }, kind: 'Service', description: '', quantity: 1, unit_price: 2000, amount: 2000, taxed: false, taxed2: false }], // Paid, should be filtered out by service
            purchase_order: '', due_amount: 0, tax: 0, tax_amount: 0, tax2: 0, tax2_amount: 0, discount: 0, discount_amount: 0, notes: '', currency: 'AUD', period_start: '', period_end: '', payment_term: '', sent_at: '', paid_at: '2025-04-20', paid_date: '2025-04-20', closed_at: '2025-04-20', created_at: '', updated_at: ''
          },
          {
            id: 789, number: 'INV-003', client: { id: 1, name: 'Client A' }, amount: 1500, state: 'open', issue_date: '2025-03-25', due_date: '2025-04-24', subject: 'Project Z Work', line_items: [{ id: 3, project: { id: 30, name: 'Project Z' }, kind: 'Service', description: '', quantity: 1, unit_price: 1500, amount: 1500, taxed: false, taxed2: false }],
            purchase_order: '', due_amount: 1500, tax: 0, tax_amount: 0, tax2: 0, tax2_amount: 0, discount: 0, discount_amount: 0, notes: '', currency: 'AUD', period_start: '', period_end: '', payment_term: '', sent_at: '', paid_at: null, paid_date: null, closed_at: null, created_at: '', updated_at: ''
          },
        ];
        // Mock the getInvoices method on the mocked client instance
        // Use 'as any' to bypass strict type checking for the mock implementation if needed,
        // although ideally the mock data should fully conform.
        mockHarvestClient.getInvoices.mockResolvedValue(mockApiResponse as any);

        // Act
        const result = await invoiceService.getInvoices(startDate, endDate);

        // Assert
        expect(mockHarvestClient.getInvoices).toHaveBeenCalledTimes(1);
        expect(result).toHaveLength(2); // Only the two 'open' invoices
        expect(result[0].id).toBe('outstanding-invoice-123');
        expect(result[0].source).toBe('harvest');
        expect(result[0].type).toBe('invoice');
        expect(result[0].amount).toBe(1000);
        expect(result[0].date).toEqual(new Date('2025-04-14')); // Should use due_date
        expect(result[0].metadata?.clientName).toBe('Client A');
        expect(result[1].id).toBe('outstanding-invoice-789');
        expect(result[1].amount).toBe(1500);
        expect(result[1].date).toEqual(new Date('2025-04-24'));
        expect(result[1].metadata?.clientName).toBe('Client A');
        expect(result[1].metadata?.projectName).toBe('Project Z');
        expect(result[1].metadata?.projectId).toBe('30');
        expect(result[1].metadata?.invoiceState).toBe('open');
        expect(result[1].metadata?.issue_date).toBe('2025-03-25');
        expect(result[1].metadata?.harvestUrl).toBe('https://onbord.harvestapp.com/invoices/789');
        expect(result[1].metadata?.invoiceNumber).toBe('INV-003');
      });

      it('should return an empty array when the API returns no invoices', async () => {
        // Arrange
        const startDate = new Date('2025-04-01');
        const endDate = new Date('2025-04-30');
        mockHarvestClient.getInvoices.mockResolvedValue([]); // API returns empty array

        // Act
        const result = await invoiceService.getInvoices(startDate, endDate);

        // Assert
        expect(mockHarvestClient.getInvoices).toHaveBeenCalledTimes(1);
        expect(result).toEqual([]);
      });

      it('should handle API errors gracefully', async () => {
        // Arrange
        const startDate = new Date('2025-04-01');
        const endDate = new Date('2025-04-30');
        // Mock the getInvoices method to throw an error
        mockHarvestClient.getInvoices.mockRejectedValue(new Error('Harvest API Error'));

        // Act
        const result = await invoiceService.getInvoices(startDate, endDate);

        // Assert
        expect(result).toEqual([]); // Should return empty array on error due to withErrorHandling
        // Optionally mock console.error and assert it was called
      });
    });
  });

  // Add tests for other public methods if any
});
