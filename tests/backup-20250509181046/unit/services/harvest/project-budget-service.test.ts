import { HarvestProjectBudgetService } from '@/services/harvest/project-budget-service';
import { HarvestClient } from '@/api/integrations/harvest'; // Import the dependency
import { jest } from '@jest/globals'; // Import Jest types for mocking

// Mock the HarvestClient dependency
jest.mock('@/api/integrations/harvest');
const MockHarvestClient = HarvestClient as jest.MockedClass<typeof HarvestClient>;

import { HarvestProject, UninvoicedResult, ProjectBudgetResult, HarvestApiResponse } from '@/api/integrations/harvest'; // Import necessary types

describe('HarvestProjectBudgetService', () => {
  let budgetService: HarvestProjectBudgetService;
  let mockHarvestClient: jest.Mocked<HarvestClient>;

  beforeEach(() => {
    MockHarvestClient.mockClear();
    // Ensure the mock client has all methods used by the service
    mockHarvestClient = {
      getProjectBudgetReport: jest.fn(),
      getProjects: jest.fn(),
      getUninvoicedReport: jest.fn(),
      getInvoices: jest.fn(), // Add getInvoices for getOutstandingInvoices tests
    } as unknown as jest.Mocked<HarvestClient>;
    budgetService = new HarvestProjectBudgetService(mockHarvestClient);
  });

  it('should be defined', () => {
    expect(budgetService).toBeDefined();
  });

  // --- Test Cases for HarvestProjectBudgetService methods ---

  describe('getProjectBudgets', () => {
    it('should fetch and combine data from budget, projects, and uninvoiced reports', async () => {
      // Arrange
      const mockBudgetResponse: HarvestApiResponse<ProjectBudgetResult> = {
        results: [
          { project_id: 10, project_name: 'Project X', client_id: 1, client_name: 'Client A', budget_is_monthly: false, budget_by: 'project', is_active: true, budget: 10000, budget_spent: 5000, budget_remaining: 5000 },
          { project_id: 20, project_name: 'Project Y', client_id: 2, client_name: 'Client B', budget_is_monthly: false, budget_by: 'project', is_active: true, budget: 20000, budget_spent: 18000, budget_remaining: 2000 },
          { project_id: 30, project_name: 'Project Z', client_id: 1, client_name: 'Client A', budget_is_monthly: false, budget_by: 'project', is_active: false, budget: 5000, budget_spent: 5000, budget_remaining: 0 }, // Inactive, should be filtered later if only active needed
        ],
        total_pages: 1, total_entries: 3, page: 1,
      };
      const mockProjectsResponse: HarvestProject[] = [
        { id: 10, client_id: 1, name: 'Project X', code: '', is_active: true, is_billable: true, is_fixed_fee: false, bill_by: '', budget: 10000, budget_by: 'project', budget_is_monthly: false, starts_on: '2025-01-01', ends_on: '2025-12-31', created_at: '', updated_at: '' },
        { id: 20, client_id: 2, name: 'Project Y', code: '', is_active: true, is_billable: true, is_fixed_fee: false, bill_by: '', budget: 20000, budget_by: 'project', budget_is_monthly: false, starts_on: '2025-02-01', ends_on: null, created_at: '', updated_at: '' }, // No end date
        { id: 30, client_id: 1, name: 'Project Z', code: '', is_active: false, is_billable: true, is_fixed_fee: false, bill_by: '', budget: 5000, budget_by: 'project', budget_is_monthly: false, starts_on: '2024-01-01', ends_on: '2024-12-31', created_at: '', updated_at: '' },
      ];
      const mockUninvoicedResponse: HarvestApiResponse<UninvoicedResult> = {
        results: [
          { project_id: 10, project_name: 'Project X', client_id: 1, client_name: 'Client A', uninvoiced_amount: 500, uninvoiced_hours: 5 },
          // Project Y has no uninvoiced amount in this mock
        ],
         total_pages: 1, total_entries: 1, page: 1,
      };

      // Setup mocks
      mockHarvestClient.getProjectBudgetReport.mockResolvedValue(mockBudgetResponse);
      mockHarvestClient.getProjects.mockResolvedValue(mockProjectsResponse);
      mockHarvestClient.getUninvoicedReport.mockResolvedValue(mockUninvoicedResponse);

      // Act
      const result = await budgetService.getProjectBudgets();

      // Assert
      expect(mockHarvestClient.getProjectBudgetReport).toHaveBeenCalledTimes(1);
      expect(mockHarvestClient.getProjects).toHaveBeenCalledTimes(1);
      expect(mockHarvestClient.getUninvoicedReport).toHaveBeenCalledTimes(1);
      // Check the arguments for getUninvoicedReport (dates)
      expect(mockHarvestClient.getUninvoicedReport).toHaveBeenCalledWith(expect.objectContaining({
          from: expect.any(String), // Check format if needed
          to: expect.any(String),
      }));

      // Check the transformed result - filters projects with 0 remaining budget AND 0 uninvoiced amount
      expect(result).toHaveLength(2); // Project Z should be filtered out
      expect(result[0]).toEqual({
        id: '10',
        name: 'Project X',
        clientId: '1',
        clientName: 'Client A',
        budgetRemaining: 5000,
        uninvoicedAmount: 500,
        startDate: new Date('2025-01-01'),
        endDate: new Date('2025-12-31'),
        isActive: true,
      });
       expect(result[1]).toEqual({
        id: '20',
        name: 'Project Y',
        clientId: '2',
        clientName: 'Client B',
        budgetRemaining: 2000,
        uninvoicedAmount: 0, // Defaults to 0 if not in uninvoiced report
        startDate: new Date('2025-02-01'),
        endDate: null, // No end date from mock
        isActive: true,
      });
    });

     it('should handle API errors gracefully and return empty array', async () => {
        // Arrange
        // Simulate error in one of the API calls
        mockHarvestClient.getProjects.mockRejectedValue(new Error('Failed to fetch projects'));
        // Mock others to resolve normally (or they might not even be called if error happens early)
        mockHarvestClient.getProjectBudgetReport.mockResolvedValue({ results: [], total_pages: 0, total_entries: 0, page: 1 });
        mockHarvestClient.getUninvoicedReport.mockResolvedValue({ results: [], total_pages: 0, total_entries: 0, page: 1 });


        // Act
        const result = await budgetService.getProjectBudgets();

        // Assert
        expect(result).toEqual([]); // Should return empty array on error
      });
  });

  // --- Test Cases for getOutstandingInvoices ---
  describe('getOutstandingInvoices', () => {
    it('should fetch all invoices, filter for open ones, and transform them', async () => {
      // Arrange
      const mockApiResponse: any[] = [
        { id: 1, number: 'INV-001', client: { id: 1, name: 'Client A' }, amount: 1000, state: 'open', due_date: '2025-04-14', line_items: [{ project: { id: 10, name: 'Project X' } }] },
        { id: 2, number: 'INV-002', client: { id: 2, name: 'Client B' }, amount: 2000, state: 'paid', due_date: '2025-04-19', line_items: [{ project: { id: 20, name: 'Project Y' } }] }, // Paid, should be filtered
        { id: 3, number: 'INV-003', client: { id: 1, name: 'Client A' }, amount: 1500, state: 'open', due_date: '2025-04-24', line_items: [{ project: { id: 10, name: 'Project X' } }] },
        { id: 4, number: 'INV-004', client: { id: 3, name: 'Client C' }, amount: 500, state: 'draft', due_date: '2025-04-30', line_items: [{ project: { id: 30, name: 'Project Z' } }] }, // Draft, should be filtered
      ];
      mockHarvestClient.getInvoices.mockResolvedValue(mockApiResponse as any);

      // Act
      const result = await budgetService.getOutstandingInvoices();

      // Assert
      expect(mockHarvestClient.getInvoices).toHaveBeenCalledTimes(1);
      expect(result).toHaveLength(2); // Only the two 'open' invoices

      // Check transformation
      expect(result[0]).toEqual({
        id: '1',
        projectId: '10',
        projectName: 'Project X',
        clientName: 'Client A',
        amount: 1000,
        dueDate: new Date('2025-04-14'),
      });
      expect(result[1]).toEqual({
        id: '3',
        projectId: '10', // Same project as first line item
        projectName: 'Project X',
        clientName: 'Client A',
        amount: 1500,
        dueDate: new Date('2025-04-24'),
      });
    });

    it('should return an empty array if no open invoices are found', async () => {
      // Arrange
      const mockApiResponse: any[] = [
        { id: 2, number: 'INV-002', client: { id: 2, name: 'Client B' }, amount: 2000, state: 'paid', due_date: '2025-04-19', line_items: [{ project: { id: 20, name: 'Project Y' } }] },
        { id: 4, number: 'INV-004', client: { id: 3, name: 'Client C' }, amount: 500, state: 'draft', due_date: '2025-04-30', line_items: [{ project: { id: 30, name: 'Project Z' } }] },
      ];
      mockHarvestClient.getInvoices.mockResolvedValue(mockApiResponse as any);

      // Act
      const result = await budgetService.getOutstandingInvoices();

      // Assert
      expect(mockHarvestClient.getInvoices).toHaveBeenCalledTimes(1);
      expect(result).toEqual([]);
    });

    it('should handle API errors gracefully and return empty array', async () => {
       // Arrange
       mockHarvestClient.getInvoices.mockRejectedValue(new Error('Harvest API Error'));

       // Act
       const result = await budgetService.getOutstandingInvoices();

       // Assert
       expect(result).toEqual([]); // Should return empty array on error
    });
  });

  // --- Test Cases for generateProjectedIncome ---
  // TODO: Add tests for generateProjectedIncome - requires mocking getProjectBudgets and handling projectSettings
  describe('generateProjectedIncome', () => {
    it('should generate projected income transactions correctly', async () => {
      // This test will be more complex, requiring mocks for getProjectBudgets
      // and defining projectSettings.
      expect(true).toBe(true); // Placeholder
    });
  });

  // Add tests for other public methods if any
});
