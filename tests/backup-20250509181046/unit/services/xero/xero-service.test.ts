import { XeroService } from '@/services/xero/xero-service';
import { BankAccountService } from '@/services/xero/bank-account-service';
import { RepeatingBillService } from '@/services/xero/repeating-bill-service';
import { createXeroClient, XeroClient } from '@/api/integrations/xero'; // Import XeroClient type
import { jest } from '@jest/globals';
import { TokenSet } from 'xero-node'; // Import TokenSet type

// Mock the factory function and sub-services
jest.mock('@/api/integrations/xero', () => ({
  createXeroClient: jest.fn(),
}));
jest.mock('@/services/xero/bank-account-service');
jest.mock('@/services/xero/repeating-bill-service');

const mockCreateXeroClient = createXeroClient as jest.Mock;
const MockBankAccountService = BankAccountService as jest.MockedClass<typeof BankAccountService>;
const MockRepeatingBillService = RepeatingBillService as jest.MockedClass<typeof RepeatingBillService>;

// Helper function to create a valid mock TokenSet
const createMockTokenSet = (overrides: Partial<TokenSet> = {}): TokenSet => ({
  access_token: 'mock_access_token',
  refresh_token: 'mock_refresh_token',
  expires_at: Date.now() / 1000 + 3600,
  token_type: 'Bearer',
  scope: 'scope1 scope2',
  session_state: 'mock_session_state',
  id_token: 'mock_id_token',
  expired: jest.fn(() => (overrides.expires_at ? (overrides.expires_at * 1000 < Date.now()) : false)),
  // Simplify claims mock as IdTokenClaims type is problematic
  claims: jest.fn(() => ({
    sub: 'user123',
    aud: 'mock_aud',
    exp: overrides.expires_at || (Date.now() / 1000 + 3600),
    iat: Date.now() / 1000,
    iss: 'mock_iss',
    auth_time: Date.now() / 1000 - 60,
  })),
  ...overrides,
});

// Helper function to create mock Tenant (using 'any' type)
const createMockTenant = (id: string, name: string): any => ({
    tenantId: id,
    tenantName: name,
    tenantType: 'ORGANISATION',
});


describe('XeroService', () => {
  let xeroService: XeroService;
  let mockXeroClient: jest.Mocked<XeroClient>; // Use mocked type
  let mockBankAccountService: jest.Mocked<BankAccountService>;
  let mockRepeatingBillService: jest.Mocked<RepeatingBillService>;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers(); // Use fake timers for delay/retry tests

    // Define a more complete mock client
    mockXeroClient = {
      config: { redirectUris: ['http://localhost/callback'], scopes: ['scope1'], clientId: 'test-client-id', clientSecret: 'test-secret' },
      initialize: jest.fn(),
      buildConsentUrl: jest.fn(),
      apiCallback: jest.fn(),
      // Adjust mock signature to potentially accept optional boolean
      updateTenants: jest.fn().mockImplementation(async (fullOrgDetails?: boolean) => {
        console.log(`Mock updateTenants called with fullOrgDetails: ${fullOrgDetails}`); // Log arg for debugging
        return [] as any[]; // Resolve with empty array by default
      }),
      readTokenSet: jest.fn(),
      refreshToken: jest.fn(),
      disconnect: jest.fn(),
      accountingApi: {} as any, // Mock accountingApi if needed by sub-services
      tenants: [], // Keep internal tenants array for reference if needed
    } as unknown as jest.Mocked<XeroClient>; // Cast to mocked type

    mockCreateXeroClient.mockReturnValue(mockXeroClient);

    // Get/reset singleton instance
    xeroService = XeroService.getInstance();

    // Inject mock client using setClient
    xeroService.setClient(mockXeroClient);
    xeroService.setActiveTenantId(null); // Reset active tenant

    // Access the internally created mock instances
    mockBankAccountService = xeroService.bankAccounts as jest.Mocked<BankAccountService>;
    mockRepeatingBillService = xeroService.repeatingBills as jest.Mocked<RepeatingBillService>;
  });

   afterEach(() => {
     jest.useRealTimers(); // Restore real timers
   });

  it('should be defined and be a singleton', () => {
    expect(xeroService).toBeDefined();
    const instance1 = XeroService.getInstance();
    const instance2 = XeroService.getInstance();
    expect(instance1).toBe(instance2);
    expect(xeroService.getClient()).toBe(mockXeroClient);
  });

  it('should set and get active tenant ID', () => {
    const testTenantId = 'test-tenant-id-987';
    expect(xeroService.getActiveTenantId()).toBeNull();
    xeroService.setActiveTenantId(testTenantId);
    expect(xeroService.getActiveTenantId()).toBe(testTenantId);
  });

  describe('buildConsentUrl', () => {
    it('should call the client buildConsentUrl method', async () => {
      const expectedUrl = 'http://consent.url';
      mockXeroClient.buildConsentUrl.mockResolvedValue(expectedUrl);
      const url = await xeroService.buildConsentUrl();
      expect(url).toBe(expectedUrl);
      expect(mockXeroClient.buildConsentUrl).toHaveBeenCalledTimes(1);
    });

    it('should throw an error if client fails', async () => {
      mockXeroClient.buildConsentUrl.mockRejectedValue(new Error('Client failed'));
      await expect(xeroService.buildConsentUrl()).rejects.toThrow('Failed to build consent URL');
    });
  });

  describe('handleCallback', () => {
    const callbackUrl = 'http://localhost/callback?code=123&state=abc';
    const mockTokenSet = createMockTokenSet();
    const mockTenants = [createMockTenant('t1', 'Org 1'), createMockTenant('t2', 'Org 2')];

    it('should call apiCallback, updateTenants, and set active tenant', async () => {
      mockXeroClient.apiCallback.mockResolvedValue(mockTokenSet);
      // Mock updateTenants to resolve with tenants
      mockXeroClient.updateTenants.mockResolvedValue(mockTenants);

      const tokenSetResult = await xeroService.handleCallback(callbackUrl);

      expect(tokenSetResult).toEqual(mockTokenSet);
      expect(mockXeroClient.apiCallback).toHaveBeenCalledWith(callbackUrl);
      expect(mockXeroClient.updateTenants).toHaveBeenCalledTimes(1);
      // Check the service's internal state after the call
      expect(xeroService.getTenants()).toEqual(mockTenants);
      expect(xeroService.getActiveTenantId()).toBe(mockTenants[0].tenantId);
    });

     it('should handle case with no tenants returned', async () => {
      mockXeroClient.apiCallback.mockResolvedValue(mockTokenSet);
      mockXeroClient.updateTenants.mockResolvedValue([]); // Resolve with empty array

      await xeroService.handleCallback(callbackUrl);

      expect(xeroService.getTenants()).toEqual([]);
      expect(xeroService.getActiveTenantId()).toBeNull();
    });

    it('should throw error if apiCallback fails', async () => {
      const apiError = new Error('API callback failed');
      mockXeroClient.apiCallback.mockRejectedValue(apiError);

      await expect(xeroService.handleCallback(callbackUrl)).rejects.toThrow(apiError);
      expect(mockXeroClient.updateTenants).not.toHaveBeenCalled();
      expect(xeroService.getActiveTenantId()).toBeNull();
    });
  });

  describe('isAuthenticated', () => {
    it('should return false if no token set exists', async () => {
      mockXeroClient.readTokenSet.mockReturnValue(null as any); // Simulate no token
      expect(await xeroService.isAuthenticated()).toBe(false);
    });

    it('should return true if token set exists, is not expired, and tenants exist', async () => {
      const validToken = createMockTokenSet();
      mockXeroClient.readTokenSet.mockReturnValue(validToken);
      // Simulate tenants already exist on the service instance
      (xeroService as any).tenants = [createMockTenant('t1', 'Org 1')];
      xeroService.setActiveTenantId('t1');

      expect(await xeroService.isAuthenticated()).toBe(true);
      expect(mockXeroClient.refreshToken).not.toHaveBeenCalled();
    });

    it('should attempt refresh, update tenants, and return true if token is expired but refresh succeeds', async () => {
      const expiredTimestamp = Date.now() / 1000 - 60;
      const expiredToken = createMockTokenSet({ expires_at: expiredTimestamp, refresh_token: 'refresh' });
      const refreshedToken = createMockTokenSet(); // New valid token
      const mockTenants = [createMockTenant('t1', 'Org 1')];

      mockXeroClient.readTokenSet.mockReturnValue(expiredToken);
      mockXeroClient.refreshToken.mockResolvedValue(refreshedToken);
      mockXeroClient.updateTenants.mockResolvedValue(mockTenants); // updateTenants returns tenants

      expect(await xeroService.isAuthenticated()).toBe(true);
      expect(mockXeroClient.refreshToken).toHaveBeenCalledTimes(1);
      expect(mockXeroClient.updateTenants).toHaveBeenCalledTimes(1);
      expect(xeroService.getActiveTenantId()).toBe(mockTenants[0].tenantId);
    });

     it('should return false if token is expired and refresh fails', async () => {
      const expiredTimestamp = Date.now() / 1000 - 60;
      const expiredToken = createMockTokenSet({ expires_at: expiredTimestamp, refresh_token: 'refresh' });
      mockXeroClient.readTokenSet.mockReturnValue(expiredToken);
      mockXeroClient.refreshToken.mockRejectedValue(new Error('Refresh failed'));

      expect(await xeroService.isAuthenticated()).toBe(false);
      expect(mockXeroClient.refreshToken).toHaveBeenCalledTimes(1);
      expect(mockXeroClient.updateTenants).not.toHaveBeenCalled();
    });

    it('should return false if token is expired and no refresh token exists', async () => {
      const expiredTimestamp = Date.now() / 1000 - 60;
      const expiredTokenNoRefresh = createMockTokenSet({ expires_at: expiredTimestamp, refresh_token: undefined });
      mockXeroClient.readTokenSet.mockReturnValue(expiredTokenNoRefresh);

      expect(await xeroService.isAuthenticated()).toBe(false);
      expect(mockXeroClient.refreshToken).not.toHaveBeenCalled();
    });
  });

  describe('getTenants', () => {
    it('should return the tenants array stored within the service instance', () => {
      const mockTenants = [createMockTenant('t1', 'Org 1')];
      // Set tenants directly on the service instance for this test
      (xeroService as any).tenants = mockTenants;
      expect(xeroService.getTenants()).toEqual(mockTenants);
    });
  });

  describe('getCashflowData', () => {
    const tenantId = 'test-tenant';
    const startDate = new Date('2025-01-01');
    const endDate = new Date('2025-03-31');
    const validToken = createMockTokenSet();

    // Helper to create a valid BankAccount mock
    const createMockBankAccount = (id: string, name: string, balance: number): any => ({
      accountID: id, code: `CODE-${id}`, name: name, status: 'ACTIVE', type: 'BANK', currencyCode: 'AUD',
      bankAccountNumber: `ACC-${id}`, reportingCode: '', reportingCodeName: '', hasAttachments: false,
      updatedDateUTC: new Date().toISOString(), addToWatchlist: false, class: 'ASSET', systemAccount: '',
      enablePaymentsToAccount: false, showInExpenseClaims: false, accountIDGuid: `guid-${id}`,
      closingBalance: balance, bankAccountType: 'BANK',
    });

    // Helper to create a valid RepeatingBill mock
    const createMockRepeatingBill = (id: string, amount: number): any => ({
        repeatingInvoiceID: id, type: 'ACCPAY', reference: `Mock Bill ${id}`, date: new Date(),
        status: 'AUTHORISED', total: amount, contact: { contactID: `contact-${id}`, name: `Supplier ${id}` },
        schedule: { period: 1, unit: 'MONTHLY', dueDate: 15, dueDateType: 'OFCALENDARDAY', startDate: '2025-01-01' },
        lineItems: [], currencyCode: 'AUD',
    });


    beforeEach(() => {
      // Mock sub-service methods
      mockBankAccountService.getBankBalancesFromBalanceSheet.mockResolvedValue({ totalOpeningBalance: 1000, totalClosingBalance: 1500, accounts: [] });
      mockRepeatingBillService.getRepeatingBills.mockResolvedValue([]);
      // Assume valid, non-expired token
      mockXeroClient.readTokenSet.mockReturnValue(validToken);
    });

    it('should call sub-services to get data', async () => {
      await xeroService.getCashflowData(tenantId, startDate, endDate);

      expect(mockBankAccountService.getBankBalancesFromBalanceSheet).toHaveBeenCalledWith(tenantId);
      expect(mockRepeatingBillService.getRepeatingBills).toHaveBeenCalledWith(tenantId, startDate, endDate);
    });

    it('should return combined data on success', async () => {
      const mockBalances = {
          totalOpeningBalance: 5000,
          totalClosingBalance: 6000,
          accounts: [createMockBankAccount('b1', 'Bank 1', 6000)]
      };
      const mockBills = [createMockRepeatingBill('rb1', 100)];

      mockBankAccountService.getBankBalancesFromBalanceSheet.mockResolvedValue(mockBalances as any);
      mockRepeatingBillService.getRepeatingBills.mockResolvedValue(mockBills as any);

      const result = await xeroService.getCashflowData(tenantId, startDate, endDate);

      expect(result).toEqual({
        bankBalances: mockBalances,
        fixedExpenses: [], // This is hardcoded empty
        repeatingBills: mockBills,
      });
    });

     it('should attempt token refresh if token is expired before API call', async () => {
        const expiredTimestamp = Date.now() / 1000 - 60;
        const expiredToken = createMockTokenSet({ expires_at: expiredTimestamp, refresh_token: 'refresh' });
        const refreshedToken = createMockTokenSet();

        mockXeroClient.readTokenSet.mockReturnValue(expiredToken);
        mockXeroClient.refreshToken.mockResolvedValue(refreshedToken); // Correct: Resolve with token
        // Correct mock for updateTenants to resolve with empty array, satisfying signature
        mockXeroClient.updateTenants.mockResolvedValue(undefined); // Pass undefined to satisfy signature

        await xeroService.getCashflowData(tenantId, startDate, endDate);

        expect(mockXeroClient.refreshToken).toHaveBeenCalledTimes(1);
        expect(mockXeroClient.updateTenants).toHaveBeenCalledTimes(1);
        expect(mockBankAccountService.getBankBalancesFromBalanceSheet).toHaveBeenCalled();
        expect(mockRepeatingBillService.getRepeatingBills).toHaveBeenCalled();
     });

     it('should still attempt API calls if token refresh fails', async () => {
        const expiredTimestamp = Date.now() / 1000 - 60;
        const expiredToken = createMockTokenSet({ expires_at: expiredTimestamp, refresh_token: 'refresh' });

        mockXeroClient.readTokenSet.mockReturnValue(expiredToken);
        mockXeroClient.refreshToken.mockRejectedValue(new Error('Refresh failed'));

        await xeroService.getCashflowData(tenantId, startDate, endDate);

        expect(mockXeroClient.refreshToken).toHaveBeenCalledTimes(1);
        expect(mockXeroClient.updateTenants).not.toHaveBeenCalled();
        expect(mockBankAccountService.getBankBalancesFromBalanceSheet).toHaveBeenCalled();
        expect(mockRepeatingBillService.getRepeatingBills).toHaveBeenCalled();
     });

    it('should return default data if a sub-service call fails after retries', async () => {
      const apiError = new Error('API Failed');
      mockBankAccountService.getBankBalancesFromBalanceSheet.mockRejectedValue(apiError);
      const mockBills = [createMockRepeatingBill('rb1', 100)];
      mockRepeatingBillService.getRepeatingBills.mockResolvedValue(mockBills as any);

      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await xeroService.getCashflowData(tenantId, startDate, endDate);

      expect(result.bankBalances).toEqual({ totalOpeningBalance: 0, totalClosingBalance: 0, accounts: [] });
      expect(result.repeatingBills).toEqual(mockBills);
      expect(mockBankAccountService.getBankBalancesFromBalanceSheet).toHaveBeenCalledTimes(4); // Check retries
      expect(mockRepeatingBillService.getRepeatingBills).toHaveBeenCalledTimes(1);

      consoleErrorSpy.mockRestore();
    });
  });
});
