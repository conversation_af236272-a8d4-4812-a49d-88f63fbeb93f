import { SuperannuationService } from '@/services/xero/superannuation-service';
import { XeroSuperannuationExpenseDisplay } from '@/api/types/xero';
import { jest } from '@jest/globals';

describe('SuperannuationService', () => {
  // Mock XeroClient
  const mockXeroClient = {
    readTokenSet: jest.fn(),
    getSuperannuationPayments: jest.fn(),
  };

  // Service instance to test
  let superannuationService: SuperannuationService;

  // Sample data
  const sampleSuperPaymentsResponse = {
    body: {
      superannuationPayments: [
        {
          superannuationPaymentID: 'super-payment-1',
          paymentDate: '2025-06-15',
          amount: 2500,
          status: 'SCHEDULED',
          superFund: {
            name: 'Australian Super'
          },
          employeeCount: 5
        },
        {
          superannuationPaymentID: 'super-payment-2',
          paymentDate: '2025-05-15',
          amount: 2400,
          status: 'PAID',
          superFund: {
            name: 'Rest Super'
          },
          employeeCount: 4
        }
      ]
    }
  };

  // Mock token with payroll scope
  const mockTokenWithScope = {
    scope: 'accounting.transactions payroll.superannuation openid email profile',
    access_token: 'mock-token',
    refresh_token: 'mock-refresh',
    expires_at: Date.now() + 3600
  };

  // Mock token without payroll scope
  const mockTokenWithoutScope = {
    scope: 'accounting.transactions openid email profile',
    access_token: 'mock-token',
    refresh_token: 'mock-refresh',
    expires_at: Date.now() + 3600
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset console mocks between tests
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    
    // Create a fresh service instance for each test
    superannuationService = new SuperannuationService(mockXeroClient as any);
  });

  describe('getSuperannuationExpensesForDisplay', () => {
    it('should return empty array when payroll scope is missing', async () => {
      // Set up the mock token without payroll scope
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithoutScope);
      
      const results = await superannuationService.getSuperannuationExpensesForDisplay('tenant-id');
      
      expect(results).toEqual([]);
      expect(mockXeroClient.getSuperannuationPayments).not.toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('Missing payroll scopes in token, using empty set');
    });

    it('should fetch and transform superannuation data when scope is available', async () => {
      // Set up the mocks
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getSuperannuationPayments.mockResolvedValue(sampleSuperPaymentsResponse);
      
      const results = await superannuationService.getSuperannuationExpensesForDisplay('tenant-id');
      
      // Verify client was called correctly
      expect(mockXeroClient.getSuperannuationPayments).toHaveBeenCalledWith('tenant-id', undefined);
      
      // Verify we got the expected number of results
      expect(results.length).toBe(2);
      
      // Verify the first result was transformed correctly
      const firstResult = results[0];
      expect(firstResult).toMatchObject({
        id: 'super-payment-1',
        provider: 'Australian Super',
        amount: 2500,
        employeeCount: 5,
        status: 'Scheduled',
        frequency: 'monthly',
        isAlreadyAdded: false
      });
      
      // Verify date was parsed correctly
      expect(firstResult.paymentDate).toBeInstanceOf(Date);
      
      // Verify the description format
      expect(firstResult.description).toBe('Superannuation: Australian Super');
    });

    it('should handle empty response gracefully', async () => {
      // Set up the mocks
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getSuperannuationPayments.mockResolvedValue({ body: { superannuationPayments: [] } });
      
      const results = await superannuationService.getSuperannuationExpensesForDisplay('tenant-id');
      
      expect(results).toEqual([]);
      expect(console.log).toHaveBeenCalledWith('Found 0 superannuation payments');
    });

    it('should handle API errors gracefully', async () => {
      // Set up the mocks
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getSuperannuationPayments.mockRejectedValue(new Error('API failure'));
      
      const results = await superannuationService.getSuperannuationExpensesForDisplay('tenant-id');
      
      expect(results).toEqual([]);
      expect(console.error).toHaveBeenCalledWith('Error getting superannuation data from Xero:', expect.any(Error));
    });

    it('should handle null response gracefully', async () => {
      // Set up the mocks
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getSuperannuationPayments.mockResolvedValue(null);
      
      const results = await superannuationService.getSuperannuationExpensesForDisplay('tenant-id');
      
      expect(results).toEqual([]);
      expect(console.log).toHaveBeenCalledWith('No superannuation data returned from Xero');
    });

    it('should pass fromDate parameter when provided', async () => {
      // Set up the mocks
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getSuperannuationPayments.mockResolvedValue(sampleSuperPaymentsResponse);
      
      const testDate = new Date('2025-01-01');
      await superannuationService.getSuperannuationExpensesForDisplay('tenant-id', testDate);
      
      // Verify client was called with the fromDate parameter
      expect(mockXeroClient.getSuperannuationPayments).toHaveBeenCalledWith('tenant-id', testDate);
    });
  });

  describe('status mapping', () => {
    it('should map all status values correctly', async () => {
      // Set up test data with different statuses
      const statusData = {
        body: {
          superannuationPayments: [
            {
              superannuationPaymentID: 'draft-payment',
              paymentDate: '2025-06-15',
              amount: 2500,
              status: 'DRAFT',
              superFund: { name: 'Super Fund' }
            },
            {
              superannuationPaymentID: 'scheduled-payment',
              paymentDate: '2025-06-15',
              amount: 2500,
              status: 'SCHEDULED',
              superFund: { name: 'Super Fund' }
            },
            {
              superannuationPaymentID: 'paid-payment',
              paymentDate: '2025-06-15',
              amount: 2500,
              status: 'PAID',
              superFund: { name: 'Super Fund' }
            },
            {
              superannuationPaymentID: 'unknown-payment',
              paymentDate: '2025-06-15',
              amount: 2500,
              status: 'SOMETHINGELSE',
              superFund: { name: 'Super Fund' }
            }
          ]
        }
      };
      
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getSuperannuationPayments.mockResolvedValue(statusData);
      
      const results = await superannuationService.getSuperannuationExpensesForDisplay('tenant-id');
      
      expect(results[0].status).toBe('Draft');
      expect(results[1].status).toBe('Scheduled');
      expect(results[2].status).toBe('Paid');
      expect(results[3].status).toBe('Unknown');
    });
  });

  describe('edge cases', () => {
    it('should handle missing provider name', async () => {
      // Set up test data with missing provider name
      const noProviderData = {
        body: {
          superannuationPayments: [
            {
              superannuationPaymentID: 'no-provider',
              paymentDate: '2025-06-15',
              amount: 2500,
              status: 'SCHEDULED',
              superFund: { } // Missing name
            }
          ]
        }
      };
      
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getSuperannuationPayments.mockResolvedValue(noProviderData);
      
      const results = await superannuationService.getSuperannuationExpensesForDisplay('tenant-id');
      
      expect(results[0].provider).toBe('Unknown Provider');
      expect(results[0].description).toBe('Superannuation: Unknown Provider');
    });

    it('should handle missing super fund object', async () => {
      // Set up test data with missing super fund object
      const noFundData = {
        body: {
          superannuationPayments: [
            {
              superannuationPaymentID: 'no-fund',
              paymentDate: '2025-06-15',
              amount: 2500,
              status: 'SCHEDULED'
              // No superFund property
            }
          ]
        }
      };
      
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getSuperannuationPayments.mockResolvedValue(noFundData);
      
      const results = await superannuationService.getSuperannuationExpensesForDisplay('tenant-id');
      
      expect(results[0].provider).toBe('Unknown Provider');
    });

    it('should handle missing amount', async () => {
      // Set up test data with missing amount
      const noAmountData = {
        body: {
          superannuationPayments: [
            {
              superannuationPaymentID: 'no-amount',
              paymentDate: '2025-06-15',
              // No amount property
              status: 'SCHEDULED',
              superFund: { name: 'Super Fund' }
            }
          ]
        }
      };
      
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getSuperannuationPayments.mockResolvedValue(noAmountData);
      
      const results = await superannuationService.getSuperannuationExpensesForDisplay('tenant-id');
      
      expect(results[0].amount).toBe(0);
    });
  });
});