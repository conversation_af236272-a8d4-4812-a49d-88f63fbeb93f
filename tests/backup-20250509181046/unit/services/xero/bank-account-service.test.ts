import { BankAccountService } from '@/services/xero/bank-account-service';
// Import the local XeroClient type definition used by the service
import { XeroClient } from '@/api/integrations/xero';
// AccountingApi type is no longer needed directly in this mock setup
// import { AccountingApi } from 'xero-node';
import { jest } from '@jest/globals';

// Create a minimal mock XeroClient satisfying the constructor (using the local type)
const mockXeroClient = {
  // Mock the methods directly called by the service
  getBankSummaryReport: jest.fn(),
  // Cast accountingApi mock to any to bypass strict type checking here
  accountingApi: {
    getAccounts: jest.fn(),
  } as any, // Cast nested mock to any
} as Partial<XeroClient> as jest.Mocked<XeroClient>; // Cast needed for partial mock

describe('BankAccountService', () => {
  let bankAccountService: BankAccountService;

  beforeEach(() => {
    jest.clearAllMocks();
    // Initialize the service with the minimal mocked client
    bankAccountService = new BankAccountService(mockXeroClient);
  });

  it('should be defined', () => {
    expect(bankAccountService).toBeDefined();
  });

  describe('getBankBalancesFromBalanceSheet', () => {
    it('should fetch bank summary report and calculate balances correctly', async () => {
      // Arrange
      const tenantId = 'tenant-123';
      // Mock the response structure based on Bank Summary Report format
      const mockBankSummaryResponse = {
        body: {
          reports: [ // Use lowercase 'reports' based on service code observation
            {
              reportID: 'BankSummary',
              rows: [
                { // Section Row
                  rowType: 'Section',
                  rows: [
                    { // Account Row 1
                      rowType: 'Row',
                      cells: [
                        { value: 'Business Account', attributes: [{ id: 'accountID', value: 'acc1' }] }, // Name/ID
                        { value: '500.00' }, // Opening Balance (example)
                        { value: '0.00' },   // Inflows (example)
                        { value: '0.00' },   // Outflows (example)
                        { value: '1000.00' } // Closing Balance
                      ]
                    },
                    { // Account Row 2
                      rowType: 'Row',
                      cells: [
                        { value: 'Savings', attributes: [{ id: 'accountID', value: 'acc2' }] }, // Name/ID
                        { value: '4000.00' }, // Opening Balance
                        { value: '0.00' },    // Inflows
                        { value: '0.00' },    // Outflows
                        { value: '5000.00' }  // Closing Balance
                      ]
                    },
                     { // Summary Row
                      rowType: 'SummaryRow',
                      cells: [
                        { value: 'Total' },
                        { value: '4500.00' }, // Total Opening
                        { value: '0.00' },
                        { value: '0.00' },
                        { value: '6000.00' }  // Total Closing
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      };
      // Mock the getBankSummaryReport method, casting mock function to any
      (mockXeroClient.getBankSummaryReport as any).mockResolvedValue(mockBankSummaryResponse);

      // Act
      const result = await bankAccountService.getBankBalancesFromBalanceSheet(tenantId);

      // Assert
      expect(mockXeroClient.getBankSummaryReport).toHaveBeenCalledWith(tenantId);
      expect(mockXeroClient.accountingApi.getAccounts).not.toHaveBeenCalled(); // Ensure fallback wasn't used
      expect(result).toBeDefined();
      expect(result.accounts).toHaveLength(2);
      expect(result.totalClosingBalance).toBe(6000.00);
      expect(result.totalOpeningBalance).toBe(4500.00); // Check opening balance from summary
      expect(result.accounts[0]).toEqual(expect.objectContaining({ id: 'acc1', name: 'Business Account', closingBalance: 1000.00, openingBalance: 500.00 }));
      expect(result.accounts[1]).toEqual(expect.objectContaining({ id: 'acc2', name: 'Savings', closingBalance: 5000.00, openingBalance: 4000.00 }));
    });

     it('should fallback to getAccounts if bank summary is empty/invalid', async () => {
        // Arrange
        const tenantId = 'tenant-123';
        // Mock bank summary to resolve with an empty/invalid body
        const mockInvalidSummaryResponse = { body: { reports: [] } }; // Example of empty response
        (mockXeroClient.getBankSummaryReport as any).mockResolvedValue(mockInvalidSummaryResponse);

        const mockAccountsResponse = {
            body: {
                accounts: [
                    { accountID: 'acc-fallback', name: 'Fallback Bank', type: 'BANK', status: 'ACTIVE', balance: 1234.56, isBankAccount: true, bankAccountType: 'BANK' },
                ]
            }
        };
        // Cast mock function to any
        (mockXeroClient.accountingApi.getAccounts as any).mockResolvedValue({
            response: {}, body: mockAccountsResponse.body
        });

        // Act
        const result = await bankAccountService.getBankBalancesFromBalanceSheet(tenantId);

        // Assert
        expect(mockXeroClient.getBankSummaryReport).toHaveBeenCalledWith(tenantId);
        expect(mockXeroClient.accountingApi.getAccounts).toHaveBeenCalledWith(tenantId, undefined, 'Type=="BANK"');
        expect(result).toBeDefined();
        expect(result.accounts).toHaveLength(1);
        expect(result.totalClosingBalance).toBe(1234.56);
        expect(result.accounts[0]).toEqual(expect.objectContaining({ id: 'acc-fallback', closingBalance: 1234.56 }));
     });

    it('should handle API errors gracefully and return defaults', async () => {
       const tenantId = 'tenant-123';
       const apiError = new Error('Xero API Error');
       // Mock both methods to reject, casting mock functions to any
       (mockXeroClient.getBankSummaryReport as any).mockRejectedValue(apiError);
       (mockXeroClient.accountingApi.getAccounts as any).mockRejectedValue(apiError);

       const result = await bankAccountService.getBankBalancesFromBalanceSheet(tenantId);

       expect(result).toBeDefined();
       expect(result.accounts).toEqual([]);
       expect(result.totalClosingBalance).toBe(0);
    });
  });

  // Add tests for other public methods if any
});
