import { RepeatingBillService } from '@/services/xero/repeating-bill-service';
import { XeroClient } from '@/api/integrations/xero';
import { RepeatingInvoice, Schedule, TokenSet } from 'xero-node';
import { jest } from '@jest/globals';

// Helper to create a mock TokenSet with correct function types
const createMockTokenSet = (props: Partial<TokenSet> = {}): TokenSet => {
  const expiresAt = props.expires_at ?? (Date.now() / 1000 + 3600);
  const isExpired = expiresAt < (Date.now() / 1000);

  const baseTokenSet: TokenSet = {
    access_token: 'mock_access_token', id_token: 'mock_id_token', expires_at: expiresAt,
    token_type: 'Bearer', refresh_token: 'mock_refresh_token',
    scope: 'openid profile email accounting.transactions offline_access', // Ensure accounting scope
    session_state: 'mock_session_state',
    expired: jest.fn<() => boolean>().mockReturnValue(isExpired),
    claims: jest.fn().mockReturnValue({ sub: 'user123', aud: 'aud', exp: expiresAt, iat: 0, iss: 'iss' }) as any,
    ...props,
  };
  return baseTokenSet;
};

// Mock the XeroClient
const mockXeroClient = {
  getRepeatingInvoices: jest.fn(),
  readTokenSet: jest.fn().mockReturnValue(createMockTokenSet()),
  accountingApi: {} as any,
} as Partial<XeroClient> as jest.Mocked<XeroClient>;

describe('RepeatingBillService', () => {
  let repeatingBillService: RepeatingBillService;

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset token mock for each test if needed, e.g., for scope tests
    mockXeroClient.readTokenSet.mockReturnValue(createMockTokenSet());
    repeatingBillService = new RepeatingBillService(mockXeroClient);
  });

  it('should be defined', () => {
    expect(repeatingBillService).toBeDefined();
  });

  describe('getRepeatingBills', () => {
    const tenantId = 'tenant-123';
    const startDate = new Date('2025-04-01');
    const endDate = new Date('2025-06-30'); // Extend range for better testing

    // Helper to create mock RepeatingInvoice
    const createMockXeroBill = (id: string, type: RepeatingInvoice.TypeEnum, total: number, schedule: Partial<Schedule>, status = RepeatingInvoice.StatusEnum.AUTHORISED): Partial<RepeatingInvoice> => ({
      repeatingInvoiceID: id, type, total, schedule: schedule as Schedule, status, reference: `Ref ${id}`, lineItems: []
    });

    it('should calculate future occurrences correctly for various frequencies', async () => {
      // Arrange
      const mockApiResponse = {
        body: {
          repeatingInvoices: [
            // Monthly bill, next date within range
            createMockXeroBill('bill-m1', RepeatingInvoice.TypeEnum.ACCPAY, 100, { unit: Schedule.UnitEnum.MONTHLY, period: 1, nextScheduledDate: '2025-04-15', dueDateType: Schedule.DueDateTypeEnum.OFFOLLOWINGMONTH, dueDate: 10 }),
            // Weekly bill, next date before range, should calculate occurrences within range
            createMockXeroBill('bill-w1', RepeatingInvoice.TypeEnum.ACCPAY, 25, { unit: Schedule.UnitEnum.WEEKLY, period: 1, nextScheduledDate: '2025-03-28', dueDateType: Schedule.DueDateTypeEnum.DAYSAFTERBILLDATE, dueDate: 7 }),
            // Quarterly bill, next date within range
            createMockXeroBill('bill-q1', RepeatingInvoice.TypeEnum.ACCPAY, 500, { unit: Schedule.UnitEnum.MONTHLY, period: 3, nextScheduledDate: '2025-05-20', dueDateType: Schedule.DueDateTypeEnum.OFCURRENTMONTH, dueDate: 25 }),
            // Bill ending before start date (should be ignored by calculation)
            createMockXeroBill('bill-ended', RepeatingInvoice.TypeEnum.ACCPAY, 99, { unit: Schedule.UnitEnum.MONTHLY, period: 1, nextScheduledDate: '2025-01-01', endDate: '2025-03-01', dueDateType: Schedule.DueDateTypeEnum.OFFOLLOWINGMONTH, dueDate: 1 }),
            // Bill starting after end date (should be ignored by calculation)
            createMockXeroBill('bill-future', RepeatingInvoice.TypeEnum.ACCPAY, 88, { unit: Schedule.UnitEnum.MONTHLY, period: 1, nextScheduledDate: '2025-07-01', dueDateType: Schedule.DueDateTypeEnum.OFFOLLOWINGMONTH, dueDate: 1 }),
            // Bill with no schedule (should be skipped)
            { repeatingInvoiceID: 'bill-nosched', type: RepeatingInvoice.TypeEnum.ACCPAY, total: 50 },
            // Invoice (ACCREC), should be filtered out
            createMockXeroBill('inv-ar1', RepeatingInvoice.TypeEnum.ACCREC, 1000, { unit: Schedule.UnitEnum.MONTHLY, period: 1, nextScheduledDate: '2025-04-10', dueDateType: Schedule.DueDateTypeEnum.OFFOLLOWINGMONTH, dueDate: 10 }),
          ]
        }
      };
      mockXeroClient.getRepeatingInvoices.mockResolvedValue(mockApiResponse as any);

      // Act
      const result = await repeatingBillService.getRepeatingBills(tenantId, startDate, endDate);

      // Assert
      expect(mockXeroClient.getRepeatingInvoices).toHaveBeenCalledWith(tenantId);
      expect(result).toBeDefined();

      // Expected occurrences:
      // bill-m1: Apr 15, May 15, Jun 15 (3)
      // bill-w1: Apr 4, 11, 18, 25, May 2, 9, 16, 23, 30, Jun 6, 13, 20, 27 (13)
      // bill-q1: May 20 (1) -> Next would be Aug 20 (outside range)
      expect(result).toHaveLength(3 + 13 + 1);

      // Check details of one monthly occurrence
      const m1_apr = result.find(r => r.id.includes('bill-m1') && r.date.toISOString().startsWith('2025-04-15'));
      expect(m1_apr).toEqual({
        id: expect.stringContaining('bill-m1-2025-04-15'), type: 'repeating_bill', reference: 'Ref bill-m1',
        date: new Date('2025-04-15T00:00:00.000Z'), amount: -100, status: 'PLANNED'
      });
       // Check details of one weekly occurrence
      const w1_may = result.find(r => r.id.includes('bill-w1') && r.date.toISOString().startsWith('2025-05-02'));
      expect(w1_may).toEqual({
        id: expect.stringContaining('bill-w1-2025-05-02'), type: 'repeating_bill', reference: 'Ref bill-w1',
        date: new Date('2025-05-02T00:00:00.000Z'), amount: -25, status: 'PLANNED'
      });
       // Check details of quarterly occurrence
      const q1_may = result.find(r => r.id.includes('bill-q1') && r.date.toISOString().startsWith('2025-05-20'));
      expect(q1_may).toEqual({
        id: expect.stringContaining('bill-q1-2025-05-20'), type: 'repeating_bill', reference: 'Ref bill-q1',
        date: new Date('2025-05-20T00:00:00.000Z'), amount: -500, status: 'PLANNED'
      });

      // Ensure ended/future/nosched/accrec bills were excluded
      expect(result.some(r => r.id.includes('bill-ended'))).toBe(false);
      expect(result.some(r => r.id.includes('bill-future'))).toBe(false);
      expect(result.some(r => r.id.includes('bill-nosched'))).toBe(false);
      expect(result.some(r => r.id.includes('inv-ar1'))).toBe(false);
    });

    it('should return empty array if accounting scope is missing', async () => {
       // Arrange
       mockXeroClient.readTokenSet.mockReturnValue(createMockTokenSet({ scope: 'openid profile email' })); // No accounting scope

       // Act
       const result = await repeatingBillService.getRepeatingBills(tenantId, startDate, endDate);

       // Assert
       expect(result).toEqual([]);
       expect(mockXeroClient.getRepeatingInvoices).not.toHaveBeenCalled();
    });


    it('should handle API errors gracefully', async () => {
       const apiError = new Error('Xero API Error');
       mockXeroClient.readTokenSet.mockReturnValue(createMockTokenSet()); // Ensure scope check passes
       mockXeroClient.getRepeatingInvoices.mockRejectedValue(apiError);

       const result = await repeatingBillService.getRepeatingBills(tenantId, startDate, endDate);

       expect(result).toEqual([]);
    });

    it('should handle empty API response', async () => {
        const mockEmptyResponse = { body: { repeatingInvoices: [] } };
        mockXeroClient.getRepeatingInvoices.mockResolvedValue(mockEmptyResponse as any);

        const result = await repeatingBillService.getRepeatingBills(tenantId, startDate, endDate);

        expect(result).toEqual([]);
    });

     it('should handle invalid date in schedule gracefully', async () => {
        const mockApiResponse = {
            body: {
                repeatingInvoices: [
                    createMockXeroBill('bill-baddate', RepeatingInvoice.TypeEnum.ACCPAY, 100, { unit: Schedule.UnitEnum.MONTHLY, period: 1, nextScheduledDate: 'invalid-date', dueDateType: Schedule.DueDateTypeEnum.OFFOLLOWINGMONTH, dueDate: 10 }),
                ]
            }
        };
        mockXeroClient.getRepeatingInvoices.mockResolvedValue(mockApiResponse as any);

        // Act - should not throw, but calculate based on startDate
        const result = await repeatingBillService.getRepeatingBills(tenantId, startDate, endDate);

        // Assert - Expect occurrences based on startDate (Apr 1, May 1, Jun 1)
        expect(result).toHaveLength(3);
        expect(result[0].date).toEqual(new Date('2025-04-01'));
        expect(result[1].date).toEqual(new Date('2025-05-01'));
        expect(result[2].date).toEqual(new Date('2025-06-01'));
     });
  });
});
