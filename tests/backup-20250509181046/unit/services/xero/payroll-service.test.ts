import { PayrollService } from '@/services/xero/payroll-service';
import { XeroPayrollExpenseDisplay } from '@/api/types/xero';
import { jest } from '@jest/globals';

describe('PayrollService', () => {
  // Mock XeroClient
  const mockXeroClient = {
    readTokenSet: jest.fn(),
    getPayrollPayRuns: jest.fn(),
  };

  // Service instance to test
  let payrollService: PayrollService;

  // Sample data
  const samplePayRunsResponse = {
    body: {
      payRuns: [
        {
          payRunID: 'pay-run-1',
          paymentDate: '2025-05-15',
          periodStartDate: '2025-05-01',
          periodEndDate: '2025-05-14',
          totalCost: 10000,
          employeeCount: 5,
          status: 'SCHEDULED'
        },
        {
          payRunID: 'pay-run-2',
          paymentDate: '2025-04-30',
          periodStartDate: '2025-04-16',
          periodEndDate: '2025-04-30',
          totalCost: 9500,
          employeeCount: 5,
          status: 'COMPLETED'
        }
      ]
    }
  };

  // Mock token with payroll scope
  const mockTokenWithScope = {
    scope: 'accounting.transactions payroll.payruns openid email profile',
    access_token: 'mock-token',
    refresh_token: 'mock-refresh',
    expires_at: Date.now() + 3600
  };

  // Mock token without payroll scope
  const mockTokenWithoutScope = {
    scope: 'accounting.transactions openid email profile',
    access_token: 'mock-token',
    refresh_token: 'mock-refresh',
    expires_at: Date.now() + 3600
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset console mocks between tests
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    
    // Create a fresh service instance for each test
    payrollService = new PayrollService(mockXeroClient as any);
  });

  describe('getPayrollExpensesForDisplay', () => {
    it('should return empty array when payroll scope is missing', async () => {
      // Set up the mock token without payroll scope
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithoutScope);
      
      const results = await payrollService.getPayrollExpensesForDisplay('tenant-id');
      
      expect(results).toEqual([]);
      expect(mockXeroClient.getPayrollPayRuns).not.toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('Missing payroll scopes in token, using empty set');
    });

    it('should fetch and transform payroll data when scope is available', async () => {
      // Set up the mocks
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getPayrollPayRuns.mockResolvedValue(samplePayRunsResponse);
      
      const results = await payrollService.getPayrollExpensesForDisplay('tenant-id');
      
      // Verify client was called correctly
      expect(mockXeroClient.getPayrollPayRuns).toHaveBeenCalledWith('tenant-id');
      
      // Verify we got the expected number of results
      expect(results.length).toBe(2);
      
      // Verify the first result was transformed correctly
      const firstResult = results[0];
      expect(firstResult).toMatchObject({
        id: 'pay-run-1',
        payRunId: 'pay-run-1',
        amount: 10000,
        employeeCount: 5,
        status: 'Scheduled',
        frequency: 'fortnightly',
        isAlreadyAdded: false
      });
      
      // Verify dates were parsed correctly
      expect(firstResult.paymentDate).toBeInstanceOf(Date);
      expect(firstResult.periodStartDate).toBeInstanceOf(Date);
      expect(firstResult.periodEndDate).toBeInstanceOf(Date);
      
      // Verify the description format
      expect(firstResult.description).toContain('Payroll: ');
      expect(firstResult.description).toContain('to');
    });

    it('should handle empty response gracefully', async () => {
      // Set up the mocks
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getPayrollPayRuns.mockResolvedValue({ body: { payRuns: [] } });
      
      const results = await payrollService.getPayrollExpensesForDisplay('tenant-id');
      
      expect(results).toEqual([]);
      expect(console.log).toHaveBeenCalledWith('Found 0 pay runs');
    });

    it('should handle API errors gracefully', async () => {
      // Set up the mocks
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getPayrollPayRuns.mockRejectedValue(new Error('API failure'));
      
      const results = await payrollService.getPayrollExpensesForDisplay('tenant-id');
      
      expect(results).toEqual([]);
      expect(console.error).toHaveBeenCalledWith('Error getting payroll data from Xero:', expect.any(Error));
    });

    it('should handle null response gracefully', async () => {
      // Set up the mocks
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getPayrollPayRuns.mockResolvedValue(null);
      
      const results = await payrollService.getPayrollExpensesForDisplay('tenant-id');
      
      expect(results).toEqual([]);
      expect(console.log).toHaveBeenCalledWith('No payroll data returned from Xero');
    });
  });

  describe('frequency determination', () => {
    // We'll test the private method by calling the public method with specially crafted test data
    it('should determine weekly frequency correctly', async () => {
      // Set up test data with 7-day period (weekly)
      const weeklyData = {
        body: {
          payRuns: [
            {
              payRunID: 'weekly-run',
              paymentDate: '2025-05-07',
              periodStartDate: '2025-05-01',
              periodEndDate: '2025-05-07', // 7 days = weekly
              totalCost: 5000,
              employeeCount: 5,
              status: 'SCHEDULED'
            }
          ]
        }
      };
      
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getPayrollPayRuns.mockResolvedValue(weeklyData);
      
      const results = await payrollService.getPayrollExpensesForDisplay('tenant-id');
      
      expect(results[0].frequency).toBe('weekly');
    });

    it('should determine fortnightly frequency correctly', async () => {
      // Set up test data with 14-day period (fortnightly)
      const fortnightlyData = {
        body: {
          payRuns: [
            {
              payRunID: 'fortnightly-run',
              paymentDate: '2025-05-14',
              periodStartDate: '2025-05-01',
              periodEndDate: '2025-05-14', // 14 days = fortnightly
              totalCost: 10000,
              employeeCount: 5,
              status: 'SCHEDULED'
            }
          ]
        }
      };
      
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getPayrollPayRuns.mockResolvedValue(fortnightlyData);
      
      const results = await payrollService.getPayrollExpensesForDisplay('tenant-id');
      
      expect(results[0].frequency).toBe('fortnightly');
    });

    it('should determine monthly frequency correctly', async () => {
      // Set up test data with 30-day period (monthly)
      const monthlyData = {
        body: {
          payRuns: [
            {
              payRunID: 'monthly-run',
              paymentDate: '2025-05-30',
              periodStartDate: '2025-05-01',
              periodEndDate: '2025-05-30', // 30 days = monthly
              totalCost: 20000,
              employeeCount: 5,
              status: 'SCHEDULED'
            }
          ]
        }
      };
      
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getPayrollPayRuns.mockResolvedValue(monthlyData);
      
      const results = await payrollService.getPayrollExpensesForDisplay('tenant-id');
      
      expect(results[0].frequency).toBe('monthly');
    });
  });

  describe('status mapping', () => {
    it('should map all status values correctly', async () => {
      // Set up test data with different statuses
      const statusData = {
        body: {
          payRuns: [
            {
              payRunID: 'draft-run',
              paymentDate: '2025-05-15',
              periodStartDate: '2025-05-01',
              periodEndDate: '2025-05-14',
              totalCost: 10000,
              employeeCount: 5,
              status: 'DRAFT'
            },
            {
              payRunID: 'scheduled-run',
              paymentDate: '2025-05-15',
              periodStartDate: '2025-05-01',
              periodEndDate: '2025-05-14',
              totalCost: 10000,
              employeeCount: 5,
              status: 'SCHEDULED'
            },
            {
              payRunID: 'completed-run',
              paymentDate: '2025-05-15',
              periodStartDate: '2025-05-01',
              periodEndDate: '2025-05-14',
              totalCost: 10000,
              employeeCount: 5,
              status: 'COMPLETED'
            },
            {
              payRunID: 'unknown-run',
              paymentDate: '2025-05-15',
              periodStartDate: '2025-05-01',
              periodEndDate: '2025-05-14',
              totalCost: 10000,
              employeeCount: 5,
              status: 'SOMETHINGELSE'
            }
          ]
        }
      };
      
      mockXeroClient.readTokenSet.mockReturnValue(mockTokenWithScope);
      mockXeroClient.getPayrollPayRuns.mockResolvedValue(statusData);
      
      const results = await payrollService.getPayrollExpensesForDisplay('tenant-id');
      
      expect(results[0].status).toBe('Draft');
      expect(results[1].status).toBe('Scheduled');
      expect(results[2].status).toBe('Completed');
      expect(results[3].status).toBe('Unknown');
    });
  });
});