import { test } from '@playwright/test';
import fs from 'fs';
import path from 'path';

// Define screen sizes to test
const screenSizes = [
  { name: 'mobile', width: 375, height: 667 },
  { name: 'tablet', width: 768, height: 1024 },
  { name: 'desktop', width: 1280, height: 800 },
];

// Ensure screenshots directory exists
const screenshotsDir = path.join(process.cwd(), 'tests/e2e/screenshots');
if (!fs.existsSync(screenshotsDir)){
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

test.describe('Visual Tests', () => {
  test('capture home page', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    await page.screenshot({
      path: path.join(screenshotsDir, 'homepage.png'),
      fullPage: true
    });
  });
  
  // Test responsive views
  for (const size of screenSizes) {
    test(`homepage at ${size.name} size`, async ({ page }) => {
      // Set viewport to target size
      await page.setViewportSize({
        width: size.width,
        height: size.height
      });
      
      await page.goto('/');
      await page.waitForTimeout(1000);
      
      await page.screenshot({
        path: path.join(screenshotsDir, `${size.name}-homepage.png`),
        fullPage: true
      });
    });
  }
});