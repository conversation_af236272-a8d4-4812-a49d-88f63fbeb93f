# E2E Testing with <PERSON><PERSON> for Onbord Financial Dashboard

This directory contains end-to-end tests using <PERSON><PERSON> for the Onbord Financial Dashboard application. These tests focus on proper handling of Xero authentication with MFA requirements.

## Test Files

- `load.spec.ts` - Basic test to verify the application loads
- `visual.spec.ts` - Captures screenshots of key pages at different screen sizes
- `accessibility.spec.ts` - Tests for accessibility standards
- **`auth-setup.ts`** - Global setup script for authentication with Xero (including MFA)
- **`dashboard.spec.ts`** - Tests for dashboard components and navigation
- **`cashflow.spec.ts`** - Tests for cashflow projection and chart features
- **`expenses.spec.ts`** - Tests for custom expenses management
- **`xero-integration.spec.ts`** - Tests for Xero data integration
- `utils.ts` - Helper functions for testing

## Authentication Approach

Since **all meaningful tests require successful authentication** with Xero, and Xero uses MFA which cannot be automated, we've implemented a two-phase testing approach:

### 1. Authentication Setup Phase

Before running tests, you must complete the authentication setup:

```bash
npm run test:e2e:auth-setup
```

This will:
- Open a browser window in UI mode
- Navigate to the Xero login page
- Prompt you to complete the authentication manually, including MFA
- Save the authenticated state to `auth.json` once completed

**Important**: You only need to do this once, or whenever authentication expires.

### 2. Test Execution Phase

After completing authentication setup, you can run the tests:

```bash
npm run test:e2e
```

All tests will:
- Load the saved authentication state from `auth.json`
- Verify authentication is valid before proceeding
- Fail with a clear message if authentication is expired

## Running Tests

```bash
# First time: Set up authentication
npm run test:e2e:auth-setup

# Run all tests
npm run test:e2e

# Run specific test suites
npm run test:e2e:dashboard
npm run test:e2e:cashflow
npm run test:e2e:expenses
npm run test:e2e:xero

# Run with UI mode for debugging
npm run test:e2e:ui

# Run a specific test file
npx playwright test tests/e2e/dashboard.spec.ts

# Run tests with headed browser
npx playwright test --headed
```

## Authentication Troubleshooting

If tests fail with authentication errors:

1. Check if `auth.json` exists in the project root
2. Run the auth setup script again: `npm run test:e2e:auth-setup`
3. Make sure you complete the entire Xero authentication flow, including MFA
4. Check for clear browser cookies or incognito windows which might interfere

## Screenshots

Screenshots are saved to the `tests/e2e/screenshots` directory. These are useful for:

1. Visual regression testing (manually comparing screenshots)
2. Debugging failed tests
3. Documentation of application state

## CI/CD Integration

For CI/CD pipelines:

- Consider using a pre-authenticated `auth.json` file (securely stored)
- Require manual authentication before pipeline execution
- Set appropriate timeout values for tests that rely on external data

## Test Development Guidelines

When adding new tests:

1. Always verify authentication at the start of each test
2. Use the utility functions in `utils.ts` for common operations
3. Keep tests simple and focused on specific functionality
4. Handle failures gracefully with clear error messages when authentication is invalid