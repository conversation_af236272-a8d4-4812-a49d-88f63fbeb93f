import { test, expect } from '@playwright/test';
import { waitForStableState, verifyAuthentication } from './utils';
import path from 'path';

/**
 * Test suite for cashflow projection functionality
 * All tests require successful Xero authentication
 */
test.describe('Cashflow Projection Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for page to be in stable state
    await waitForStableState(page);
    
    // Verify authentication is valid before proceeding
    await verifyAuthentication(page);
    
    // Ensure we're on the Cashflow Projection tab using JavaScript for reliability
    await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('nav button, button'));
      const cashflowButton = buttons.find(btn => btn.textContent.includes('Cashflow Projection'));
      if (cashflowButton) cashflowButton.click();
    });
    await waitForStableState(page);
  });
  
  test('Cashflow chart displays correctly', async ({ page }) => {
    // Verify chart container is visible
    const chartContainer = page.locator('[data-testid="cashflow-chart"], .cashflow-chart, .recharts-responsive-container');
    await expect(chartContainer).toBeVisible();
    
    // Verify chart has rendered by checking for svg elements
    await expect(chartContainer.locator('svg')).toBeVisible();
    await expect(chartContainer.locator('path')).toBeVisible();
    
    // Verify chart axes are present
    await expect(chartContainer.locator('.recharts-xaxis')).toBeVisible();
    await expect(chartContainer.locator('.recharts-yaxis')).toBeVisible();
    
    // Take a screenshot of the chart
    await chartContainer.screenshot({ 
      path: path.join(process.cwd(), 'tests/e2e/screenshots', 'cashflow-chart.png')
    });
  });
  
  test('Date range filters affect data properly', async ({ page }) => {
    // Find date range controls
    const dateControls = page.locator('[data-testid="date-controls"], .date-controls, .date-range-selector');
    await expect(dateControls).toBeVisible();
    
    // Get initial chart state for comparison
    const initialBars = await page.locator('.recharts-bar-rectangle').count();
    
    // Find and click date range selector (select a different period)
    // Adjust these selectors based on your app's actual controls
    const dateRangeButton = dateControls.locator('button, select').first();
    await dateRangeButton.click();
    
    // Select a different option (e.g., "3 months", "6 months", etc.)
    await page.locator('text=3 months, text="3 months", [value="3-months"]').first().click();
    
    // Wait for chart to update
    await waitForStableState(page);
    
    // Verify the chart data has changed (number of bars might be different)
    const newBars = await page.locator('.recharts-bar-rectangle').count();
    
    // Get textual version of data for logging
    const dateRangeText = await dateControls.textContent();
    console.log(`Date range selection: ${dateRangeText}`);
    console.log(`Initial bars: ${initialBars}, New bars: ${newBars}`);
    
    // When date range changes, chart data should update
    // If implementation allows, check for specific changes
    // Otherwise, verify chart is still visible with data
    await expect(page.locator('.recharts-bar-rectangle')).toBeVisible();
  });
  
  test('Chart tooltip functionality', async ({ page }) => {
    // Find chart areas (bars, lines) to hover over
    const chartBars = page.locator('.recharts-bar-rectangle');
    await expect(chartBars.first()).toBeVisible();
    
    // Hover over a chart element
    await chartBars.first().hover();
    
    // Wait for tooltip to appear
    const tooltip = page.locator('.recharts-tooltip-wrapper, .chart-tooltip');
    await expect(tooltip).toBeVisible({ timeout: 2000 });
    
    // Verify tooltip contains expected information
    const tooltipText = await tooltip.textContent();
    
    // Tooltip should contain some data (amount, date, etc.)
    expect(tooltipText).toBeTruthy();
    expect(tooltipText?.length).toBeGreaterThan(5);
    
    // Optional: Take a screenshot with tooltip visible
    await page.screenshot({ 
      path: path.join(process.cwd(), 'tests/e2e/screenshots', 'cashflow-chart-tooltip.png')
    });
  });
});