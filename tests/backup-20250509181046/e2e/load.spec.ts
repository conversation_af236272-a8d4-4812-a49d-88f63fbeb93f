import { test, expect } from '@playwright/test';
import { waitForStableState, getPageInfo } from './utils';
import path from 'path';

test('application loads successfully', async ({ page }) => {
  // Navigate to app
  await page.goto('/');
  
  // Wait for the page to be stable
  await waitForStableState(page);
  
  // Verify something loaded - basic check for body
  await expect(page.locator('body')).toBeVisible();
  
  // Check for minimum content - look for key elements that should be on any page
  const hasContent = await page.locator('body').textContent();
  expect(hasContent?.length).toBeGreaterThan(50); // Arbitrary length but should have some content
  
  // Take a screenshot
  await page.screenshot({ 
    path: path.join(process.cwd(), 'tests/e2e/screenshots', 'app-loaded.png') 
  });
  
  // Log basic info about the page for debugging
  const pageInfo = await getPageInfo(page);
  console.log('Page info:', JSON.stringify(pageInfo, null, 2));
});