import { test, expect } from '@playwright/test';
import { waitForStableState, verifyAuthentication } from './utils';
import path from 'path';

/**
 * Test suite for Xero data integration functionality
 * All tests require successful Xero authentication
 */
test.describe('Xero Data Integration Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for page to be in stable state
    await waitForStableState(page);
    
    // Verify authentication is valid before proceeding
    await verifyAuthentication(page);
  });
  
  test('Xero accounts display correctly', async ({ page }) => {
    // Navigate to cashflow projection tab where accounts should be visible using JavaScript for reliability
    await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('nav button, button'));
      const cashflowButton = buttons.find(btn => btn.textContent.includes('Cashflow Projection'));
      if (cashflowButton) cashflowButton.click();
    });
    await waitForStableState(page);
    
    // Check for accounts section, which might be in a dropdown, settings panel, or directly on page
    const accountsSection = page.locator('[data-testid="xero-accounts"], .xero-accounts, text="Xero Accounts"');
    
    // If accounts section isn't immediately visible, check if it's in a dropdown or settings panel
    if (await accountsSection.isVisible().catch(() => false) === false) {
      // Try to open settings or accounts menu
      const settingsButton = page.locator('button:has-text("Settings"), [data-testid="settings-button"]');
      if (await settingsButton.isVisible().catch(() => false)) {
        await settingsButton.click();
        await waitForStableState(page);
      }
    }
    
    // Look for account names or bank account information
    // Adapt these selectors based on how accounts are displayed in your application
    const accountItems = page.locator('[data-testid="account-item"], .account-item, .bank-account');
    
    // Verify at least one account is displayed
    const accountCount = await accountItems.count();
    
    // Log information about accounts found
    for (let i = 0; i < Math.min(accountCount, 3); i++) {
      console.log(`Account ${i + 1}: ${await accountItems.nth(i).textContent()}`);
    }
    
    // We should have at least one account from Xero
    expect(accountCount).toBeGreaterThan(0);
  });
  
  test('Bank account selection affects reports', async ({ page }) => {
    // Navigate to cashflow projection tab using JavaScript for reliability
    await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('nav button, button'));
      const cashflowButton = buttons.find(btn => btn.textContent.includes('Cashflow Projection'));
      if (cashflowButton) cashflowButton.click();
    });
    await waitForStableState(page);
    
    // Find account selection control
    // Adapt selectors based on your application
    const accountSelector = page.locator('[data-testid="account-selector"], select.account-selector, .account-dropdown');
    
    // If account selector isn't visible, check if it's in settings
    if (await accountSelector.isVisible().catch(() => false) === false) {
      // Try to open settings menu
      const settingsButton = page.locator('button:has-text("Settings"), [data-testid="settings-button"]');
      if (await settingsButton.isVisible()) {
        await settingsButton.click();
        await waitForStableState(page);
      }
    }
    
    // If we have an account selector, test changing it
    if (await accountSelector.isVisible()) {
      // Take screenshot before changing account
      await page.screenshot({ 
        path: path.join(process.cwd(), 'tests/e2e/screenshots', 'before-account-change.png')
      });
      
      // Get current account selection text for logging
      const beforeAccountText = await accountSelector.textContent();
      console.log(`Before account selection: ${beforeAccountText}`);
      
      // Select a different account
      // If it's a select element
      if (await page.locator('select.account-selector').isVisible().catch(() => false)) {
        // Get the select element
        const select = page.locator('select.account-selector');
        // Get the current value
        const currentValue = await select.evaluate(sel => (sel as HTMLSelectElement).value);
        // Get all available options
        const options = await select.evaluate(sel => {
          return Array.from((sel as HTMLSelectElement).options)
            .map(opt => opt.value)
            .filter(value => value !== '');
        });
        
        // Find a different option to select
        const differentOption = options.find(opt => opt !== currentValue);
        if (differentOption) {
          await select.selectOption(differentOption);
          console.log(`Selected different account: ${differentOption}`);
        } else {
          console.log('Could not find a different account to select');
        }
      } 
      // If it's a custom dropdown or button that opens options
      else {
        await accountSelector.click();
        // Wait for dropdown to appear
        await waitForStableState(page);
        
        // Select a different option (first option that's not currently selected)
        const options = page.locator('[data-testid="account-option"], .account-option');
        const optionsCount = await options.count();
        
        if (optionsCount > 1) {
          await options.nth(1).click();
          console.log('Selected second account option');
        } else if (optionsCount > 0) {
          await options.first().click();
          console.log('Only one account option available, selected it');
        }
      }
      
      // Wait for report to update
      await waitForStableState(page, 10000); // Longer timeout for data refresh
      
      // Take screenshot after changing account
      await page.screenshot({ 
        path: path.join(process.cwd(), 'tests/e2e/screenshots', 'after-account-change.png')
      });
      
      // Get updated account selection text
      const afterAccountText = await accountSelector.textContent();
      console.log(`After account selection: ${afterAccountText}`);
      
      // Compare screenshots or check that data has changed
      // We can only verify the account was changed and the page still shows data
      await expect(page.locator('.recharts-responsive-container')).toBeVisible();
    } else {
      console.log('No account selector found, skipping account change test');
      test.skip();
    }
  });
  
  test('Transaction data matches expected format', async ({ page }) => {
    // Navigate to the projection page where transactions might be shown using JavaScript for reliability
    await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('nav button, button'));
      const cashflowButton = buttons.find(btn => btn.textContent.includes('Cashflow Projection'));
      if (cashflowButton) cashflowButton.click();
    });
    await waitForStableState(page);
    
    // Look for transactions list - might be in a separate tab, expand button, or section
    const transactionsSection = page.locator('[data-testid="transactions-section"], .transactions-list, text="Recent Transactions"');
    
    // If transactions aren't immediately visible, check if we need to open a section or tab
    if (await transactionsSection.isVisible().catch(() => false) === false) {
      // Try clicking "transactions" tab or button if it exists
      const transactionsTab = page.locator('button:has-text("Transactions"), [data-testid="transactions-tab"]');
      if (await transactionsTab.isVisible().catch(() => false)) {
        await transactionsTab.click();
        await waitForStableState(page);
      }
      
      // Try expanding transactions section if it exists
      const expandButton = page.locator('[data-testid="expand-transactions"], button:has-text("View Transactions")');
      if (await expandButton.isVisible().catch(() => false)) {
        await expandButton.click();
        await waitForStableState(page);
      }
    }
    
    // Look for transaction items
    const transactionItems = page.locator('[data-testid="transaction-item"], .transaction-item, tr:has(.transaction-date)');
    
    // Check if we have transaction items
    const transactionCount = await transactionItems.count();
    
    // If we have transactions, verify their format
    if (transactionCount > 0) {
      console.log(`Found ${transactionCount} transactions`);
      
      // Check first transaction for expected elements
      const firstTransaction = transactionItems.first();
      
      // Verify transaction has date
      const dateElement = firstTransaction.locator('.transaction-date, [data-testid="transaction-date"]');
      const hasDate = await dateElement.isVisible().catch(() => false);
      
      // Verify transaction has amount
      const amountElement = firstTransaction.locator('.transaction-amount, [data-testid="transaction-amount"]');
      const hasAmount = await amountElement.isVisible().catch(() => false);
      
      // Verify transaction has description or reference
      const descElement = firstTransaction.locator('.transaction-description, [data-testid="transaction-description"]');
      const hasDescription = await descElement.isVisible().catch(() => false);
      
      // Log transaction details
      if (hasDate) console.log(`Transaction date: ${await dateElement.textContent()}`);
      if (hasAmount) console.log(`Transaction amount: ${await amountElement.textContent()}`);
      if (hasDescription) console.log(`Transaction description: ${await descElement.textContent()}`);
      
      // Take screenshot of transactions
      await transactionItems.first().scrollIntoViewIfNeeded();
      await page.screenshot({ 
        path: path.join(process.cwd(), 'tests/e2e/screenshots', 'xero-transactions.png')
      });
      
      // Expect transaction to have key elements
      expect(hasDate || hasAmount || hasDescription).toBeTruthy();
    } else {
      console.log('No transaction items found, skipping format verification');
      // Instead check if there's a "no transactions" message
      const noTransactionsMsg = page.locator('text="No transactions", text="No recent transactions"');
      const hasNoTransactionsMsg = await noTransactionsMsg.isVisible().catch(() => false);
      
      if (hasNoTransactionsMsg) {
        console.log('Found "No transactions" message');
      } else {
        console.log('Transactions section not found or empty');
      }
    }
  });
});