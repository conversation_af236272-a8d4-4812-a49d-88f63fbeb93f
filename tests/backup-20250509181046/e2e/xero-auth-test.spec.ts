import { test, expect } from '@playwright/test';
import { waitForStableState } from './utils';
import fs from 'fs';

/**
 * Test file for Xero authentication
 * 
 * This test:
 * 1. Navigates to the app
 * 2. Initiates Xero authentication flow
 * 3. Waits for human intervention to complete MFA
 * 4. Detects successful authentication
 * 5. Saves the authenticated state for use in tests
 * 
 * This test is skipped by default when run as part of the regular test suite.
 * Run it explicitly with: npm run test:e2e:auth-setup
 */
test('Authenticate with Xero (requires human intervention for MFA)', async ({ page, context }) => {
  // Skip this test when running as part of the regular test suite
  // It should only be run explicitly with npm run test:e2e:auth-setup
  if (process.env.npm_lifecycle_event === 'test:e2e') {
    test.skip();
    return;
  }
  console.log('\n🔐 Starting Xero authentication...\n');
  
  try {
    // Navigate to the app
    console.log('🌐 Navigating to application...');
    await page.goto('/');
    
    // Wait for page to be stable
    await waitForStableState(page);
    
    // Check if we need to authenticate by looking for the Connect to Xero button
    const needsAuth = await page.locator('button:has-text("Connect to Xero")')
        .isVisible({ timeout: 5000 })
        .catch(() => true); // Default to true if element not found
        
    if (needsAuth) {
      console.log('🔑 Authentication required. Initiating Xero login flow...');
      
      // Click the Xero login button using JavaScript to avoid stability issues
      await page.evaluate(() => {
        // Find and click the Connect to Xero button
        const buttons = Array.from(document.querySelectorAll('button'));
        const xeroButton = buttons.find(btn => btn.textContent.includes('Connect to Xero'));
        if (xeroButton) {
          console.log('Found Connect to Xero button, clicking it');
          xeroButton.click();
        } else {
          console.log('Connect to Xero button not found via JavaScript');
        }
      });
      
      // Provide clear instructions to the human tester
      console.log('\n⚠️ HUMAN INTERVENTION REQUIRED ⚠️');
      console.log('Please complete the Xero login process including MFA verification.');
      console.log('The browser will wait until authentication is complete.');
      console.log('You will need to:');
      console.log('1. Enter your Xero credentials');
      console.log('2. Complete any MFA challenges');
      console.log('3. Approve the necessary permissions');
      console.log('\nWaiting for authentication to complete...\n');
      
      // Wait for successful authentication by looking for dashboard navigation elements
      // Timeout is high (5 minutes) to allow for human intervention
      await Promise.race([
        page.waitForSelector('button:has-text("Cashflow Projection")', { timeout: 300000 }),
        page.waitForSelector('button:has-text("Custom Expenses")', { timeout: 300000 })
      ]);
      
      console.log('✅ Authentication detected! Saving authentication state...');
    } else {
      console.log('✅ Already authenticated. Refreshing and saving auth state...');
    }
    
    // Save authentication state to a file
    const authFile = './auth.json';
    await context.storageState({ path: authFile });
    
    // Get file stats to confirm save
    const stats = fs.statSync(authFile);
    console.log(`💾 Auth state saved to ${authFile} (${stats.size} bytes)`);
    
    // Validate authentication state was properly saved
    expect(stats.size).toBeGreaterThan(100);
    
    console.log('\n✅ Auth setup complete! You can now run your tests.');
    console.log('To run tests with this authentication state:');
    console.log('npx playwright test\n');
    
  } catch (error) {
    console.error('\n❌ Auth test failed:', error);
    test.fail();
  }
});
