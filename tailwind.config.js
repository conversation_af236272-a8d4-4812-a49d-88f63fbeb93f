/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // Enable dark mode with class strategy
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      // === CONSOLIDATED COLOR SYSTEM ===
      colors: {
        // Primary brand colors (consolidated from App.css variables)
        primary: {
          50: '#eff8ff',
          100: '#dbeafe', 
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#2870AB', // Main primary color
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e3a8a',
          900: '#1e40af',
          DEFAULT: '#2870AB',
          dark: '#2870AB',
          light: '#C7DEF2',
        },
        
        // Secondary colors  
        secondary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#4CB5F5', // Main secondary color
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          DEFAULT: '#4CB5F5',
          dark: '#6B7280',
          light: '#F5EBE0',
        },

        // Utility colors (consolidated from App.css)
        accent: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#e74c3c', // Main accent/error color
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
          DEFAULT: '#e74c3c'
        },

        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#27ae60', // Main success color
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
          DEFAULT: '#27ae60'
        },

        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f39c12', // Main warning color
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
          DEFAULT: '#f39c12'
        },

        // Chart specific colors
        chart: {
          actual: '#3498db',
          projected: '#f39c12',
          income: '#27ae60',
          expense: '#e74c3c'
        },

        // Semantic background colors
        background: {
          DEFAULT: '#f9f9f9',
          dark: '#121212',
          card: '#ffffff',
          'card-dark': '#1f2937'
        },

        // Border colors
        border: {
          DEFAULT: '#ddd',
          dark: '#333',
          light: '#e5e7eb'
        },

        // Text colors
        text: {
          DEFAULT: '#333',
          dark: '#e0e0e0',
          muted: '#777',
          'muted-dark': '#9ca3af'
        },

        // Legacy color mappings for blue
        blue: {
          logo: '#2870AB',
        }
      },

      // === FLUID TYPOGRAPHY SYSTEM ===
      // Consolidated from short-term-cashflow.css CSS variables
      fontSize: {
        'fluid-xs': 'clamp(0.65rem, 0.5vw + 0.5rem, 0.75rem)',
        'fluid-sm': 'clamp(0.75rem, 0.75vw + 0.5rem, 0.875rem)', 
        'fluid-base': 'clamp(0.875rem, 1vw + 0.5rem, 1rem)',
        'fluid-lg': 'clamp(1rem, 1.25vw + 0.5rem, 1.125rem)',
        'fluid-xl': 'clamp(1.125rem, 1.5vw + 0.5rem, 1.25rem)',
        'fluid-2xl': 'clamp(1.25rem, 2vw + 0.5rem, 1.5rem)',
      },

      // === FLUID SPACING SYSTEM ===
      // Consolidated from short-term-cashflow.css CSS variables
      spacing: {
        'fluid-xs': 'clamp(0.25rem, 0.25vw + 0.25rem, 0.5rem)',
        'fluid-sm': 'clamp(0.5rem, 0.5vw + 0.25rem, 0.75rem)',
        'fluid-md': 'clamp(0.75rem, 0.75vw + 0.25rem, 1rem)', 
        'fluid-lg': 'clamp(1rem, 1vw + 0.5rem, 1.5rem)',
        'fluid-xl': 'clamp(1.5rem, 1.5vw + 0.5rem, 2rem)',
      },

      // === FLUID WIDTH SYSTEM ===
      // Consolidated from short-term-cashflow.css CSS variables
      maxWidth: {
        'fluid-xs': 'clamp(16rem, 30vw, 20rem)',
        'fluid-sm': 'clamp(20rem, 40vw, 24rem)',
        'fluid-md': 'clamp(24rem, 60vw, 28rem)',
        'fluid-lg': 'clamp(28rem, 80vw, 32rem)',
        'fluid-xl': 'clamp(32rem, 90vw, 36rem)',
      },

      // === ENHANCED BOX SHADOWS ===
      boxShadow: {
        card: '0 2px 4px rgba(0, 0, 0, 0.05)',
        'card-hover': '0 4px 8px rgba(0, 0, 0, 0.1)',
        'card-dark': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'floating': '0 10px 25px rgba(0, 0, 0, 0.15)',
      },

      // === TYPOGRAPHY ===
      fontFamily: {
        sans: ['Segoe UI', 'Tahoma', 'Geneva', 'Verdana', 'sans-serif'],
      },

      // === ENHANCED ANIMATIONS ===
      animation: {
        fadeIn: 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out forwards',
        'slide-in-left': 'slideInLeft 0.3s ease-out forwards',
        'slide-out': 'slideOut 0.3s ease-in forwards',
        'slide-out-left': 'slideOutLeft 0.3s ease-in forwards',
        'spin-slow': 'spin 3s linear infinite',
        'spin-reverse': 'spin-reverse 2.5s linear infinite',
        'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'ellipsis1': 'ellipsis 1.4s infinite 0s',
        'ellipsis2': 'ellipsis 1.4s infinite 0.2s',
        'ellipsis3': 'ellipsis 1.4s infinite 0.4s',
        'progress-indeterminate': 'progress-indeterminate 1.5s ease-in-out infinite',
        // New smooth transitions
        'scale-in': 'scale-in 0.2s ease-out',
        'scale-out': 'scale-out 0.15s ease-in',
      },

      keyframes: {
        fadeIn: {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
        slideIn: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOut: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' },
        },
        slideOutLeft: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        'spin-reverse': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(-360deg)' },
        },
        ellipsis: {
          '0%': { opacity: 0 },
          '50%': { opacity: 1 },
          '100%': { opacity: 0 },
        },
        'progress-indeterminate': {
          '0%': { transform: 'translateX(-100%)' },
          '50%': { transform: 'translateX(0%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'scale-in': {
          '0%': { transform: 'scale(0.95)', opacity: 0 },
          '100%': { transform: 'scale(1)', opacity: 1 },
        },
        'scale-out': {
          '0%': { transform: 'scale(1)', opacity: 1 },
          '100%': { transform: 'scale(0.95)', opacity: 0 },
        },
      },

      // === ENHANCED TRANSITIONS ===
      transitionProperty: {
        'height': 'height',
        'spacing': 'margin, padding',
        'colors': 'color, background-color, border-color, text-decoration-color, fill, stroke',
        'opacity': 'opacity',
        'shadow': 'box-shadow',
        'transform': 'transform',
      },

      transitionDuration: {
        '250': '250ms',
        '400': '400ms',
      },

      transitionTimingFunction: {
        'out-expo': 'cubic-bezier(0.16, 1, 0.3, 1)',
        'in-expo': 'cubic-bezier(0.7, 0, 0.84, 0)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/container-queries'),
  ],
}