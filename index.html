<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, viewport-fit=cover" />
  <title>Upstream by onbord</title>
  <meta name="description" content="Financial analysis dashboard by onbord" />
  <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
  <style>
    /* AG Grid CSS variables at the root level - must be here before any scripts */
    :root {
      --ag-row-height: 42px !important;
      --ag-header-height: 48px !important;
      --ag-list-item-height: 24px !important;
    }
    
    /* Essential AG Grid styles for consistent table rendering */
    .ag-root-wrapper {
      /* Fix for ResizeObserver loop limit errors */
      contain: content;
      padding-bottom: 1px;
      overflow: clip;
    }
    
    /* Basic AG Grid essentials that might be missing */
    .ag-theme-alpine,
    .ag-theme-alpine-dark {
      --ag-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      --ag-font-size: 0.875rem;
      font-family: var(--ag-font-family);
      font-size: var(--ag-font-size);
    }
    
    /* Ensure cell styling works properly */
    .ag-cell-positive {
      color: #16a34a !important; /* green-600 */
    }
    
    .ag-cell-negative {
      color: #dc2626 !important; /* red-600 */
    }
    
    .dark .ag-cell-positive {
      color: #4ade80 !important; /* green-400 */
    }
    
    .dark .ag-cell-negative {
      color: #f87171 !important; /* red-400 */
    }
  </style>
  <script>
    // Polyfill to suppress ResizeObserver loop limit errors
    const debounce = (fn, delay) => {
      let timer = null;
      return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      };
    };

    // Overriding the ResizeObserver to add debouncing
    window.addEventListener('load', function() {
      const originalResizeObserver = window.ResizeObserver;
      if (originalResizeObserver) {
        window.ResizeObserver = class ResizeObserver extends originalResizeObserver {
          constructor(callback) {
            const debouncedCallback = debounce(callback, 20);
            super(debouncedCallback);
          }
        };
      }
    });
  </script>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/index.tsx"></script>
</body>
</html>
