/**
 * Test script for the relationship models implementation
 *
 * This script tests:
 * 1. Company-Company relationships
 * 2. Contact-Company relationships
 * 3. Deal-Contact relationships
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Get database path
const dataDir = process.env.DATA_DIR || path.join(__dirname, '..', 'data');
const dbPath = path.join(dataDir, 'upstream.db');

console.log(`Using database at: ${dbPath}`);

// Connect to the database
const db = new BetterSqlite3(dbPath);
console.log('Successfully connected to database');

// Create log directory and file
const logDir = path.join(__dirname, '..', 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

const logFilePath = path.join(logDir, `relationship_test_${new Date().toISOString().replace(/:/g, '-')}.log`);
const logStream = fs.createWriteStream(logFilePath, { flags: 'a' });

// Logging helper function
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  logStream.write(logMessage + '\n');
}

// Error logging helper
function logError(message, error) {
  const errorMessage = `ERROR: ${message} - ${error.message}\n${error.stack || ''}`;
  log(errorMessage);
}

// Helper to create test companies
function createTestCompany(name, source = 'Manual') {
  const companyId = uuidv4();
  const now = new Date().toISOString();
  
  try {
    db.prepare(`
      INSERT INTO company (
        id, name, industry, source, created_at, updated_at, created_by, updated_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      companyId,
      name,
      'Test Industry',
      source,
      now,
      now,
      'test-script',
      'test-script'
    );
    
    log(`Created test company: ${name} (${companyId})`);
    return { id: companyId, name };
  } catch (error) {
    logError(`Failed to create test company ${name}`, error);
    throw error;
  }
}

// Helper to create test contacts
function createTestContact(firstName, lastName, companyId = null) {
  const contactId = uuidv4();
  const now = new Date().toISOString();
  
  try {
    db.prepare(`
      INSERT INTO contact (
        id, first_name, last_name, company_id, source, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(
      contactId,
      firstName,
      lastName,
      companyId,
      'Manual',
      now,
      now
    );
    
    log(`Created test contact: ${firstName} ${lastName} (${contactId})`);
    return { id: contactId, firstName, lastName };
  } catch (error) {
    logError(`Failed to create test contact ${firstName} ${lastName}`, error);
    throw error;
  }
}

// Helper to create test deals
function createTestDeal(name, companyId, value = 10000) {
  const dealId = uuidv4();
  const now = new Date().toISOString();
  
  try {
    db.prepare(`
      INSERT INTO deal (
        id, name, stage, value, currency, company_id, source, created_at, updated_at, created_by, updated_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      dealId,
      name,
      'Qualified',
      value,
      'AUD',
      companyId,
      'Manual',
      now,
      now,
      'test-script',
      'test-script'
    );
    
    log(`Created test deal: ${name} (${dealId})`);
    return { id: dealId, name };
  } catch (error) {
    logError(`Failed to create test deal ${name}`, error);
    throw error;
  }
}

// Create company relationship
function createCompanyRelationship(parentId, childId, relationshipType) {
  const now = new Date().toISOString();
  
  try {
    db.prepare(`
      INSERT INTO company_relationship (
        parent_company_id, child_company_id, relationship_type, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      parentId,
      childId,
      relationshipType,
      now,
      'test-script'
    );
    
    log(`Created company relationship: ${parentId} -> ${childId} (${relationshipType})`);
    return true;
  } catch (error) {
    logError(`Failed to create company relationship`, error);
    throw error;
  }
}

// Associate contact with company
function associateContactWithCompany(contactId, companyId, isPrimary = false, role = null) {
  const now = new Date().toISOString();
  
  try {
    db.prepare(`
      INSERT INTO contact_company (
        contact_id, company_id, role, is_primary, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).run(
      contactId,
      companyId,
      role,
      isPrimary ? 1 : 0,
      now,
      'test-script'
    );
    
    log(`Associated contact ${contactId} with company ${companyId} (role: ${role || 'none'}, primary: ${isPrimary})`);
    return true;
  } catch (error) {
    logError(`Failed to associate contact with company`, error);
    throw error;
  }
}

// Add contact to deal
function addContactToDeal(dealId, contactId, role) {
  const now = new Date().toISOString();
  
  try {
    db.prepare(`
      INSERT INTO deal_contact (
        deal_id, contact_id, role, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      dealId,
      contactId,
      role,
      now,
      'test-script'
    );
    
    log(`Added contact ${contactId} to deal ${dealId} (role: ${role})`);
    return true;
  } catch (error) {
    logError(`Failed to add contact to deal`, error);
    throw error;
  }
}

// Clean up test data
function cleanupTestData() {
  log('Cleaning up test data...');
  
  try {
    // Use a transaction to ensure atomicity
    db.exec('BEGIN TRANSACTION');
    
    // Delete all test data created by this script
    db.prepare('DELETE FROM deal_contact WHERE created_by = ?').run('test-script');
    db.prepare('DELETE FROM contact_company WHERE created_by = ?').run('test-script');
    db.prepare('DELETE FROM company_relationship WHERE created_by = ?').run('test-script');
    
    // Delete deals created by this script
    db.prepare('DELETE FROM deal WHERE created_by = ?').run('test-script');
    
    // Delete contacts created by this script (using created_at since contacts don't have created_by)
    db.prepare('DELETE FROM contact WHERE source = ?').run('Manual');
    
    // Delete companies created by this script
    db.prepare('DELETE FROM company WHERE created_by = ?').run('test-script');
    
    db.exec('COMMIT');
    log('Test data cleanup complete');
  } catch (error) {
    db.exec('ROLLBACK');
    logError('Failed to clean up test data', error);
  }
}

// Main test function
async function runTests() {
  log('Starting relationship model tests');
  
  try {
    // Create test data
    log('Creating test companies...');
    const parentCompany = createTestCompany('Parent Company Ltd');
    const childCompany1 = createTestCompany('Subsidiary One Pty Ltd');
    const childCompany2 = createTestCompany('Subsidiary Two Pty Ltd');
    const partnerCompany = createTestCompany('Partner Company Ltd');
    
    log('Creating test contacts...');
    const ceo = createTestContact('Jane', 'CEO');
    const cfo = createTestContact('John', 'CFO');
    const manager1 = createTestContact('Mary', 'Manager');
    const manager2 = createTestContact('Bob', 'Manager');
    
    log('Creating test deals...');
    const deal1 = createTestDeal('Big Project', parentCompany.id, 100000);
    const deal2 = createTestDeal('Small Project', childCompany1.id, 25000);
    
    // Test company relationships
    log('\nTesting company relationships...');
    createCompanyRelationship(parentCompany.id, childCompany1.id, 'parent');
    createCompanyRelationship(parentCompany.id, childCompany2.id, 'parent');
    createCompanyRelationship(childCompany1.id, partnerCompany.id, 'partner');
    
    // Verify company relationships
    const parentRelationships = db.prepare(`
      SELECT * FROM company_relationship WHERE parent_company_id = ?
    `).all(parentCompany.id);
    log(`Parent company has ${parentRelationships.length} child relationships`);
    
    // Test contact-company associations
    log('\nTesting contact-company associations...');
    associateContactWithCompany(ceo.id, parentCompany.id, true, 'CEO');
    associateContactWithCompany(cfo.id, parentCompany.id, true, 'CFO');
    associateContactWithCompany(manager1.id, childCompany1.id, true, 'Manager');
    associateContactWithCompany(manager2.id, childCompany2.id, true, 'Manager');
    
    // Test multiple company associations
    associateContactWithCompany(ceo.id, childCompany1.id, false, 'Board Member');
    associateContactWithCompany(ceo.id, childCompany2.id, false, 'Board Member');
    
    // Verify contact-company associations
    const ceoCompanies = db.prepare(`
      SELECT c.name, cc.role, cc.is_primary
      FROM company c
      JOIN contact_company cc ON c.id = cc.company_id
      WHERE cc.contact_id = ?
    `).all(ceo.id);
    log(`CEO has ${ceoCompanies.length} company associations`);
    
    // Test deal-contact associations
    log('\nTesting deal-contact associations...');
    addContactToDeal(deal1.id, ceo.id, 'decision_maker');
    addContactToDeal(deal1.id, cfo.id, 'finance');
    addContactToDeal(deal2.id, manager1.id, 'decision_maker');
    addContactToDeal(deal2.id, cfo.id, 'finance');
    
    // Verify deal-contact associations
    const deal1Contacts = db.prepare(`
      SELECT c.first_name, c.last_name, dc.role
      FROM contact c
      JOIN deal_contact dc ON c.id = dc.contact_id
      WHERE dc.deal_id = ?
    `).all(deal1.id);
    log(`Deal 1 has ${deal1Contacts.length} contacts`);
    
    // Summary of test results
    log('\nTest Results Summary:');
    log(`- Created ${4} test companies`);
    log(`- Created ${4} test contacts`);
    log(`- Created ${2} test deals`);
    log(`- Created ${3} company relationships`);
    log(`- Created ${6} contact-company associations`);
    log(`- Created ${4} deal-contact associations`);
    
    log('\nAll tests completed successfully.');
  } catch (error) {
    logError('Test failed', error);
  } finally {
    // Clean up test data
    cleanupTestData();
    
    // Close database connection
    db.close();
    log('Database connection closed');
    logStream.end();
  }
}

// Run the tests
runTests().catch(error => {
  logError('Unhandled error in tests', error);
  process.exit(1);
});