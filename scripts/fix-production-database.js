#!/usr/bin/env node

/**
 * Fix Production Database Script
 *
 * This script ensures the production database has all required tables
 * and can be run on Render.com after deployment.
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Production database path on Render
const dbPath = '/data/upstream.db';

console.log('=== Production Database Fix Script ===');
console.log(`Database path: ${dbPath}`);

// Check if database file exists
if (!fs.existsSync(dbPath)) {
  console.log('Database file does not exist. It will be created.');
}

// Connect to database
const db = new BetterSqlite3(dbPath);

console.log('Connected to database successfully');

try {
  // Enable foreign keys
  db.pragma('foreign_keys = ON');

  // Check if schema_version table exists
  const schemaExists = db.prepare(`
    SELECT 1 FROM sqlite_master
    WHERE type='table' AND name='schema_version'
  `).get();

  if (!schemaExists) {
    console.log('Database is completely empty. Running full initialization...');

    // Run the full initialization script
    const initScript = path.join(__dirname, 'initialize-fresh-database.js');
    if (fs.existsSync(initScript)) {
      db.close();
      require('child_process').execSync(`node ${initScript}`, {
        stdio: 'inherit',
        env: { ...process.env, DB_PATH: dbPath }
      });
      console.log('Full database initialization completed');
      process.exit(0);
    } else {
      console.error('Initialize script not found. Creating minimal schema...');

      // Create minimal schema inline
      db.transaction(() => {
        // Schema version table
        db.prepare(`
          CREATE TABLE schema_version (
            version INTEGER PRIMARY KEY,
            applied_at TEXT NOT NULL,
            description TEXT NOT NULL
          )
        `).run();

        // Company table
        db.prepare(`
          CREATE TABLE company (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            industry TEXT,
            size TEXT,
            website TEXT,
            address TEXT,
            description TEXT,
            hubspot_id TEXT UNIQUE,
            harvest_id INTEGER UNIQUE,
            source TEXT,
            radar_state TEXT,
            priority TEXT,
            current_spend REAL,
            potential_spend REAL,
            last_interaction_date TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            created_by TEXT,
            updated_by TEXT,
            deleted_at TEXT,
            notes TEXT,
            contacts INTEGER
          )
        `).run();

        // Contact table
        db.prepare(`
          CREATE TABLE contact (
            id TEXT PRIMARY KEY,
            first_name TEXT NOT NULL,
            last_name TEXT,
            email TEXT,
            phone TEXT,
            job_title TEXT,
            hubspot_id TEXT UNIQUE,
            harvest_user_id TEXT UNIQUE,
            source TEXT,
            deleted_at TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            created_by TEXT,
            updated_by TEXT,
            notes TEXT
          )
        `).run();

        // Deal table
        db.prepare(`
          CREATE TABLE deal (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            stage TEXT NOT NULL,
            status TEXT,
            value REAL,
            currency TEXT DEFAULT 'AUD',
            probability REAL,
            expected_close_date TEXT,
            start_date TEXT,
            end_date TEXT,
            invoice_frequency TEXT,
            payment_terms INTEGER,
            company_id TEXT NOT NULL,
            hubspot_id TEXT UNIQUE,
            harvest_project_id TEXT,
            description TEXT,
            source TEXT,
            priority TEXT,
            owner TEXT,
            custom_fields TEXT,
            include_in_projections INTEGER DEFAULT 1,
            deleted_at TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            created_by TEXT NOT NULL,
            updated_by TEXT NOT NULL,
            FOREIGN KEY (company_id) REFERENCES company(id)
          )
        `).run();

        // Estimate table
        db.prepare(`
          CREATE TABLE estimate (
            id TEXT PRIMARY KEY,
            company_id TEXT NOT NULL,
            client_name TEXT NOT NULL,
            project_name TEXT,
            start_date TEXT NOT NULL,
            end_date TEXT NOT NULL,
            discount_type TEXT DEFAULT 'none',
            discount_value REAL DEFAULT 0,
            invoice_frequency TEXT,
            payment_terms INTEGER,
            harvest_estimate_id INTEGER,
            status TEXT DEFAULT 'draft',
            source TEXT,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            created_by TEXT NOT NULL,
            updated_by TEXT NOT NULL,
            FOREIGN KEY (company_id) REFERENCES company(id)
          )
        `).run();

        // Note table (often missing)
        db.prepare(`
          CREATE TABLE note (
            id TEXT PRIMARY KEY,
            deal_id TEXT,
            content TEXT NOT NULL,
            created_at TEXT NOT NULL,
            created_by TEXT,
            FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
          )
        `).run();

        // Expense table
        db.prepare(`
          CREATE TABLE expense (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            source TEXT NOT NULL,
            type TEXT NOT NULL,
            category TEXT,
            amount REAL NOT NULL,
            currency TEXT DEFAULT 'AUD',
            tax_inclusive INTEGER DEFAULT 1,
            tax_amount REAL,
            date TEXT NOT NULL,
            due_date TEXT,
            frequency TEXT,
            repeat_count INTEGER,
            end_date TEXT,
            description TEXT,
            reference TEXT,
            status TEXT,
            external_id TEXT,
            external_url TEXT,
            metadata TEXT,
            editable INTEGER DEFAULT 1,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            created_by TEXT,
            updated_by TEXT,
            last_synced_at TEXT
          )
        `).run();

        // Add schema version
        const now = new Date().toISOString();
        db.prepare(`
          INSERT INTO schema_version (version, applied_at, description)
          VALUES (1, ?, 'Emergency production initialization')
        `).run(now);
      })();

      console.log('Minimal schema created successfully');
    }
  } else {
    // Check for missing tables
    console.log('Database exists. Checking for missing tables...');

    const requiredTables = [
      'company', 'contact', 'deal', 'estimate', 'note',
      'expense', 'deal_estimate', 'contact_company', 'contact_role',
      'harvest_invoice_cache'
    ];

    const missingTables = [];

    for (const tableName of requiredTables) {
      const exists = db.prepare(`
        SELECT 1 FROM sqlite_master
        WHERE type='table' AND name=?
      `).get(tableName);

      if (!exists) {
        missingTables.push(tableName);
      }
    }

    if (missingTables.length > 0) {
      console.log(`Missing tables: ${missingTables.join(', ')}`);

      // Create missing tables
      if (missingTables.includes('note')) {
        console.log('Creating note table...');
        db.prepare(`
          CREATE TABLE note (
            id TEXT PRIMARY KEY,
            deal_id TEXT,
            content TEXT NOT NULL,
            created_at TEXT NOT NULL,
            created_by TEXT,
            FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
          )
        `).run();

        db.prepare(`
          CREATE INDEX idx_note_deal_id ON note(deal_id)
        `).run();
      }

      if (missingTables.includes('harvest_invoice_cache')) {
        console.log('Creating harvest_invoice_cache table...');
        db.prepare(`
          CREATE TABLE harvest_invoice_cache (
            harvest_client_id INTEGER PRIMARY KEY,
            total_invoiced REAL NOT NULL DEFAULT 0,
            invoice_count INTEGER NOT NULL DEFAULT 0,
            last_updated TEXT NOT NULL,
            created_at TEXT NOT NULL DEFAULT (datetime('now'))
          )
        `).run();

        db.prepare(`
          CREATE INDEX idx_harvest_invoice_cache_updated ON harvest_invoice_cache(last_updated)
        `).run();
      }

      console.log('Missing tables created');
    } else {
      console.log('All required tables exist');
    }
  }

  console.log('Database fix completed successfully');
} catch (error) {
  console.error('Error fixing database:', error);
  process.exit(1);
} finally {
  db.close();
}