const fs = require('fs');
const path = require('path');

// Copy public directory to dist
console.log('Copying public assets to dist...');

const publicDir = path.join(__dirname, '..', 'public');
const distDir = path.join(__dirname, '..', 'dist');

// Check if public directory exists
if (!fs.existsSync(publicDir)) {
  console.log('Public directory does not exist, skipping copy');
  process.exit(0);
}

// Create dist directory if it doesn't exist
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Recursive function to copy directory
const copyDir = (src, dest) => {
  // Create destination directory if it doesn't exist
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  // Read source directory
  const entries = fs.readdirSync(src);
  
  // Copy each entry
  for (const entry of entries) {
    const srcPath = path.join(src, entry);
    const destPath = path.join(dest, entry);
    
    // Get stats for the entry
    const stats = fs.statSync(srcPath);
    
    // If it's a directory, recursively copy it
    if (stats.isDirectory()) {
      copyDir(srcPath, destPath);
    } 
    // If it's a file, copy it
    else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
};

// Copy public directory to dist
copyDir(publicDir, distDir);

console.log('Public assets copied to dist');