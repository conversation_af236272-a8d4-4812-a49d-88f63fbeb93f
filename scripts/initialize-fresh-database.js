/**
 * Fresh Database Initialization Script
 *
 * This script initializes a completely fresh database with the unified data model.
 * It drops all existing tables and creates new ones according to the plan.
 *
 * IMPORTANT: This will delete all existing data. Only use when you want to start fresh.
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Get database path from environment or use default
const dbPath = process.env.DB_PATH || path.join(__dirname, '..', 'data', 'upstream.db');

// Create data directory if it doesn't exist
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  console.log(`Creating data directory: ${dataDir}`);
  fs.mkdirSync(dataDir, { recursive: true });
}

console.log(`Initializing fresh database at: ${dbPath}`);

// Initialize the database
const db = new BetterSqlite3(dbPath);

// Function to drop all existing tables
function dropAllTables() {
  console.log('Dropping all existing tables...');

  try {
    // Get all table names
    const tables = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `).all();

    // Disable foreign key constraints temporarily
    db.pragma('foreign_keys = OFF');

    // Drop each table
    for (const table of tables) {
      try {
        console.log(`Dropping table: ${table.name}`);
        db.prepare(`DROP TABLE IF EXISTS "${table.name}"`).run();
      } catch (error) {
        console.error(`Error dropping table ${table.name}:`, error.message);
        // Continue with other tables
      }
    }

    // Re-enable foreign key constraints
    db.pragma('foreign_keys = ON');

    console.log('Tables dropped successfully');
  } catch (error) {
    console.error('Error getting table list:', error.message);
    console.log('Continuing with fresh database creation...');
  }
}

// Function to create schema version table
function createSchemaVersionTable() {
  console.log('Creating schema_version table...');

  db.prepare(`
    CREATE TABLE schema_version (
      version INTEGER PRIMARY KEY,
      applied_at TEXT NOT NULL,
      description TEXT NOT NULL
    )
  `).run();

  // Insert initial version
  const now = new Date().toISOString();
  db.prepare(`
    INSERT INTO schema_version (version, applied_at, description)
    VALUES (1, ?, 'Initial unified data model')
  `).run(now);

  console.log('Schema version table created');
}

// Function to create core entity tables
function createCoreEntityTables() {
  console.log('Creating core entity tables...');

  // Create company table
  db.prepare(`
    CREATE TABLE company (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      industry TEXT,
      size TEXT,
      website TEXT,
      address TEXT,
      description TEXT,

      /* External System IDs */
      hubspot_id TEXT UNIQUE,
      harvest_id INTEGER UNIQUE,

      /* Source Information */
      source TEXT, -- 'HubSpot', 'Harvest', 'Manual'

      /* Radar-specific Fields */
      radar_state TEXT, -- 'Strategy', 'Transformation', 'BAU', 'Transition out'
      priority TEXT, -- 'High', 'Medium', 'Low', 'Qualified out'
      current_spend REAL,
      potential_spend REAL,
      last_interaction_date TEXT,

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,
      updated_by TEXT,
      deleted_at TEXT,

      /* Additional Fields */
      notes TEXT, -- Company-specific notes
      contacts INTEGER -- Number of contacts (for Radar view)
    )
  `).run();

  // Create indexes for company table
  db.prepare(`CREATE INDEX idx_company_name ON company(name)`).run();
  db.prepare(`CREATE INDEX idx_company_hubspot_id ON company(hubspot_id)`).run();
  db.prepare(`CREATE INDEX idx_company_harvest_id ON company(harvest_id)`).run();
  db.prepare(`CREATE INDEX idx_company_source ON company(source)`).run();
  db.prepare(`CREATE INDEX idx_company_radar_state ON company(radar_state)`).run();
  db.prepare(`CREATE INDEX idx_company_priority ON company(priority)`).run();
  db.prepare(`CREATE INDEX idx_company_deleted_at ON company(deleted_at)`).run();

  // Create contact table
  db.prepare(`
    CREATE TABLE contact (
      id TEXT PRIMARY KEY,
      first_name TEXT NOT NULL,
      last_name TEXT,
      email TEXT,
      phone TEXT,
      job_title TEXT,

      /* External System IDs */
      hubspot_id TEXT UNIQUE,
      harvest_user_id TEXT UNIQUE,

      /* Source and Status Information */
      source TEXT, -- 'HubSpot', 'Harvest', 'Manual'
      deleted_at TEXT, -- Soft delete implementation

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,
      updated_by TEXT,
      notes TEXT
    )
  `).run();

  // Create indexes for contact table
  db.prepare(`CREATE INDEX idx_contact_name ON contact(first_name, last_name)`).run();
  db.prepare(`CREATE INDEX idx_contact_email ON contact(email)`).run();
  db.prepare(`CREATE INDEX idx_contact_hubspot_id ON contact(hubspot_id)`).run();
  db.prepare(`CREATE INDEX idx_contact_harvest_user_id ON contact(harvest_user_id)`).run();
  db.prepare(`CREATE INDEX idx_contact_source ON contact(source)`).run();
  db.prepare(`CREATE INDEX idx_contact_deleted_at ON contact(deleted_at)`).run();

  // Create deal table
  db.prepare(`
    CREATE TABLE deal (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      stage TEXT NOT NULL,
      status TEXT, -- Separate from stage for clearer state management

      /* Financial Information */
      value REAL,
      currency TEXT DEFAULT 'AUD',
      probability REAL,

      /* Timing Information */
      expected_close_date TEXT,
      start_date TEXT,
      end_date TEXT,

      /* Invoicing Information */
      invoice_frequency TEXT,
      payment_terms INTEGER,

      /* Relationships */
      company_id TEXT NOT NULL,

      /* External System IDs */
      hubspot_id TEXT UNIQUE,
      harvest_project_id TEXT,

      /* Additional Information */
      description TEXT,
      source TEXT, -- 'HubSpot', 'Manual'
      priority TEXT,
      owner TEXT,
      custom_fields TEXT, -- JSON string for custom fields
      include_in_projections INTEGER DEFAULT 1,
      deleted_at TEXT, -- Soft delete implementation

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT NOT NULL,
      updated_by TEXT NOT NULL,

      FOREIGN KEY (company_id) REFERENCES company(id)
    )
  `).run();

  // Create indexes for deal table
  db.prepare(`CREATE INDEX idx_deal_stage ON deal(stage)`).run();
  db.prepare(`CREATE INDEX idx_deal_status ON deal(status)`).run();
  db.prepare(`CREATE INDEX idx_deal_expected_close_date ON deal(expected_close_date)`).run();
  db.prepare(`CREATE INDEX idx_deal_company_id ON deal(company_id)`).run();
  db.prepare(`CREATE INDEX idx_deal_hubspot_id ON deal(hubspot_id)`).run();
  db.prepare(`CREATE INDEX idx_deal_harvest_project_id ON deal(harvest_project_id)`).run();
  db.prepare(`CREATE INDEX idx_deal_source ON deal(source)`).run();
  db.prepare(`CREATE INDEX idx_deal_deleted_at ON deal(deleted_at)`).run();
  db.prepare(`CREATE INDEX idx_deal_include_in_projections ON deal(include_in_projections)`).run();

  // Create estimate table
  db.prepare(`
    CREATE TABLE estimate (
      id TEXT PRIMARY KEY,
      company_id TEXT NOT NULL,
      client_name TEXT NOT NULL,
      project_name TEXT,

      /* Timing Information */
      start_date TEXT NOT NULL,
      end_date TEXT NOT NULL,

      /* Financial Information */
      discount_type TEXT DEFAULT 'none',
      discount_value REAL DEFAULT 0,
      invoice_frequency TEXT,
      payment_terms INTEGER,

      /* External System IDs */
      harvest_estimate_id INTEGER,

      /* Status and Source Information */
      status TEXT DEFAULT 'draft',
      source TEXT, -- 'Harvest', 'Manual'

      /* Additional Information */
      notes TEXT,

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT NOT NULL,
      updated_by TEXT NOT NULL,

      FOREIGN KEY (company_id) REFERENCES company(id)
    )
  `).run();

  // Create indexes for estimate table
  db.prepare(`CREATE INDEX idx_estimate_company_id ON estimate(company_id)`).run();
  db.prepare(`CREATE INDEX idx_estimate_dates ON estimate(start_date, end_date)`).run();
  db.prepare(`CREATE INDEX idx_estimate_status ON estimate(status)`).run();
  db.prepare(`CREATE INDEX idx_estimate_harvest_id ON estimate(harvest_estimate_id)`).run();
  db.prepare(`CREATE INDEX idx_estimate_source ON estimate(source)`).run();

  // Create estimate_allocation table
  db.prepare(`
    CREATE TABLE estimate_allocation (
      id TEXT PRIMARY KEY,
      estimate_id TEXT NOT NULL,
      harvest_user_id INTEGER NOT NULL,
      first_name TEXT NOT NULL,
      last_name TEXT,
      project_role TEXT,
      level TEXT,
      target_rate_daily REAL NOT NULL,
      cost_rate_daily REAL NOT NULL,
      proposed_rate_daily REAL NOT NULL,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for estimate_allocation table
  db.prepare(`CREATE INDEX idx_estimate_allocation_estimate_id ON estimate_allocation(estimate_id)`).run();
  db.prepare(`CREATE INDEX idx_estimate_allocation_harvest_user_id ON estimate_allocation(harvest_user_id)`).run();

  // Create estimate_time_allocation table
  db.prepare(`
    CREATE TABLE estimate_time_allocation (
      id TEXT PRIMARY KEY,
      allocation_id TEXT NOT NULL,
      week_identifier TEXT NOT NULL, -- ISO week identifier
      days REAL NOT NULL,
      FOREIGN KEY (allocation_id) REFERENCES estimate_allocation(id) ON DELETE CASCADE,
      UNIQUE(allocation_id, week_identifier)
    )
  `).run();

  // Create indexes for estimate_time_allocation table
  db.prepare(`CREATE INDEX idx_estimate_time_allocation_allocation_id ON estimate_time_allocation(allocation_id)`).run();
  db.prepare(`CREATE INDEX idx_estimate_time_allocation_week ON estimate_time_allocation(week_identifier)`).run();

  console.log('Core entity tables created successfully');
}

// Function to create relationship tables
function createRelationshipTables() {
  console.log('Creating relationship tables...');

  // Create company_relationship table
  db.prepare(`
    CREATE TABLE company_relationship (
      parent_company_id TEXT NOT NULL,
      child_company_id TEXT NOT NULL,
      relationship_type TEXT NOT NULL, -- 'parent', 'subsidiary', 'partner'

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,

      PRIMARY KEY (parent_company_id, child_company_id),
      FOREIGN KEY (parent_company_id) REFERENCES company(id) ON DELETE CASCADE,
      FOREIGN KEY (child_company_id) REFERENCES company(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for company_relationship
  db.prepare(`CREATE INDEX idx_company_relationship_parent ON company_relationship(parent_company_id)`).run();
  db.prepare(`CREATE INDEX idx_company_relationship_child ON company_relationship(child_company_id)`).run();
  db.prepare(`CREATE INDEX idx_company_relationship_type ON company_relationship(relationship_type)`).run();

  // Create contact_company junction table
  db.prepare(`
    CREATE TABLE contact_company (
      contact_id TEXT NOT NULL,
      company_id TEXT NOT NULL,
      role TEXT, -- 'employee', 'contractor', etc.
      is_primary INTEGER DEFAULT 0, -- Boolean: 1 = primary company for this contact

      /* Audit Information */
      created_at TEXT NOT NULL,
      created_by TEXT,

      PRIMARY KEY (contact_id, company_id),
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for contact_company
  db.prepare(`CREATE INDEX idx_contact_company_contact ON contact_company(contact_id)`).run();
  db.prepare(`CREATE INDEX idx_contact_company_company ON contact_company(company_id)`).run();
  db.prepare(`CREATE INDEX idx_contact_company_role ON contact_company(role)`).run();
  db.prepare(`CREATE INDEX idx_contact_company_is_primary ON contact_company(is_primary)`).run();

  // Create contact_role table
  db.prepare(`
    CREATE TABLE contact_role (
      contact_id TEXT NOT NULL,
      deal_id TEXT NOT NULL,
      role TEXT, -- 'decision_maker', 'influencer', 'technical_contact', etc.

      /* Audit Information */
      created_at TEXT NOT NULL,
      created_by TEXT,

      PRIMARY KEY (contact_id, deal_id),
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for contact_role
  db.prepare(`CREATE INDEX idx_contact_role_contact ON contact_role(contact_id)`).run();
  db.prepare(`CREATE INDEX idx_contact_role_deal ON contact_role(deal_id)`).run();
  db.prepare(`CREATE INDEX idx_contact_role_role ON contact_role(role)`).run();

  // Create a view for backward compatibility with deal_contact
  console.log('Creating deal_contact view for backward compatibility...');
  try {
    // Drop the view if it exists
    db.prepare(`DROP VIEW IF EXISTS deal_contact`).run();

    // Create the view
    db.prepare(`
      CREATE VIEW deal_contact AS
      SELECT contact_id, deal_id, role, created_at, created_by
      FROM contact_role
    `).run();
  } catch (error) {
    console.warn('Warning: Could not create deal_contact view:', error.message);
  }

  // Create deal_estimate table
  db.prepare(`
    CREATE TABLE deal_estimate (
      deal_id TEXT NOT NULL,
      estimate_id TEXT NOT NULL,
      estimate_type TEXT NOT NULL, -- 'draft', 'harvest'

      /* Audit Information */
      linked_at TEXT NOT NULL,
      linked_by TEXT,

      PRIMARY KEY (deal_id, estimate_id, estimate_type),
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
      FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for deal_estimate
  db.prepare(`CREATE INDEX idx_deal_estimate_deal ON deal_estimate(deal_id)`).run();
  db.prepare(`CREATE INDEX idx_deal_estimate_estimate ON deal_estimate(estimate_id)`).run();

  console.log('Relationship tables created successfully');
}

// Function to create activity tracking tables
function createActivityTables() {
  console.log('Creating activity tracking tables...');

  // Create activity table (legacy)
  db.prepare(`
    CREATE TABLE activity (
      id TEXT PRIMARY KEY,
      type TEXT NOT NULL, -- 'call', 'email', 'meeting', 'note', 'task'
      subject TEXT NOT NULL,
      description TEXT,
      status TEXT, -- 'planned', 'completed', 'canceled'

      /* Timing Information */
      due_date TEXT,
      completed_date TEXT,

      /* Relationships */
      company_id TEXT,
      contact_id TEXT,
      deal_id TEXT,

      /* Audit Information */
      created_by TEXT NOT NULL,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,

      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for activity
  db.prepare(`CREATE INDEX idx_activity_type ON activity(type)`).run();
  db.prepare(`CREATE INDEX idx_activity_status ON activity(status)`).run();
  db.prepare(`CREATE INDEX idx_activity_due_date ON activity(due_date)`).run();
  db.prepare(`CREATE INDEX idx_activity_company ON activity(company_id)`).run();
  db.prepare(`CREATE INDEX idx_activity_contact ON activity(contact_id)`).run();
  db.prepare(`CREATE INDEX idx_activity_deal ON activity(deal_id)`).run();

  console.log('Activity tracking tables created successfully');
}

// Function to create audit tables (for activity feed system)
function createAuditTables() {
  console.log('Creating audit domain tables...');

  // Create field_ownership table
  db.prepare(`
    CREATE TABLE field_ownership (
      id TEXT PRIMARY KEY,
      entity_type TEXT NOT NULL, -- 'deal', 'estimate', etc.
      entity_id TEXT NOT NULL,
      field_name TEXT NOT NULL,
      owner TEXT NOT NULL, -- 'HubSpot', 'Harvest', 'Manual', 'System', 'Estimate'
      set_at TEXT NOT NULL,
      set_by TEXT,
      UNIQUE(entity_type, entity_id, field_name)
    )
  `).run();

  // Create indexes for field_ownership table
  db.prepare(`CREATE INDEX idx_field_ownership_entity ON field_ownership(entity_type, entity_id)`).run();
  db.prepare(`CREATE INDEX idx_field_ownership_owner ON field_ownership(owner)`).run();

  // Create change_log table
  db.prepare(`
    CREATE TABLE change_log (
      id TEXT PRIMARY KEY,
      entity_type TEXT NOT NULL, -- 'deal', 'estimate', etc.
      entity_id TEXT NOT NULL,
      field_name TEXT NOT NULL,
      old_value TEXT,
      new_value TEXT,
      change_source TEXT NOT NULL,
      changed_at TEXT NOT NULL,
      changed_by TEXT
    )
  `).run();

  // Create indexes for change_log table
  db.prepare(`CREATE INDEX idx_change_log_entity ON change_log(entity_type, entity_id)`).run();
  db.prepare(`CREATE INDEX idx_change_log_field ON change_log(field_name)`).run();
  db.prepare(`CREATE INDEX idx_change_log_date ON change_log(changed_at)`).run();

  // Create activity_feed table
  db.prepare(`
    CREATE TABLE activity_feed (
      id TEXT PRIMARY KEY,
      type TEXT NOT NULL, -- Activity type (see types below)
      subject TEXT NOT NULL, -- Brief description
      description TEXT, -- Detailed description
      status TEXT, -- 'planned', 'completed', 'canceled', 'in_progress'

      /* Entity Relationships */
      entity_type TEXT, -- 'deal', 'company', 'estimate', 'expense', 'contact'
      entity_id TEXT, -- ID of the related entity

      /* Timing Information */
      due_date TEXT,
      completed_date TEXT,

      /* Legacy Relationships (for backward compatibility) */
      company_id TEXT,
      contact_id TEXT,
      deal_id TEXT,

      /* Metadata */
      metadata TEXT, -- JSON for additional context and structured data
      is_read INTEGER DEFAULT 0, -- Track read status per activity
      importance TEXT DEFAULT 'normal', -- 'low', 'normal', 'high'

      /* User and System Information */
      created_by TEXT NOT NULL, -- User ID or 'system'
      source TEXT, -- 'user', 'hubspot', 'xero', 'harvest', 'system'

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,

      /* Foreign Key Constraints */
      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for activity_feed table
  db.prepare(`CREATE INDEX idx_activity_feed_created_at ON activity_feed(created_at DESC)`).run();
  db.prepare(`CREATE INDEX idx_activity_feed_type ON activity_feed(type)`).run();
  db.prepare(`CREATE INDEX idx_activity_feed_entity ON activity_feed(entity_type, entity_id)`).run();
  db.prepare(`CREATE INDEX idx_activity_feed_source ON activity_feed(source)`).run();
  db.prepare(`CREATE INDEX idx_activity_feed_created_by ON activity_feed(created_by)`).run();
  db.prepare(`CREATE INDEX idx_activity_feed_unread ON activity_feed(is_read)`).run();

  console.log('Audit domain tables created successfully');
}

// Function to create pipeline management tables
function createPipelineTables() {
  console.log('Creating pipeline management tables...');

  // Create pipeline table
  db.prepare(`
    CREATE TABLE pipeline (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      is_default INTEGER DEFAULT 0,

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    )
  `).run();

  // Create pipeline_stage table
  db.prepare(`
    CREATE TABLE pipeline_stage (
      id TEXT PRIMARY KEY,
      pipeline_id TEXT NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      position INTEGER NOT NULL,
      default_probability REAL,

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,

      FOREIGN KEY (pipeline_id) REFERENCES pipeline(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for pipeline_stage
  db.prepare(`CREATE INDEX idx_pipeline_stage_pipeline ON pipeline_stage(pipeline_id)`).run();
  db.prepare(`CREATE INDEX idx_pipeline_stage_position ON pipeline_stage(position)`).run();

  // Create deal_stage_history table
  db.prepare(`
    CREATE TABLE deal_stage_history (
      id TEXT PRIMARY KEY,
      deal_id TEXT NOT NULL,
      pipeline_stage_id TEXT NOT NULL,

      /* Timing Information */
      entered_at TEXT NOT NULL,
      exited_at TEXT,
      duration_days REAL,

      /* Audit Information */
      created_by TEXT NOT NULL,

      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
      FOREIGN KEY (pipeline_stage_id) REFERENCES pipeline_stage(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for deal_stage_history
  db.prepare(`CREATE INDEX idx_deal_stage_history_deal ON deal_stage_history(deal_id)`).run();
  db.prepare(`CREATE INDEX idx_deal_stage_history_stage ON deal_stage_history(pipeline_stage_id)`).run();
  db.prepare(`CREATE INDEX idx_deal_stage_history_entered ON deal_stage_history(entered_at)`).run();

  console.log('Pipeline management tables created successfully');
}

// Function to create tagging system tables
function createTaggingTables() {
  console.log('Creating tagging system tables...');

  // Create tag table
  db.prepare(`
    CREATE TABLE tag (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL UNIQUE,
      color TEXT,
      created_at TEXT NOT NULL
    )
  `).run();

  // Create taggable table
  db.prepare(`
    CREATE TABLE taggable (
      tag_id TEXT NOT NULL,
      entity_type TEXT NOT NULL, -- 'company', 'contact', 'deal'
      entity_id TEXT NOT NULL,

      PRIMARY KEY (tag_id, entity_type, entity_id),
      FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for taggable
  db.prepare(`CREATE INDEX idx_taggable_tag ON taggable(tag_id)`).run();
  db.prepare(`CREATE INDEX idx_taggable_entity ON taggable(entity_type, entity_id)`).run();

  console.log('Tagging system tables created successfully');
}

// Function to create integration tables
function createIntegrationTables() {
  console.log('Creating integration tables...');

  // Create hubspot_import table
  db.prepare(`
    CREATE TABLE hubspot_import (
      id TEXT PRIMARY KEY,
      import_date TEXT NOT NULL,
      status TEXT NOT NULL, -- 'pending', 'completed', 'failed'
      deals_count INTEGER,
      contacts_count INTEGER,
      companies_count INTEGER,
      error_message TEXT,
      created_at TEXT NOT NULL
    )
  `).run();

  // Create indexes for hubspot_import
  db.prepare(`CREATE INDEX idx_hubspot_import_date ON hubspot_import(import_date)`).run();
  db.prepare(`CREATE INDEX idx_hubspot_import_status ON hubspot_import(status)`).run();

  // Create settings table
  db.prepare(`
    CREATE TABLE settings (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      updated_by TEXT
    )
  `).run();

  // Create harvest_invoice_cache table for performance
  db.prepare(`
    CREATE TABLE harvest_invoice_cache (
      harvest_client_id INTEGER PRIMARY KEY,
      total_invoiced REAL NOT NULL DEFAULT 0,
      invoice_count INTEGER NOT NULL DEFAULT 0,
      last_updated TEXT NOT NULL,
      created_at TEXT NOT NULL DEFAULT (datetime('now'))
    )
  `).run();

  // Create indexes for harvest_invoice_cache table
  db.prepare(`CREATE INDEX idx_harvest_invoice_cache_updated ON harvest_invoice_cache(last_updated)`).run();

  console.log('Integration tables created successfully');
}

// Function to create financial tables
function createFinancialTables() {
  console.log('Creating financial tables...');

  // Create expense table
  db.prepare(`
    CREATE TABLE expense (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,

      /* Source and Type Information */
      source TEXT NOT NULL, -- 'manual', 'xero_bill', 'xero_payroll', 'xero_tax', etc.
      type TEXT NOT NULL, -- 'Monthly Payroll', 'Superannuation', 'Insurances', 'Tax', etc.
      category TEXT, -- Additional categorization

      /* Financial Information */
      amount REAL NOT NULL,
      currency TEXT DEFAULT 'AUD',
      tax_inclusive INTEGER DEFAULT 1, -- Boolean: 1 = tax inclusive, 0 = tax exclusive
      tax_amount REAL, -- For Xero expenses that include tax details

      /* Timing Information */
      date TEXT NOT NULL, -- ISO date string for the expense date
      due_date TEXT, -- For bills and other expenses with due dates
      frequency TEXT, -- 'one-off', 'weekly', 'fortnightly', 'monthly', 'quarterly', 'yearly'
      repeat_count INTEGER, -- Number of times to repeat (for recurring expenses)
      end_date TEXT, -- Optional end date for recurring expenses

      /* Additional Information */
      description TEXT,
      reference TEXT, -- Reference number, invoice number, etc.
      status TEXT, -- 'draft', 'approved', 'paid', etc. (primarily for Xero-synced expenses)

      /* External System Information */
      external_id TEXT, -- ID in external system (e.g., Xero bill ID)
      external_url TEXT, -- URL to view in external system
      metadata TEXT, -- JSON string for additional data from external systems

      /* Audit Information */
      editable INTEGER DEFAULT 1, -- Boolean: 1 = can be edited, 0 = read-only (for synced expenses)
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,
      updated_by TEXT,
      last_synced_at TEXT -- For expenses synced from external systems
    )
  `).run();

  // Create indexes for expense
  db.prepare(`CREATE INDEX idx_expense_date ON expense(date)`).run();
  db.prepare(`CREATE INDEX idx_expense_due_date ON expense(due_date)`).run();
  db.prepare(`CREATE INDEX idx_expense_type ON expense(type)`).run();
  db.prepare(`CREATE INDEX idx_expense_source ON expense(source)`).run();
  db.prepare(`CREATE INDEX idx_expense_status ON expense(status)`).run();
  db.prepare(`CREATE INDEX idx_expense_external_id ON expense(external_id)`).run();

  // Create cashflow_snapshot table
  db.prepare(`
    CREATE TABLE cashflow_snapshot (
      id TEXT PRIMARY KEY,
      date TEXT NOT NULL, -- ISO date string
      tenant_id TEXT NOT NULL, -- Xero tenant ID
      days_ahead INTEGER NOT NULL DEFAULT 90,
      snapshot_data TEXT NOT NULL, -- JSON string of snapshot data
      created_at TEXT NOT NULL,
      created_by TEXT,
      UNIQUE(date, tenant_id, days_ahead)
    )
  `).run();

  // Create indexes for cashflow_snapshot
  db.prepare(`CREATE INDEX idx_cashflow_snapshot_date ON cashflow_snapshot(date)`).run();
  db.prepare(`CREATE INDEX idx_cashflow_snapshot_tenant ON cashflow_snapshot(tenant_id)`).run();

  console.log('Financial tables created successfully');
}

// Main function to initialize the database
function initializeDatabase() {
  try {
    // Drop all existing tables first (outside of transaction in case it fails)
    dropAllTables();

    // Start a transaction for creating tables
    db.transaction(() => {
      // Create schema version table
      createSchemaVersionTable();

      // Create all domain tables
      createCoreEntityTables();
      createRelationshipTables();
      createActivityTables();
      createAuditTables(); // Add audit tables for activity feed
      createPipelineTables();
      createTaggingTables();
      createIntegrationTables();
      createFinancialTables();
    })();

    console.log('Database initialization completed successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    db.close();
  }
}

// Run the initialization
initializeDatabase();
