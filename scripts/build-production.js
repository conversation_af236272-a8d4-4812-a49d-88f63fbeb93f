/**
 * Production build script for Onbord Financial Dashboard
 * Handles the complete build process with proper error handling
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Build configuration
const config = {
  distDir: 'dist',
  requiredScripts: [
    'ensure-database.js',
    'initialize-fresh-database.js',
    'fix-production-database.js',
    'fix-server-path.js',
    'add-company-notes-contacts.js',
    'add-estimate-allocation-tables.js',
    'add-activity-feed-migration.js'
  ],
  requiredDirectories: ['api', 'frontend', 'scripts']
};

// Utility functions
function log(message, level = 'info') {
  const prefix = {
    info: '📌',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  };
  console.log(`${prefix[level]} ${message}`);
}

function execCommand(command, description) {
  try {
    log(description);
    execSync(command, { stdio: 'inherit' });
    return true;
  } catch (error) {
    log(`Failed: ${description}`, 'error');
    console.error(error.message);
    return false;
  }
}

function ensureDirectory(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function copyFile(src, dest) {
  if (fs.existsSync(src)) {
    ensureDirectory(path.dirname(dest));
    fs.copyFileSync(src, dest);
    return true;
  }
  return false;
}

// Main build process
async function build() {
  console.log('🚀 Starting production build...\n');

  try {
    // 1. Clean previous build
    log('Cleaning previous build...');
    if (fs.existsSync(config.distDir)) {
      fs.rmSync(config.distDir, { recursive: true, force: true });
    }
    ensureDirectory(config.distDir);

    // 2. Build backend
    if (!execCommand('npx tsc --project tsconfig.backend.json', 'Building backend (TypeScript)...')) {
      // Try to fix common TypeScript issues
      log('Attempting to fix TypeScript errors...', 'warning');
      execCommand('npm install --save-dev @types/node @types/express @types/cookie-parser @types/socket.io', 'Installing missing types...');

      if (!execCommand('npx tsc --project tsconfig.backend.json', 'Retrying backend build...')) {
        throw new Error('Backend build failed');
      }
    }

    // 3. Build frontend
    if (!execCommand('npm run build:frontend', 'Building frontend (Vite)...')) {
      throw new Error('Frontend build failed');
    }

    // 4. Copy public assets
    if (!execCommand('npm run copy:public', 'Copying public assets...')) {
      throw new Error('Failed to copy public assets');
    }

    // 5. Create type definitions
    if (!execCommand('npm run create:types', 'Creating type definitions...')) {
      log('Type creation failed (non-critical)', 'warning');
    }

    // 6. Copy essential scripts
    log('Copying essential scripts...');
    const scriptsDir = path.join(config.distDir, 'scripts');
    ensureDirectory(scriptsDir);

    let scriptsCopied = 0;
    for (const script of config.requiredScripts) {
      const src = path.join('scripts', script);
      const dest = path.join(scriptsDir, script);
      if (copyFile(src, dest)) {
        log(`  ✓ ${script}`, 'success');
        scriptsCopied++;
      } else {
        log(`  ✗ ${script} not found`, 'warning');
      }
    }

    if (scriptsCopied === 0) {
      throw new Error('No essential scripts were copied');
    }

    // 7. Create server.js redirect
    log('Creating server.js redirect...');
    execCommand('node scripts/fix-server-path.js', 'Running fix-server-path.js...');

    // 8. Verify build output
    log('Verifying build output...');
    let missingDirs = [];
    for (const dir of config.requiredDirectories) {
      const fullPath = path.join(config.distDir, dir);
      if (!fs.existsSync(fullPath)) {
        missingDirs.push(dir);
      }
    }

    if (missingDirs.length > 0) {
      throw new Error(`Missing required directories: ${missingDirs.join(', ')}`);
    }

    // 9. Create build info
    const buildInfo = {
      buildDate: new Date().toISOString(),
      nodeVersion: process.version,
      npmVersion: execSync('npm --version').toString().trim(),
      gitCommit: execSync('git rev-parse HEAD').toString().trim(),
      gitBranch: execSync('git branch --show-current').toString().trim()
    };

    fs.writeFileSync(
      path.join(config.distDir, 'BUILD_INFO.json'),
      JSON.stringify(buildInfo, null, 2)
    );

    // Success!
    console.log('\n' + '='.repeat(50));
    log('Build completed successfully!', 'success');
    console.log('='.repeat(50));

    // Build summary
    console.log('\n📊 Build Summary:');
    console.log(`  - Output directory: ${config.distDir}/`);
    console.log(`  - Backend compiled: ✓`);
    console.log(`  - Frontend built: ✓`);
    console.log(`  - Scripts copied: ${scriptsCopied}/${config.requiredScripts.length}`);
    console.log(`  - Build date: ${new Date().toLocaleString()}`);

  } catch (error) {
    console.error('\n' + '='.repeat(50));
    log('Build failed!', 'error');
    console.error('='.repeat(50));
    console.error('\nError:', error.message);
    process.exit(1);
  }
}

// Run the build
build();