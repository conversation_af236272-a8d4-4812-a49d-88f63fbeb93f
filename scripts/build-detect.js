/**
 * Simple script to detect if we're running in a pre-built environment
 * Used by the start:pre-built script
 */

const fs = require('fs');
const path = require('path');

try {
  // Check if we're running in a pre-built environment by looking for environment markers
  const previewMarkerPath = path.join(__dirname, '..', 'env-preview');
  const productionMarkerPath = path.join(__dirname, '..', 'env-production');
  
  if (fs.existsSync(previewMarkerPath)) {
    console.log('Running in pre-built preview environment');
    process.exit(0);
  } else if (fs.existsSync(productionMarkerPath)) {
    console.log('Running in pre-built production environment');
    process.exit(0);
  } else {
    console.log('Not running in a pre-built environment');
    process.exit(1); // Exit with error so the || operator in package.json will trigger
  }
} catch (error) {
  console.error('Error detecting environment:', error.message);
  process.exit(1);
}