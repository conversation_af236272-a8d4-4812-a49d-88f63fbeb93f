const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

console.log('Building frontend...');

// Helper function to check if a file exists
const fileExists = (filePath) => {
  try {
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
};

// Helper function to execute command with better error handling
const runCommand = (command, options = {}) => {
  try {
    console.log(`Running command: ${command}`);
    execSync(command, {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..'),
      env: { ...process.env, NODE_ENV: 'production' },
      ...options
    });
    return true;
  } catch (error) {
    console.error(`Command failed: ${command}`);
    console.error(`Error message: ${error.message}`);
    return false;
  }
};

// Try to build the frontend using multiple approaches
try {
  console.log('Starting Vite build process with multiple fallback options...');
  const rootDir = path.join(__dirname, '..');
  const viteBinPath = path.join(rootDir, 'node_modules', '.bin', 'vite');
  
  // Approach 1: Check if vite executable exists directly in node_modules/.bin
  if (fileExists(viteBinPath)) {
    console.log('Found Vite in node_modules/.bin directory');
    if (runCommand(`"${viteBinPath}" build`)) {
      console.log('Vite build completed successfully using local binary');
      process.exit(0);
    }
  }
  
  // Approach 2: Try using npx to run vite
  console.log('Trying to build with npx...');
  if (runCommand('npx --no-install vite build')) {
    console.log('Vite build completed successfully using npx');
    process.exit(0);
  }
  
  // Approach 3: Install vite globally and try again
  console.log('Trying to install vite and dependencies...');
  runCommand('npm install --no-save vite @vitejs/plugin-react tailwindcss autoprefixer postcss');
  
  if (fileExists(viteBinPath)) {
    if (runCommand(`"${viteBinPath}" build`)) {
      console.log('Vite build completed successfully after installing dependencies');
      process.exit(0);
    }
  }
  
  // Approach 4: Use NODE_PATH to find modules
  console.log('Trying with explicit NODE_PATH...');
  const nodeModulesPath = path.join(rootDir, 'node_modules');
  if (runCommand('npx vite build', {
    env: {
      ...process.env,
      NODE_ENV: 'production',
      NODE_PATH: nodeModulesPath
    }
  })) {
    console.log('Vite build completed successfully with NODE_PATH');
    process.exit(0);
  }
  
  throw new Error('All Vite build approaches failed');
  
} catch (buildError) {
  console.error('All build approaches failed:', buildError.message);
  
  // Fallback: Create a basic frontend
  console.log('Creating fallback frontend...');
  
  // Create dist directory if it doesn't exist
  const distDir = path.join(__dirname, '..', 'dist');
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }
  
  // Copy index.html to dist folder or create a basic one
  const sourceHtml = path.join(__dirname, '..', 'index.html');
  const targetHtml = path.join(distDir, 'index.html');
  
  if (fs.existsSync(sourceHtml)) {
    fs.copyFileSync(sourceHtml, targetHtml);
    console.log('Copied index.html to dist folder');
  } else {
    // Create a basic HTML file if source doesn't exist
    const basicHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Onbord Financial Dashboard</title>
</head>
<body>
  <div id="root">
    <h1>Onbord Financial Dashboard</h1>
    <p>The API is running. Frontend build is unavailable.</p>
    <p><a href="/api/health">Check API Health</a></p>
  </div>
</body>
</html>`;
    
    fs.writeFileSync(targetHtml, basicHtml);
    console.log('Created basic index.html in dist folder');
  }
  
  // Create an empty main.js file to prevent 404 errors
  fs.writeFileSync(path.join(distDir, 'main.js'), '// Fallback main.js');
  console.log('Created empty main.js in dist folder');
}

console.log('Frontend build process complete');
