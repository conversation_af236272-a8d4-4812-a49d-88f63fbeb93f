const fs = require('fs');
const path = require('path');

// Create types directory if it doesn't exist
const typesDir = path.join(__dirname, '..', 'dist', 'types');
if (!fs.existsSync(typesDir)) {
  fs.mkdirSync(typesDir, { recursive: true });
}

console.log('Creating types module...');

// Create the types content
const typesContent = `
// Simple JavaScript classes for types from src/types.ts
class CustomExpense {
  constructor(id, name, type, amount, date, frequency) {
    this.id = id;
    this.name = name;
    this.type = type;
    this.amount = amount;
    this.date = date;
    this.frequency = frequency;
  }
}

class Transaction {
  constructor(id, type, what, date, source, amount) {
    this.id = id;
    this.type = type;
    this.what = what;
    this.date = date;
    this.source = source;
    this.amount = amount;
  }
}

class DailyCashflow {
  constructor(date, inflows, outflows, netFlow, balance, transactions) {
    this.date = date;
    this.inflows = inflows;
    this.outflows = outflows;
    this.netFlow = netFlow;
    this.balance = balance;
    this.transactions = transactions || [];
  }
}

class BankAccount {
  constructor(id, name, closingBalance, openingBalance) {
    this.id = id;
    this.name = name;
    this.closingBalance = closingBalance;
    this.openingBalance = openingBalance;
  }
}

class BankBalances {
  constructor(totalClosingBalance, accounts, totalOpeningBalance) {
    this.totalClosingBalance = totalClosingBalance;
    this.accounts = accounts || [];
    this.totalOpeningBalance = totalOpeningBalance;
  }
}

class CashflowForecast {
  constructor(options = {}) {
    this.startDate = options.startDate;
    this.endDate = options.endDate;
    this.startingBalance = options.startingBalance;
    this.endingBalance = options.endingBalance;
    this.currentBalance = options.currentBalance;
    this.projectedBalance = options.projectedBalance;
    this.projectedChange = options.projectedChange;
    this.accounts = options.accounts || [];
    this.projectedTransactions = options.projectedTransactions || [];
    this.transactions = options.transactions || [];
    this.dailyCashflow = options.dailyCashflow || [];
    this.customExpenses = options.customExpenses;
  }
}

module.exports = {
  CustomExpense,
  Transaction,
  DailyCashflow,
  BankAccount,
  BankBalances,
  CashflowForecast
};
`;

// Write the types file
fs.writeFileSync(path.join(typesDir, 'index.js'), typesContent);
console.log('TypeScript types file created in dist/types/index.js');