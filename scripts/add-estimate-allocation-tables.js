#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to add missing estimate allocation tables
 * This script can be run safely multiple times - it checks if tables exist first
 */

const Database = require('better-sqlite3');
const path = require('path');

// Determine database path based on environment
const dbPath = process.env.NODE_ENV === 'production' || process.env.RENDER === 'true'
  ? '/data/upstream.db'
  : path.join(__dirname, '..', 'data', 'upstream.db');

console.log(`Using database at: ${dbPath}`);

// Open database connection
const db = new Database(dbPath);

try {
  // Check if tables already exist
  const estimateAllocationExists = db.prepare(`
    SELECT 1 FROM sqlite_master 
    WHERE type='table' AND name='estimate_allocation'
  `).get();
  
  const estimateTimeAllocationExists = db.prepare(`
    SELECT 1 FROM sqlite_master 
    WHERE type='table' AND name='estimate_time_allocation'
  `).get();
  
  if (estimateAllocationExists && estimateTimeAllocationExists) {
    console.log('Both estimate allocation tables already exist');
  } else {
    console.log('Creating missing estimate allocation tables...');
    
    // Start a transaction
    db.prepare('BEGIN').run();
    
    try {
      // Create estimate_allocation table if it doesn't exist
      if (!estimateAllocationExists) {
        console.log('Creating estimate_allocation table...');
        db.prepare(`
          CREATE TABLE estimate_allocation (
            id TEXT PRIMARY KEY,
            estimate_id TEXT NOT NULL,
            harvest_user_id INTEGER NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT,
            project_role TEXT,
            level TEXT,
            target_rate_daily REAL NOT NULL,
            cost_rate_daily REAL NOT NULL,
            proposed_rate_daily REAL NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
          )
        `).run();
        
        // Create indexes
        db.prepare(`CREATE INDEX idx_estimate_allocation_estimate_id ON estimate_allocation(estimate_id)`).run();
        db.prepare(`CREATE INDEX idx_estimate_allocation_harvest_user_id ON estimate_allocation(harvest_user_id)`).run();
        
        console.log('✓ estimate_allocation table created');
      }
      
      // Create estimate_time_allocation table if it doesn't exist
      if (!estimateTimeAllocationExists) {
        console.log('Creating estimate_time_allocation table...');
        db.prepare(`
          CREATE TABLE estimate_time_allocation (
            id TEXT PRIMARY KEY,
            allocation_id TEXT NOT NULL,
            week_identifier TEXT NOT NULL,
            days REAL NOT NULL,
            FOREIGN KEY (allocation_id) REFERENCES estimate_allocation(id) ON DELETE CASCADE,
            UNIQUE(allocation_id, week_identifier)
          )
        `).run();
        
        // Create indexes
        db.prepare(`CREATE INDEX idx_estimate_time_allocation_allocation_id ON estimate_time_allocation(allocation_id)`).run();
        db.prepare(`CREATE INDEX idx_estimate_time_allocation_week ON estimate_time_allocation(week_identifier)`).run();
        
        console.log('✓ estimate_time_allocation table created');
      }
      
      // Commit the transaction
      db.prepare('COMMIT').run();
      console.log('✅ Estimate allocation tables created successfully');
      
    } catch (error) {
      // Rollback on error
      db.prepare('ROLLBACK').run();
      throw error;
    }
  }
  
  // Verify the tables exist
  console.log('\nVerifying estimate allocation tables:');
  const tables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name IN ('estimate_allocation', 'estimate_time_allocation')
    ORDER BY name
  `).all();
  
  if (tables.length > 0) {
    console.log('Found tables:');
    tables.forEach(table => console.log(`  - ${table.name}`));
  }
  
} catch (error) {
  console.error('Error creating estimate allocation tables:', error);
  process.exit(1);
} finally {
  // Close database connection
  db.close();
}