#!/usr/bin/env node

/**
 * Fix Server Path Script
 * 
 * This script creates a simple server.js file in the root directory
 * that redirects to the actual server.js file in the dist/api directory.
 * This is a workaround for the deployment issue where the server.js file
 * is not found at the expected location.
 */

const fs = require('fs');
const path = require('path');

console.log('Creating server.js redirect file...');

// Check if the actual server.js exists
const actualServerPath = path.join(__dirname, '..', 'dist', 'api', 'server.js');
const exists = fs.existsSync(actualServerPath);

if (exists) {
  console.log(`Found actual server.js at: ${actualServerPath}`);
  
  // Create a simple redirect file
  const redirectContent = `
/**
 * Server Redirect File
 * 
 * This file redirects to the actual server.js file in the api directory.
 * It was automatically generated by the fix-server-path.js script.
 */

// In the deployment, the structure is flattened
// The api directory is at the same level as this server.js file
require('./api/server.js');
`;

  // Write the redirect file to the root directory
  const redirectPath = path.join(__dirname, '..', 'server.js');
  fs.writeFileSync(redirectPath, redirectContent);
  
  console.log(`Created redirect file at: ${redirectPath}`);
} else {
  console.error(`ERROR: Could not find server.js at: ${actualServerPath}`);
  console.log('Available files in dist directory:');
  
  try {
    const distDir = path.join(__dirname, '..', 'dist');
    if (fs.existsSync(distDir)) {
      const files = fs.readdirSync(distDir);
      console.log(files);
      
      // Check if api directory exists
      const apiDir = path.join(distDir, 'api');
      if (fs.existsSync(apiDir)) {
        const apiFiles = fs.readdirSync(apiDir);
        console.log('Files in dist/api:');
        console.log(apiFiles);
      }
    } else {
      console.log('dist directory does not exist');
    }
  } catch (error) {
    console.error('Error listing directory:', error);
  }
}
