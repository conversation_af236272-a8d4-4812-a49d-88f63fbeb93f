/**
 * Test Fresh Database Script
 *
 * This script tests the fresh database initialization by:
 * 1. Running the reset-database.js script to create a fresh database
 * 2. Verifying that all tables from the unified data model exist
 * 3. Verifying that the tables have the correct structure
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Get database path
const dbPath = path.join(__dirname, '..', 'data', 'upstream.db');

// Ask for confirmation before proceeding
rl.question('WARNING: This will reset the database and delete ALL data. Are you sure you want to continue? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes') {
    console.log('Proceeding with database reset and test...');
    
    try {
      // Run the reset-database.js script
      console.log('Running reset-database.js...');
      execSync('node scripts/reset-database.js', { 
        stdio: 'inherit',
        input: Buffer.from('yes\n') // Automatically answer "yes" to the confirmation prompt
      });
      
      console.log('\nDatabase reset completed. Now verifying the schema...');
      
      // Connect to the database
      const db = new BetterSqlite3(dbPath);
      
      // Get all tables
      const tables = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
      `).all();
      
      console.log('\nVerifying tables...');
      
      // Expected tables from the unified data model
      const expectedTables = [
        'activity',
        'cashflow_snapshot',
        'company',
        'company_relationship',
        'contact',
        'contact_company',
        'contact_role',
        'deal',
        'deal_estimate',
        'deal_stage_history',
        'estimate',
        'expense',
        'hubspot_import',
        'pipeline',
        'pipeline_stage',
        'schema_version',
        'settings',
        'tag',
        'taggable'
      ];
      
      // Check if all expected tables exist
      const missingTables = expectedTables.filter(table => !tables.some(t => t.name === table));
      
      if (missingTables.length > 0) {
        console.error('ERROR: Missing tables:', missingTables);
        process.exit(1);
      }
      
      console.log('All expected tables exist.');
      
      // Verify the structure of key tables
      console.log('\nVerifying table structures...');
      
      // Verify company table
      const companyColumns = db.prepare('PRAGMA table_info(company)').all().map(col => col.name);
      const expectedCompanyColumns = [
        'id', 'name', 'industry', 'size', 'website', 'address', 'description',
        'hubspot_id', 'harvest_id', 'source', 'radar_state', 'priority',
        'current_spend', 'potential_spend', 'last_interaction_date',
        'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at'
      ];
      
      const missingCompanyColumns = expectedCompanyColumns.filter(col => !companyColumns.includes(col));
      
      if (missingCompanyColumns.length > 0) {
        console.error('ERROR: Missing columns in company table:', missingCompanyColumns);
        process.exit(1);
      }
      
      // Verify deal table
      const dealColumns = db.prepare('PRAGMA table_info(deal)').all().map(col => col.name);
      const expectedDealColumns = [
        'id', 'name', 'stage', 'status', 'value', 'currency', 'probability',
        'expected_close_date', 'start_date', 'end_date', 'invoice_frequency', 'payment_terms',
        'company_id', 'hubspot_id', 'harvest_project_id', 'description', 'source',
        'priority', 'owner', 'custom_fields', 'include_in_projections', 'deleted_at',
        'created_at', 'updated_at', 'created_by', 'updated_by'
      ];
      
      const missingDealColumns = expectedDealColumns.filter(col => !dealColumns.includes(col));
      
      if (missingDealColumns.length > 0) {
        console.error('ERROR: Missing columns in deal table:', missingDealColumns);
        process.exit(1);
      }
      
      // Verify contact table
      const contactColumns = db.prepare('PRAGMA table_info(contact)').all().map(col => col.name);
      const expectedContactColumns = [
        'id', 'first_name', 'last_name', 'email', 'phone', 'job_title',
        'hubspot_id', 'harvest_user_id', 'source', 'deleted_at',
        'created_at', 'updated_at', 'created_by', 'updated_by', 'notes'
      ];
      
      const missingContactColumns = expectedContactColumns.filter(col => !contactColumns.includes(col));
      
      if (missingContactColumns.length > 0) {
        console.error('ERROR: Missing columns in contact table:', missingContactColumns);
        process.exit(1);
      }
      
      // Verify relationship tables
      const contactCompanyColumns = db.prepare('PRAGMA table_info(contact_company)').all().map(col => col.name);
      const expectedContactCompanyColumns = [
        'contact_id', 'company_id', 'role', 'is_primary', 'created_at', 'created_by'
      ];
      
      const missingContactCompanyColumns = expectedContactCompanyColumns.filter(col => !contactCompanyColumns.includes(col));
      
      if (missingContactCompanyColumns.length > 0) {
        console.error('ERROR: Missing columns in contact_company table:', missingContactCompanyColumns);
        process.exit(1);
      }
      
      // Close the database connection
      db.close();
      
      console.log('\nAll table structures verified successfully.');
      console.log('The fresh unified data model has been implemented correctly!');
    } catch (error) {
      console.error('Error during test:', error);
      process.exit(1);
    }
  } else {
    console.log('Test cancelled.');
  }
  
  rl.close();
});
