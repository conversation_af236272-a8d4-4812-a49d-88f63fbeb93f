/**
 * Activity Feed Migration Script
 *
 * This script adds the activity_feed table and related audit tables to existing databases.
 * It's designed to be safe to run multiple times (idempotent).
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Get database path from environment or use default
const dbPath = process.env.NODE_ENV === 'production' ? '/data/upstream.db' : path.join(__dirname, '..', 'data', 'upstream.db');

console.log(`Adding activity feed tables to database at: ${dbPath}`);

// Check if database exists
if (!fs.existsSync(dbPath)) {
  console.error('Database does not exist. Please run the initialization script first.');
  process.exit(1);
}

// Connect to database
const db = new BetterSqlite3(dbPath);

try {
  // Check if activity_feed table already exists
  const activityFeedExists = db.prepare(`
    SELECT 1 FROM sqlite_master 
    WHERE type='table' AND name='activity_feed'
  `).get();

  if (activityFeedExists) {
    console.log('✅ activity_feed table already exists');
  } else {
    console.log('📝 Creating activity_feed table...');
    
    // Create activity_feed table
    db.prepare(`
      CREATE TABLE activity_feed (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL, -- Activity type (see types below)
        subject TEXT NOT NULL, -- Brief description
        description TEXT, -- Detailed description
        status TEXT, -- 'planned', 'completed', 'canceled', 'in_progress'

        /* Entity Relationships */
        entity_type TEXT, -- 'deal', 'company', 'estimate', 'expense', 'contact'
        entity_id TEXT, -- ID of the related entity

        /* Timing Information */
        due_date TEXT,
        completed_date TEXT,

        /* Legacy Relationships (for backward compatibility) */
        company_id TEXT,
        contact_id TEXT,
        deal_id TEXT,

        /* Metadata */
        metadata TEXT, -- JSON for additional context and structured data
        is_read INTEGER DEFAULT 0, -- Track read status per activity
        importance TEXT DEFAULT 'normal', -- 'low', 'normal', 'high'

        /* User and System Information */
        created_by TEXT NOT NULL, -- User ID or 'system'
        source TEXT, -- 'user', 'hubspot', 'xero', 'harvest', 'system'

        /* Audit Information */
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,

        /* Foreign Key Constraints */
        FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
        FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
        FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
      )
    `).run();

    // Create indexes for activity_feed table
    db.prepare(`CREATE INDEX idx_activity_feed_created_at ON activity_feed(created_at DESC)`).run();
    db.prepare(`CREATE INDEX idx_activity_feed_type ON activity_feed(type)`).run();
    db.prepare(`CREATE INDEX idx_activity_feed_entity ON activity_feed(entity_type, entity_id)`).run();
    db.prepare(`CREATE INDEX idx_activity_feed_source ON activity_feed(source)`).run();
    db.prepare(`CREATE INDEX idx_activity_feed_created_by ON activity_feed(created_by)`).run();
    db.prepare(`CREATE INDEX idx_activity_feed_unread ON activity_feed(is_read)`).run();

    console.log('✅ activity_feed table created successfully');
  }

  // Check if field_ownership table exists
  const fieldOwnershipExists = db.prepare(`
    SELECT 1 FROM sqlite_master 
    WHERE type='table' AND name='field_ownership'
  `).get();

  if (fieldOwnershipExists) {
    console.log('✅ field_ownership table already exists');
  } else {
    console.log('📝 Creating field_ownership table...');
    
    // Create field_ownership table
    db.prepare(`
      CREATE TABLE field_ownership (
        id TEXT PRIMARY KEY,
        entity_type TEXT NOT NULL, -- 'deal', 'estimate', etc.
        entity_id TEXT NOT NULL,
        field_name TEXT NOT NULL,
        owner TEXT NOT NULL, -- 'HubSpot', 'Harvest', 'Manual', 'System', 'Estimate'
        set_at TEXT NOT NULL,
        set_by TEXT,
        UNIQUE(entity_type, entity_id, field_name)
      )
    `).run();

    // Create indexes for field_ownership table
    db.prepare(`CREATE INDEX idx_field_ownership_entity ON field_ownership(entity_type, entity_id)`).run();
    db.prepare(`CREATE INDEX idx_field_ownership_owner ON field_ownership(owner)`).run();

    console.log('✅ field_ownership table created successfully');
  }

  // Check if change_log table exists
  const changeLogExists = db.prepare(`
    SELECT 1 FROM sqlite_master 
    WHERE type='table' AND name='change_log'
  `).get();

  if (changeLogExists) {
    console.log('✅ change_log table already exists');
  } else {
    console.log('📝 Creating change_log table...');
    
    // Create change_log table
    db.prepare(`
      CREATE TABLE change_log (
        id TEXT PRIMARY KEY,
        entity_type TEXT NOT NULL, -- 'deal', 'estimate', etc.
        entity_id TEXT NOT NULL,
        field_name TEXT NOT NULL,
        old_value TEXT,
        new_value TEXT,
        change_source TEXT NOT NULL,
        changed_at TEXT NOT NULL,
        changed_by TEXT
      )
    `).run();

    // Create indexes for change_log table
    db.prepare(`CREATE INDEX idx_change_log_entity ON change_log(entity_type, entity_id)`).run();
    db.prepare(`CREATE INDEX idx_change_log_field ON change_log(field_name)`).run();
    db.prepare(`CREATE INDEX idx_change_log_date ON change_log(changed_at)`).run();

    console.log('✅ change_log table created successfully');
  }

  // Insert a test activity to verify everything works
  if (!activityFeedExists) {
    console.log('📝 Adding initial activity...');
    
    const testActivity = {
      id: 'migration-' + Date.now(),
      type: 'system_event',
      subject: 'Activity feed system initialized',
      description: 'The activity feed tables have been successfully added to the database.',
      status: 'completed',
      created_by: 'system',
      source: 'system',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    db.prepare(`
      INSERT INTO activity_feed (
        id, type, subject, description, status, created_by, source, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      testActivity.id,
      testActivity.type,
      testActivity.subject,
      testActivity.description,
      testActivity.status,
      testActivity.created_by,
      testActivity.source,
      testActivity.created_at,
      testActivity.updated_at
    );

    console.log('✅ Initial activity added successfully');
  }

  // Verify the tables exist and are accessible
  const activityCount = db.prepare('SELECT COUNT(*) as count FROM activity_feed').get();
  console.log(`✅ Activity feed table contains ${activityCount.count} activities`);

  console.log('\n🎉 Activity feed migration completed successfully!');
  console.log('The activity feed feature is now ready to use.');

} catch (error) {
  console.error('\n❌ Error during migration:', error);
  process.exit(1);
} finally {
  // Close the database connection
  db.close();
}
