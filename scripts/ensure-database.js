#!/usr/bin/env node

/**
 * Ensure Database Script
 *
 * This script ensures the database exists and has all required tables.
 * It can be run as part of the startup process on Render.
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Get database path
const dbPath = process.env.NODE_ENV === 'production' ? '/data/upstream.db' : path.join(__dirname, '..', 'data', 'upstream.db');

console.log(`Ensuring database at: ${dbPath}`);

// Ensure directory exists
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Connect to database
const db = new BetterSqlite3(dbPath);

try {
  // Check if schema exists
  const schemaExists = db.prepare(`
    SELECT 1 FROM sqlite_master
    WHERE type='table' AND name='schema_version'
  `).get();

  if (!schemaExists) {
    console.log('Database needs initialization. Running initialization script...');

    // Close current connection
    db.close();

    // Run the initialization script
    const initScript = path.join(__dirname, 'initialize-fresh-database.js');
    try {
      require('child_process').execSync(`node ${initScript}`, {
        stdio: 'inherit',
        env: { ...process.env, DB_PATH: dbPath }
      });
      console.log('Database initialization completed');
    } catch (error) {
      console.error('Initialization script failed, but continuing startup...');
      // Continue execution - the database might still be usable
    }
  } else {
    console.log('Database already initialized');

    // Check for critical tables
    const tables = ['company', 'contact', 'deal', 'estimate', 'note', 'expense', 'harvest_invoice_cache', 'activity_feed'];
    const missingTables = [];

    for (const table of tables) {
      const exists = db.prepare(`
        SELECT 1 FROM sqlite_master
        WHERE type='table' AND name=?
      `).get(table);

      if (!exists) {
        missingTables.push(table);
      }
    }

    if (missingTables.length > 0) {
      console.log(`Missing tables detected: ${missingTables.join(', ')}`);
      console.log('Running initialization to fix...');

      db.close();

      // Run the initialization script
      const initScript = path.join(__dirname, 'initialize-fresh-database.js');
      try {
        require('child_process').execSync(`node ${initScript}`, {
          stdio: 'inherit',
          env: { ...process.env, DB_PATH: dbPath }
        });
      } catch (error) {
        console.error('Initialization script failed, but continuing startup...');
        // Continue execution - the database might still be usable
      }
    }

    console.log('Database check completed');

    // Run column migration for company table
    console.log('Checking for required column updates...');
    db.close();

    const companyMigrationScript = path.join(__dirname, 'add-company-notes-contacts.js');
    if (fs.existsSync(companyMigrationScript)) {
      try {
        require('child_process').execSync(`node ${companyMigrationScript}`, {
          stdio: 'inherit',
          env: { ...process.env }
        });
      } catch (error) {
        console.error('Company column migration failed, but continuing startup...');
      }
    }

    // Run estimate allocation tables migration
    const estimateMigrationScript = path.join(__dirname, 'add-estimate-allocation-tables.js');
    if (fs.existsSync(estimateMigrationScript)) {
      try {
        require('child_process').execSync(`node ${estimateMigrationScript}`, {
          stdio: 'inherit',
          env: { ...process.env }
        });
      } catch (error) {
        console.error('Estimate allocation tables migration failed, but continuing startup...');
      }
    }

    // Run activity feed migration
    const activityMigrationScript = path.join(__dirname, 'add-activity-feed-migration.js');
    if (fs.existsSync(activityMigrationScript)) {
      try {
        require('child_process').execSync(`node ${activityMigrationScript}`, {
          stdio: 'inherit',
          env: { ...process.env }
        });
      } catch (error) {
        console.error('Activity feed migration failed, but continuing startup...');
      }
    }
  }
} catch (error) {
  console.error('Error ensuring database:', error);
  process.exit(1);
} finally {
  if (db.open) {
    db.close();
  }
}