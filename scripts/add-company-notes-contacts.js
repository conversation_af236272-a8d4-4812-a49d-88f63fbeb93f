#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to add missing notes and contacts columns to company table
 * This script can be run safely multiple times - it checks if columns exist first
 */

const Database = require('better-sqlite3');
const path = require('path');

// Determine database path based on environment
const dbPath = process.env.NODE_ENV === 'production' || process.env.RENDER === 'true'
  ? '/data/upstream.db'
  : path.join(__dirname, '..', 'data', 'upstream.db');

console.log(`Using database at: ${dbPath}`);

// Open database connection
const db = new Database(dbPath);

try {
  // Check if columns already exist
  const tableInfo = db.prepare("PRAGMA table_info(company)").all();
  const columnNames = tableInfo.map(col => col.name);
  
  const hasNotes = columnNames.includes('notes');
  const hasContacts = columnNames.includes('contacts');
  
  if (hasNotes && hasContacts) {
    console.log('Both notes and contacts columns already exist in company table');
  } else {
    console.log('Adding missing columns to company table...');
    
    // Start a transaction
    db.prepare('BEGIN').run();
    
    try {
      // Add notes column if it doesn't exist
      if (!hasNotes) {
        console.log('Adding notes column...');
        db.prepare('ALTER TABLE company ADD COLUMN notes TEXT').run();
        console.log('✓ notes column added');
      }
      
      // Add contacts column if it doesn't exist
      if (!hasContacts) {
        console.log('Adding contacts column...');
        db.prepare('ALTER TABLE company ADD COLUMN contacts INTEGER').run();
        console.log('✓ contacts column added');
      }
      
      // Commit the transaction
      db.prepare('COMMIT').run();
      console.log('✅ Company table updated successfully');
      
    } catch (error) {
      // Rollback on error
      db.prepare('ROLLBACK').run();
      throw error;
    }
  }
  
  // Verify the columns exist
  console.log('\nVerifying company table structure:');
  const updatedTableInfo = db.prepare("PRAGMA table_info(company)").all();
  const relevantColumns = updatedTableInfo
    .filter(col => ['notes', 'contacts'].includes(col.name))
    .map(col => `  - ${col.name} (${col.type})`);
  
  if (relevantColumns.length > 0) {
    console.log('Found columns:');
    console.log(relevantColumns.join('\n'));
  }
  
} catch (error) {
  console.error('Error updating company table:', error);
  process.exit(1);
} finally {
  // Close database connection
  db.close();
}