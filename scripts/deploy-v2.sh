#!/bin/bash
set -e

# Modern deployment script for Onbord Financial Dashboard
# Supports both preview and production deployments

# Determine environment from command line argument
ENVIRONMENT=${1:-preview}
if [[ "$ENVIRONMENT" != "preview" && "$ENVIRONMENT" != "production" ]]; then
  echo "❌ Invalid environment. Use: deploy-v2.sh [preview|production]"
  exit 1
fi

# Set variables based on environment
if [ "$ENVIRONMENT" == "preview" ]; then
  SOURCE_BRANCH="preview"
  TARGET_BRANCH="pre-built-preview"
else
  SOURCE_BRANCH="main"
  TARGET_BRANCH="pre-built-production"
fi

echo "=== DEPLOYING TO $ENVIRONMENT ==="
echo "Source: $SOURCE_BRANCH → Target: $TARGET_BRANCH"

# 1. Verify we're on the correct source branch
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "$SOURCE_BRANCH" ]; then
  echo "❌ You must be on the $SOURCE_BRANCH branch"
  echo "   Current branch: $CURRENT_BRANCH"
  echo "   Run: git checkout $SOURCE_BRANCH"
  exit 1
fi

# 2. Ensure working directory is clean
if [ -n "$(git status --porcelain)" ]; then
  echo "❌ You have uncommitted changes. Please commit or stash them."
  exit 1
fi

# 3. Pull latest changes
echo "📥 Pulling latest changes..."
git pull origin $SOURCE_BRANCH

# 4. Install dependencies
echo "📦 Installing dependencies..."
npm ci --legacy-peer-deps

# 5. Run tests (optional but recommended)
# echo "🧪 Running tests..."
# npm test

# 6. Build the application
echo "🔨 Building application..."
# Clean previous build
rm -rf dist

# Build backend (TypeScript compilation)
echo "  → Building backend..."
npx tsc --project tsconfig.backend.json || {
  echo "❌ Backend build failed. Installing required types..."
  npm install --save-dev @types/node @types/express @types/cookie-parser @types/socket.io
  npx tsc --project tsconfig.backend.json
}

# Build frontend
echo "  → Building frontend..."
npm run build:frontend

# Copy static assets
echo "  → Copying static assets..."
npm run copy:public

# Create types
echo "  → Creating type definitions..."
npm run create:types

# Copy essential scripts
echo "  → Copying essential scripts..."
mkdir -p dist/scripts
cp scripts/ensure-database.js dist/scripts/
cp scripts/initialize-fresh-database.js dist/scripts/
cp scripts/fix-production-database.js dist/scripts/
cp scripts/fix-server-path.js dist/scripts/
cp scripts/add-company-notes-contacts.js dist/scripts/
cp scripts/add-estimate-allocation-tables.js dist/scripts/
cp scripts/add-activity-feed-migration.js dist/scripts/

# Create server redirect
echo "  → Creating server.js redirect..."
node scripts/fix-server-path.js

# 7. Prepare deployment package
echo "📦 Preparing deployment package..."
TEMP_DIR=$(mktemp -d)
echo "  → Using temp directory: $TEMP_DIR"

# Copy build output
cp -r dist/* $TEMP_DIR/

# Copy the server.js redirect file
if [ -f server.js ]; then
  echo "  → Copying server.js redirect..."
  cp server.js $TEMP_DIR/
else
  echo "  ⚠️  Warning: server.js redirect not found"
fi

# Create minimal package.json with all runtime dependencies
echo "  → Creating package.json with all dependencies..."
node -e "
const pkg = require('./package.json');
const deployPkg = {
  name: 'onbord-financial-dashboard-$ENVIRONMENT',
  version: pkg.version,
  private: true,
  scripts: {
    start: 'node scripts/ensure-database.js && node server.js',
    postinstall: 'npm rebuild better-sqlite3'
  },
  engines: {
    node: '18.17.1'
  },
  dependencies: pkg.dependencies
};
console.log(JSON.stringify(deployPkg, null, 2));
" > $TEMP_DIR/package.json

# Create environment marker
echo $ENVIRONMENT > $TEMP_DIR/env-$ENVIRONMENT

# Create deployment info
cat > $TEMP_DIR/DEPLOY_INFO.md << EOL
Environment: $ENVIRONMENT
Source Branch: $SOURCE_BRANCH
Commit: $(git rev-parse HEAD)
Commit Message: $(git log -1 --pretty=%B)
Build Date: $(date)
Node Version: $(node --version)
NPM Version: $(npm --version)
EOL

# 8. Deploy to target branch
echo "🚀 Deploying to $TARGET_BRANCH branch..."

# Save current commit info
COMMIT_HASH=$(git rev-parse HEAD)
COMMIT_MSG=$(git log -1 --pretty=%B)

# Switch to target branch
git fetch origin $TARGET_BRANCH 2>/dev/null || true
git checkout $TARGET_BRANCH 2>/dev/null || git checkout -b $TARGET_BRANCH

# Clean target branch (except .git)
find . -mindepth 1 -maxdepth 1 -not -name ".git" -exec rm -rf {} \;

# Copy deployment package
cp -r $TEMP_DIR/* .

# Commit and push
git add .
git commit -m "Deploy $ENVIRONMENT: $COMMIT_MSG" || {
  echo "⚠️  No changes to deploy"
  git checkout $SOURCE_BRANCH
  rm -rf $TEMP_DIR
  exit 0
}

git push -f origin $TARGET_BRANCH

# 9. Cleanup and return
echo "🧹 Cleaning up..."
git checkout $SOURCE_BRANCH
rm -rf $TEMP_DIR

# 10. Success message
echo ""
echo "✅ Deployment to $ENVIRONMENT complete!"
echo "📊 Deployment Details:"
echo "   - Environment: $ENVIRONMENT"
echo "   - Source: $SOURCE_BRANCH @ $COMMIT_HASH"
echo "   - Target: $TARGET_BRANCH"
echo "   - Render should auto-deploy shortly"
echo ""
echo "💡 To restore your development environment:"
echo "   npm install"
echo "   npm run dev"