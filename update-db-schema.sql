-- Create a temporary table with the new schema
CREATE TABLE estimate_draft_allocations_temp (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  estimate_draft_uuid TEXT NOT NULL,
  internal_id TEXT UNIQUE NOT NULL,
  harvest_user_id INTEGER NOT NULL,
  first_name TEXT NOT NULL,
  last_name TEXT,
  project_role TEXT,
  level TEXT,
  onbord_target_rate_daily REAL NOT NULL,
  onbord_cost_rate_daily REAL NOT NULL,
  rate_proposed_daily REAL NOT NULL,
  FOREIGN KEY (estimate_draft_uuid) REFERENCES estimate_drafts(uuid) ON DELETE CASCADE
);

-- Copy data from the original table to the temporary table, mapping old columns to new ones
INSERT INTO estimate_draft_allocations_temp (
  id, estimate_draft_uuid, internal_id, harvest_user_id,
  first_name, last_name, project_role, level,
  onbord_target_rate_daily, onbord_cost_rate_daily, rate_proposed_daily
)
SELECT
  id, estimate_uuid, internal_id, staff_id,
  staff_name, NULL, role, NULL,
  rate, rate * 0.6, rate
FROM estimate_draft_allocations;

-- Drop the original table
DROP TABLE estimate_draft_allocations;

-- Rename the temporary table to the original table name
ALTER TABLE estimate_draft_allocations_temp RENAME TO estimate_draft_allocations;

-- Verify the schema was updated successfully
PRAGMA table_info(estimate_draft_allocations);
