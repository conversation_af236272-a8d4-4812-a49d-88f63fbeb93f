/** @type {import('jest').Config} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/tests/integration'],
  testMatch: [
    '**/?(*.)+(spec|test).[jt]s?(x)'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup/setup.int.ts'],
  testTimeout: 180000, // 3 minutes timeout for E2E tests (increased from 2 minutes)
  verbose: true,
  // Don't run tests in parallel to avoid conflicts
  maxConcurrency: 1,
  maxWorkers: 1
};
