import React from "react";
import ReactDOM from "react-dom/client";
import axios from "axios";
import { QueryClient, QueryClientProvider } from "react-query";
import App from "./frontend/components/App";
import { LoadingProvider } from "./frontend/contexts/LoadingContext";
import "./tailwind.css";
import "./frontend/styles/short-term-cashflow.css";
import "./frontend/styles/components/command-menu.css";

// Configure axios globally
axios.defaults.withCredentials = true;
console.log("Axios configured with withCredentials=true globally");

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Create root element and render the app
const rootElement = document.getElementById("root");

if (!rootElement) {
  throw new Error("Failed to find the root element");
}

const root = ReactDOM.createRoot(rootElement);

root.render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <LoadingProvider>
        <App />
      </LoadingProvider>
    </QueryClientProvider>
  </React.StrictMode>
);
