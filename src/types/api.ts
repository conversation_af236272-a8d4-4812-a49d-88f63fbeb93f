/**
 * API-specific type definitions
 *
 * This file contains types related to API requests, responses, and configurations.
 */

export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: Record<string, string | number | boolean>;
  headers?: Record<string, string>;
  data?: unknown;
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface ApiError extends Error {
  status?: number;
  code?: string;
  data?: unknown;
}

export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  retryAfterMs: number;
}

export interface ApiClientConfig {
  baseUrl: string;
  rateLimit?: RateLimitConfig;
  timeout?: number;
  retryAttempts?: number;
}

export interface ApiResponse<T> {
  data: T;
  meta?: {
    page?: number;
    perPage?: number;
    total?: number;
  };
}

/**
 * Draft Estimate Interfaces
 */
export interface DraftEstimateTimeAllocation {
  weekIdentifier: string;
  days: number;
}

export interface DraftEstimateAllocation {
  internalId: string;
  harvestUserId: number;
  firstName: string;
  lastName: string | null;
  projectRole: string | null;
  level: string | null;
  onbordTargetRateDaily: number;
  onbordCostRateDaily: number;
  rateProposedDaily: number;
  timeAllocations: DraftEstimateTimeAllocation[];
}

export interface DraftEstimate {
  uuid: string;
  clientId: number;
  clientName: string;
  projectName: string | null;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  harvestEstimateId: number | null;
  userId: string;
  notes: string | null;
  status: 'draft' | 'published' | 'archived';
  // Discount fields
  discountType: 'percentage' | 'amount' | 'none';
  discountValue: number;
  // Invoice and payment fields
  invoiceFrequency?: string; // 'weekly', 'biweekly', 'monthly', 'custom', 'upfront', 'completion'
  paymentTerms?: number; // days until payment due
  allocations: DraftEstimateAllocation[];
  // Optional calculated fields
  totalFees?: number;
}

export interface DraftEstimateSummary {
  uuid: string;
  clientName: string;
  projectName: string | null;
  startDate: string;
  endDate: string;
  updatedAt: string;
  status: 'draft' | 'published' | 'archived';
  harvestEstimateId: number | null;
}

export interface DraftEstimateAllocationInput {
  internalId: string;
  harvestUserId: number;
  firstName: string;
  lastName?: string | null;
  projectRole?: string | null;
  level?: string | null;
  onbordTargetRateDaily: number;
  onbordCostRateDaily: number;
  rateProposedDaily: number;
  weeklyAllocation: Record<string, number>; // Convert to timeAllocations in repository
}

export interface DraftEstimateCreate {
  uuid?: string; // Optional, will be generated if not provided
  clientId: number;
  clientName: string;
  projectName?: string;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  userId: string;
  notes?: string;
  // Discount fields
  discountType?: 'percentage' | 'amount' | 'none';
  discountValue?: number;
  // Invoice and payment fields
  invoiceFrequency?: string; // 'weekly', 'biweekly', 'monthly', 'custom', 'upfront', 'completion'
  paymentTerms?: number; // days until payment due
  allocations: DraftEstimateAllocationInput[];
}

export interface DraftEstimateUpdate extends Partial<DraftEstimateCreate> {
  updatedAt?: string; // Will be set automatically in repository
  harvestEstimateId?: number | null;
  status?: 'draft' | 'published' | 'archived';
}

// Re-export the Transaction interface from financial types
// This allows api.ts users to continue importing Transaction from here
export type { Transaction } from './financial';
