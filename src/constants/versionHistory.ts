/**
 * Version history for the application
 * Hierarchically organized with major releases containing minor releases and bug fixes
 */

export interface PatchRelease {
  version: string;
  date: string; // ISO format date string
  title: string;
  description: string;
  changes: string[];
}

export interface MinorRelease {
  version: string;
  date: string; // ISO format date string
  title: string;
  description: string;
  changes: string[];
  patches: PatchRelease[];
}

export interface MajorRelease {
  version: string;
  date: string; // ISO format date string
  title: string;
  description: string;
  changes: string[];
  minorReleases: MinorRelease[];
}

export type VersionUpdate = MajorRelease | MinorRelease | PatchRelease;

/**
 * Complete version history with hierarchical organization
 * Major releases represent significant functional updates
 */
export const versionHistory: MajorRelease[] = [
  {
    version: "0.26.1",
    date: "2025-01-27",
    title: "Data Management UI Improvements",
    description: "Enhanced data management table layout with improved LinkedStatus component for better readability and user experience",
    changes: [
      "Improved LinkedStatus component layout with two-line display structure",
      "Enhanced data management table readability by separating badges from action elements",
      "Optimized unlink button styling to prevent text wrapping issues",
      "Streamlined company linking interface with cleaner visual hierarchy"
    ],
    minorReleases: []
  },
  {
    version: "0.26.0",
    date: "2025-01-27",
    title: "Complete UI/CSS Refactoring & Component Modernization",
    description: "Comprehensive UI/CSS refactoring project implementing modern design system with type-safe components, container queries, and enhanced accessibility across all application components",
    changes: [
      "Implemented comprehensive UI/CSS refactoring across 4 major phases",
      "Created unified design system with Tailwind layers and consolidated color system",
      "Added container query support with @tailwindcss/container-queries plugin",
      "Developed type-safe component variant system with full TypeScript support",
      "Modernized badge system with enhanced accessibility and performance",
      "Migrated form components to new system with improved UX patterns",
      "Implemented DataList system with responsive container-based layouts",
      "Enhanced estimate management with draft/published workflow",
      "Added comprehensive form validation and error handling",
      "Created reusable component library with consistent API patterns",
      "Fixed CSS duplication issues causing layout overlap problems",
      "Enhanced developer experience with IntelliSense and type checking",
      "Maintained zero breaking changes with full backwards compatibility",
      "Added comprehensive documentation and migration guides",
      "Improved build performance and bundle optimization"
    ],
    minorReleases: [
      {
        version: "0.26.1",
        date: "2025-01-27",
        title: "Phase 2D - Form System Modernization",
        description: "Complete overhaul of form components with enhanced UX, validation, and accessibility features",
        changes: [
          "Modernized EstimateConfigurationForm with improved layout and validation",
          "Enhanced EstimatesList with better filtering and sorting capabilities",
          "Updated EstimatePage with streamlined user interface",
          "Improved ExpenseList and ExpenseListItem with consistent styling",
          "Enhanced ForwardProjection components with better data visualization",
          "Added comprehensive form hooks for state management",
          "Implemented draft estimate workflow with auto-save functionality",
          "Created reusable estimate card components for different states",
          "Added media query hooks for responsive design patterns"
        ],
        patches: [
          {
            version: "********",
            date: "2025-01-27",
            title: "Estimate Creation Logic Fixes",
            description: "Fixed estimate creation data model logic and improved dropdown UI for invoice frequency and payment terms",
            changes: [
              "Fixed estimate creation to properly use unified data model for company selection",
              "Removed unnecessary company name matching logic in favor of direct company ID lookup",
              "Updated client dropdown to show all companies from Upstream database instead of filtering to Harvest-linked only",
              "Fixed validation logic to always require invoice frequency and payment terms for estimate initialization",
              "Enhanced dropdown UI with icons, visual feedback, and improved styling",
              "Added dynamic border colors (green when selected, red when required but empty)",
              "Improved user experience with checkmark icons and better placeholder text",
              "Added inline validation error messages for required fields",
              "Maintained responsive design with proper mobile support",
              "Moved Harvest ID validation to initialization time instead of client selection time"
            ]
          },
          {
            version: "********",
            date: "2025-05-28",
            title: "Estimates UI & UX Enhancements",
            description: "Enhanced estimates UI with improved color scheme, pricing model controls, and budget summary improvements",
            changes: [
              "Updated estimates UI color scheme from blue to emerald for better brand consistency",
              "Enhanced pricing model controls - now shows 'Pricing Model' instead of 'Rate Display'",
              "Hidden hours per day control when daily pricing model is selected for cleaner UI",
              "Improved budget summary with collapsed gross margin by default and better help text",
              "Added 'View Deal' button for linked deals with external link styling",
              "Enhanced deal and estimate linking modals with emerald color scheme",
              "Fixed team members table rate input to properly handle blur events and Enter key",
              "Added explanatory text for invoice and payment terms in estimate configuration",
              "Improved form field visual feedback with blue color for selected invoice/payment terms",
              "Reorganized estimate creation workflow with better visual hierarchy"
            ]
          },
          {
            version: "********",
            date: "2025-05-28",
            title: "Code Formatting & Navigation Updates",
            description: "Applied consistent code formatting and updated navigation labels for better clarity",
            changes: [
              "Applied consistent double-quote formatting across React components",
              "Updated navigation labels: 'Deals' → 'CRM' and 'Leads' → 'Radar' for clarity",
              "Updated SearchCommand to show 'CRM Board' instead of 'Deal Board'",
              "Removed 'NEW' badge from Radar navigation tab",
              "Updated help documentation to use 'Late Bills' and 'Late Invoices' terminology",
              "Standardized component formatting and code style across CRM modules",
              "Improved readability with consistent spacing and quote usage",
              "Enhanced search functionality with better formatting and structure"
            ]
          }
        ]
      },
      {
        version: "0.26.2",
        date: "2025-01-27",
        title: "Phase 2C - DataList System Implementation",
        description: "Implementation of container query-based responsive list components with enhanced user experience",
        changes: [
          "Migrated DecisionTable to DataList system with container queries",
          "Updated TransactionsList with responsive layout patterns",
          "Enhanced TransactionsListItem with improved accessibility",
          "Implemented consistent data visualization across list components",
          "Added responsive breakpoints at 768px for optimal mobile experience",
          "Created reusable DataList patterns for future component migrations"
        ],
        patches: []
      },
      {
        version: "0.26.3",
        date: "2025-01-27",
        title: "Phase 2B - Form Components Migration",
        description: "Migration of form components to new design system with enhanced type safety and accessibility",
        changes: [
          "Created type-safe Input, Select, Textarea components with variant system",
          "Implemented FormField, FormSection, FormGrid for consistent layouts",
          "Enhanced form validation and error handling throughout application",
          "Added loading states and icon support for better user feedback",
          "Improved accessibility with proper ARIA labels and keyboard navigation",
          "Maintained backwards compatibility during component transition"
        ],
        patches: []
      },
      {
        version: "0.26.4",
        date: "2025-01-27",
        title: "Phase 2A - Badge System Modernization",
        description: "Complete modernization of badge system with type-safe variants and enhanced accessibility",
        changes: [
          "Created modern Badge and IconBadge components with variant system",
          "Enhanced XeroBadge and HarvestBadge with consistent sizing",
          "Improved PredictedBadge and AccruedBadge with design system integration",
          "Added full TypeScript support with IntelliSense for all badge components",
          "Enhanced accessibility with proper ARIA labels and screen reader support",
          "Optimized performance through style consolidation and efficient rendering"
        ],
        patches: []
      },
      {
        version: "0.26.5",
        date: "2025-01-27",
        title: "Phase 1 - Foundation Consolidation",
        description: "Establishment of unified design system foundation with Tailwind layers and container query support",
        changes: [
          "Consolidated 15+ CSS custom properties into structured Tailwind theme extensions",
          "Added @tailwindcss/container-queries plugin with proper configuration",
          "Created variant utility system with TypeScript support",
          "Established foundation CSS layer system for consistent component patterns",
          "Implemented fluid typography and spacing with clamp() functions",
          "Created type-safe component architecture with backwards compatibility"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.25.0",
    date: "2025-05-23",
    title: "Unified Data Model Implementation Fixes",
    description: "Fixed unified data model implementation issues and updated all documentation",
    changes: [
      "Fixed TypeScript type mismatches across all interfaces",
      "Resolved missing database columns in repositories",
      "Added missing methods to EnhancedRepository",
      "Corrected better-sqlite3 type definitions",
      "Removed references to non-existent columns",
      "Updated all documentation to reflect current implementation",
      "Cleaned up deprecated documentation files",
      "Fixed contact and deal repository queries",
      "Improved error handling in data access layer",
      "Added missing fields to CompanyCreate, CompanyUpdate, DealCreate, and ContactCreate interfaces"
    ],
    minorReleases: []
  },
  {
    version: "0.24.5",
    date: "2023-07-10",
    title: "Leads Radar UI Enhancements",
    description: "Improved UI and layout for the Radar subtab within the Leads tab",
    changes: [
      "Enhanced company selection modal with improved highlighting for selected companies",
      "Redesigned company cards in quadrants to be half-width instead of full-width",
      "Improved aesthetics of company cards with better visual hierarchy and company initials",
      "Added a removal drop zone for easily removing companies from the radar",
      "Replaced ellipsis menu with a more intuitive priority badge for better usability",
      "Fixed drag and drop context issues for better stability"
    ],
    minorReleases: []
  },
  {
    version: "0.24.0",
    date: "2025-05-14",
    title: "User Feedback System & Mobile Optimizations",
    description: "Added user feedback system for bug reports and feature requests, and improved mobile responsiveness",
    changes: [
      "Added floating feedback button at the bottom left of the page for bug reports and feature requests",
      "Implemented email notification system for feedback submissions using Resend API",
      "Enhanced mobile responsiveness of cashflow dashboard components",
      "Improved dark mode compatibility for late payment cards",
      "Refactored cashflow summary cards for better mobile display",
      "Implemented mobile-first responsive design approach for financial dashboard",
      "Optimized Recharts components for better mobile performance",
      "Improved tooltip positioning and responsiveness in chart components",
      "Enhanced deal cards with action buttons and estimate linking functionality",
      "Added linked estimate preview modal with summary information",
      "Improved Reports section with proper dark mode support",
      "Redesigned Deal Edit Page with card-based layout and section-based organization",
      "Enhanced CRM deal details with improved contact and estimate card displays"
    ],
    minorReleases: [
      {
        version: "0.24.4",
        date: "2025-05-14",
        title: "Deal Page UI Redesign",
        description: "Completely redesigned Deal Edit Page with a modern card-based layout and section organization",
        changes: [
          "Redesigned Deal Edit Page with card-based layout instead of tabs for better organization",
          "Created dedicated card components for contacts, notes, and estimates with improved visuals",
          "Enhanced deal estimates section with card-based visualization and action buttons",
          "Improved contacts section with better visual hierarchy and contact information display",
          "Added DealFinancialSection for financial metrics with estimate integration",
          "Enhanced DealTimelineSection for better date management and estimate linking indicators"
        ],
        patches: []
      },
      {
        version: "0.24.3",
        date: "2025-05-14",
        title: "Deal Card Enhancements & Report Improvements",
        description: "Improved deal cards with action buttons and fixed dark mode issues in reports",
        changes: [
          "Enhanced deal cards with quick edit, view/edit, and estimate action buttons",
          "Made deal cards taller with improved information density",
          "Added linked estimate preview modal with summary information and unlink capability",
          "Improved the Reports section components with proper dark mode support",
          "Added dashed green border style to the 'Link Estimate' button for consistency",
          "Fixed TaskBreakdownChart and UtilizationTable dark mode styling"
        ],
        patches: []
      },
      {
        version: "0.24.1",
        date: "2025-05-13",
        title: "User Feedback System",
        description: "Implemented user feedback system for bug reports and feature requests",
        changes: [
          "Added floating feedback button at the bottom left of the page",
          "Created modal form for submitting bug reports and feature requests",
          "Implemented backend API endpoint for feedback submission",
          "Added email notification system using Resend API",
          "Implemented proper form validation and error handling",
          "Added success and error messages for better user experience"
        ],
        patches: []
      },
      {
        version: "0.24.2",
        date: "2025-05-12",
        title: "Mobile Optimizations",
        description: "Enhanced mobile responsiveness across the application",
        changes: [
          "Improved cashflow dashboard components for mobile devices",
          "Enhanced dark mode compatibility for late payment cards",
          "Refactored cashflow summary cards for better mobile display",
          "Implemented mobile-first responsive design approach",
          "Optimized chart components for mobile screens",
          "Improved tooltip positioning and responsiveness"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.23.0",
    date: "2025-05-09",
    title: "Database Refactoring & Estimate-Deal Integration",
    description: "Comprehensive database refactoring with improved schema design and enhanced estimate-deal integration",
    changes: [
      "Implemented new database module with clean schema design for better maintainability",
      "Enhanced estimate-deal linking with improved UI and functionality",
      "Added 'Link to Deal' feature for draft estimates with intuitive workflow",
      "Implemented 'View/Edit Linked Deal' functionality to manage linked deals",
      "Added 'Create Estimate' button for streamlined estimate creation from deals",
      "Improved UI with outline-style buttons and eliminated horizontal scrolling",
      "Added 'Close' button to deal modal for better user experience",
      "Removed deprecated migrations directory and related scripts",
      "Updated all repositories and services to use the new database schema"
    ],
    minorReleases: [
      {
        version: "0.23.1",
        date: "2025-05-09",
        title: "Database Schema Refactoring",
        description: "Implemented a clean-start database approach with improved schema design",
        changes: [
          "Created new database module with comprehensive schema definitions",
          "Implemented repository pattern for data access with improved type safety",
          "Updated all services to use the new database schema",
          "Removed deprecated database code and migration system",
          "Added documentation for the new database architecture",
          "Fixed transaction handling in repositories to use synchronous methods"
        ],
        patches: []
      },
      {
        version: "0.23.2",
        date: "2025-05-09",
        title: "Estimate-Deal Integration Enhancements",
        description: "Improved estimate-deal linking with enhanced UI and functionality",
        changes: [
          "Added 'Link to Deal' button to draft estimates table",
          "Implemented 'View/Edit Linked Deal' functionality with detailed deal information",
          "Added 'Create Estimate' button next to 'Link Upstream Estimate' in deal modal",
          "Added 'Close' button to deal modal for improved user experience",
          "Updated button styles to use outline instead of filled colors",
          "Removed horizontal scrolling from estimate tables for better usability",
          "Improved text truncation for long content in tables"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.22.0",
    date: "2025-05-08",
    title: "Cashflow Time Machine",
    description: "Added the ability to view historical snapshots of cashflow projections over time",
    changes: [
      "Implemented historical snapshot storage for cashflow projections",
      "Added user interface to view and compare projections from previous dates",
      "Created automatic daily snapshot job to preserve projection history",
      "Added manual snapshot creation for key points in time",
      "Implemented projection comparison with clear visual indicators",
      "Created database schema and migration for snapshot storage",
      "Enhanced frontend components with time travel indicators and controls",
      "Improved user experience with intuitive historical data navigation"
    ],
    minorReleases: []
  },
  {
    version: "0.21.1",
    date: "2025-05-22",
    title: "Deal-Estimate Integration Improvements",
    description: "Enhanced deal-estimate linking with improved UI indicators and fixed URL issues",
    changes: [
      "Fixed deal update when linking estimates to properly refresh UI",
      "Added visual indicators for deals with linked estimates in all views",
      "Fixed estimate URL in deal detail modal to use correct format",
      "Improved modal positioning for estimate linking",
      "Fixed error in compact view mode for deals",
      "Enhanced deal cards to show linked estimate badges"
    ],
    minorReleases: [
      {
        version: "********",
        date: "2025-05-23",
        title: "Deal UI Enhancements",
        description: "Improved deal page UI with better field control and reduced redundancy",
        changes: [
          "Removed duplicate 'Go to Estimate' button from deal header for cleaner UI",
          "Made value field non-editable when linked to an estimate",
          "Added visual indicators for estimate-controlled fields",
          "Improved field ownership handling for better data integrity",
          "Enhanced UI consistency across deal edit and view modes"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.21.0",
    date: "2025-05-20",
    title: "Deal Edit Page & Project Integration",
    description: "Enhanced CRM with comprehensive Deal Edit Page and Smart Forecast integration",
    changes: [
      "Added dedicated Deal Edit Page with tabbed interface for managing all aspects of a deal",
      "Implemented project start/end dates, invoice frequency, and payment terms fields",
      "Added automatic deal updates when linking estimates",
      "Fixed estimate linking URL to correctly view linked estimates",
      "Enhanced navigation to support CRM routes",
      "Added comprehensive documentation for the Deal Edit Page",
      "Improved integration between CRM and Smart Forecast features"
    ],
    minorReleases: []
  },
  {
    version: "0.20.0",
    date: "2025-05-15",
    title: "CRM Deal Flow & Pipeline Management",
    description: "Added lightweight CRM functionality with deal flow management and HubSpot integration",
    changes: [
      "Implemented CRM Deal Flow feature with kanban board interface",
      "Added contact and company management with relationship mapping",
      "Created one-way HubSpot data import functionality",
      "Implemented deal pipeline reporting and visualization",
      "Added drag-and-drop functionality for deal stage transitions",
      "Enhanced navigation with new Deals tab",
      "Created comprehensive database schema for CRM data"
    ],
    minorReleases: [
      {
        version: "0.20.1",
        date: "2025-05-15",
        title: "Core Deal Management",
        description: "Implemented core deal management functionality with kanban board",
        changes: [
          "Created kanban board interface for deal stage visualization",
          "Implemented drag-and-drop functionality for deal stage transitions",
          "Added deal creation and editing forms with comprehensive fields",
          "Created database schema for deals, contacts, companies, and notes",
          "Implemented backend API endpoints for CRM data management"
        ],
        patches: []
      },
      {
        version: "0.20.2",
        date: "2025-05-15",
        title: "Contact & Company Management",
        description: "Added contact and company management with relationship mapping",
        changes: [
          "Implemented contact management with comprehensive profile data",
          "Added company management with industry and size tracking",
          "Created relationship mapping between deals, contacts, and companies",
          "Implemented search and filter functionality for contacts and companies",
          "Added note-taking functionality for deals with chronological history"
        ],
        patches: []
      },
      {
        version: "0.20.3",
        date: "2025-05-15",
        title: "HubSpot Integration",
        description: "Implemented one-way HubSpot data import functionality",
        changes: [
          "Added HubSpot API client integration for data import",
          "Implemented Private App access token authentication flow",
          "Created settings page for HubSpot API token management",
          "Added import history and logging for data synchronization",
          "Implemented field mapping for HubSpot to internal data model"
        ],
        patches: [
          {
            version: "********",
            date: "2025-05-16",
            title: "HubSpot Stage Mapping and Reset Functionality",
            description: "Enhanced HubSpot integration with accurate stage mapping and data reset capability",
            changes: [
              "Updated deal stage mapping to match HubSpot stages exactly",
              "Added reset CRM data functionality with UI button",
              "Updated deal board and pipeline visualization to support new stages",
              "Enhanced documentation for HubSpot integration",
              "Fixed issue with deals not appearing in kanban board"
            ]
          },
          {
            version: "********",
            date: "2025-05-17",
            title: "Deal Kanban Board Enhancements",
            description: "Improved Deal Kanban board with compact view and probability display fixes",
            changes: [
              "Added compact view toggle for Deal Kanban board with optimized space utilization",
              "Fixed probability display to correctly show percentages (e.g., 50% instead of 0.5%)",
              "Improved HubSpot integration to properly convert probability values",
              "Reduced padding between columns in compact view for better horizontal space usage",
              "Set normal view as default when loading the Deal Kanban board",
              "Updated documentation with compact view and probability display information"
            ]
          }
        ]
      }
    ]
  },
  {
    version: "0.19.0",
    date: "2025-05-13",
    title: "Late Payments Integration & Cashflow UI Enhancements",
    description: "Added late payment indicators for unpaid bills and invoices to improve cashflow visibility",
    changes: [
      "Added late payments integration showing overdue bills from Xero and invoices from Harvest",
      "Implemented expandable late payments cards with detailed overdue item information",
      "Created net effect indicator to show balance between late money in vs. out",
      "Enhanced cashflow projection page layout with redesigned summary cards",
      "Reorganized cashflow UI components for improved information hierarchy",
      "Added ability to see all overdue items with their days past due in detail view",
      "Improved responsive design for mobile and tablet use"
    ],
    minorReleases: [
      {
        version: "0.19.1",
        date: "2025-05-13",
        title: "Late Bills & Invoices Components",
        description: "Implemented components to display late bills and invoices",
        changes: [
          "Created LateBillsSummary component to display overdue bills from Xero",
          "Added LateInvoicesSummary component for unpaid Harvest invoices",
          "Implemented expandable card design with compact summary view and detailed table view",
          "Added days-past-due calculation for better overdue item prioritization",
          "Created API endpoints for retrieving late bills and invoices data"
        ],
        patches: []
      },
      {
        version: "0.19.2",
        date: "2025-05-13",
        title: "Net Effect & UI Improvements",
        description: "Added net effect indicator and improved UI layout",
        changes: [
          "Created LatePaymentsNetEffect component to show balance of late payments",
          "Added visual indicators using green/red colors for financial impact clarity",
          "Improved layout with all components in a consistent row",
          "Enhanced responsive design with proper wrapping on smaller screens",
          "Optimized dropdown table width and styling for better readability"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.18.0",
    date: "2025-05-10",
    title: "URL-Based Navigation & Security Enhancements",
    description: "Implemented URL-based navigation with React Router and enhanced authentication security",
    changes: [
      "Implemented URL-based navigation with React Router v7",
      "Created shareable URLs for all application sections",
      "Enhanced authentication security to prevent unauthenticated access to app structure",
      "Added NotFound component for invalid routes",
      "Updated deployment scripts for React Router compatibility",
      "Improved browser history support with back/forward navigation",
      "Enabled bookmarking of specific application pages",
      "Implemented proper route handling for direct URL access"
    ],
    minorReleases: [
      {
        version: "0.18.1",
        date: "2025-05-10",
        title: "React Router Implementation",
        description: "Implemented URL-based navigation with React Router v7",
        changes: [
          "Created Routes component for centralized route management",
          "Implemented BrowserRouter for client-side routing",
          "Updated navigation components to use NavLink",
          "Created routes configuration with path definitions",
          "Updated page components to use URL parameters"
        ],
        patches: []
      },
      {
        version: "0.18.2",
        date: "2025-05-10",
        title: "Authentication Security",
        description: "Enhanced authentication security to prevent unauthenticated access",
        changes: [
          "Moved authentication check to App level",
          "Prevented unauthenticated users from seeing application layout",
          "Improved authentication flow with proper redirects",
          "Fixed export/import patterns for better module resolution"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.17.0",
    date: "2025-05-06",
    title: "Xero Balance Sheet & API Improvements",
    description: "Added Xero Balance Sheet reports and enhanced API integration with improved error handling",
    changes: [
      "Added Balance Sheet report to Xero integration with expandable sections",
      "Implemented period selection for Balance Sheet (today, end of month, quarter, financial year, custom date)",
      "Enhanced Xero API client with method validation for better error detection",
      "Improved error handling for API compatibility issues",
      "Fixed API method mismatch in Xero Balance Sheet implementation",
      "Added special handling for financial years in report dates",
      "Implemented responsive Balance Sheet design with dark mode support",
      "Created comprehensive test plan for Xero Balance Sheet integration"
    ],
    minorReleases: [
      {
        version: "0.17.1",
        date: "2025-05-06",
        title: "Balance Sheet Implementation",
        description: "Implemented Balance Sheet component with Xero API integration",
        changes: [
          "Created BalanceSheetReport component with expandable sections",
          "Added XeroReportsPage with period selection and report types",
          "Implemented backend controller for Balance Sheet report",
          "Added frontend API service for Balance Sheet data",
          "Created responsive UI with dark mode support"
        ],
        patches: []
      },
      {
        version: "0.17.2",
        date: "2025-05-06",
        title: "API Robustness Improvements",
        description: "Enhanced Xero client with API method validation and improved error handling",
        changes: [
          "Added API method validation during client initialization",
          "Enhanced getBalanceSheetReport method with comprehensive parameter support",
          "Improved error handling for API compatibility issues",
          "Added custom error messages for better user feedback",
          "Updated getBalanceSheet controller to use the client wrapper method"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.16.0",
    date: "2025-05-03",
    title: "Staff Utilisation Report",
    description: "Implemented comprehensive staff utilisation reporting with interactive visualizations",
    changes: [
      "Added Staff Utilisation Report to Reports tab with capacity-based calculations",
      "Created interactive period selection with flexible time frame options",
      "Implemented task breakdown visualization for detailed staff time tracking",
      "Added utilisation summary dashboard with metrics and visualization graph",
      "Enhanced Harvest API integration with time report endpoints",
      "Improved workday calculation for more accurate capacity metrics",
      "Created visual UtilizationSummaryGraph component with color-coded utilisation indicators",
      "Used Australian English spelling consistently throughout the interface"
    ],
    minorReleases: [
      {
        version: "0.16.1",
        date: "2025-05-03",
        title: "Staff Utilisation Backend",
        description: "Implemented backend services for staff utilisation reporting",
        changes: [
          "Created HarvestTimeReportService for capacity-based utilisation calculations",
          "Enhanced Harvest API client with time report endpoints",
          "Implemented workday-based period capacity calculation",
          "Added API endpoint for staff utilisation data"
        ],
        patches: []
      },
      {
        version: "0.16.2",
        date: "2025-05-03",
        title: "Utilisation UI Components",
        description: "Created interactive UI components for utilisation reporting",
        changes: [
          "Implemented modular component architecture for utilisation reporting",
          "Added interactive period selector with multiple time frame options",
          "Created visual task breakdown charts with capacity indicators",
          "Added responsive summary dashboard with utilisation metrics",
          "Created UtilizationSummaryGraph component with color-coded bars for quick staff utilisation assessment",
          "Implemented 2-column grid layout for summary sections on larger screens"
        ],
        patches: [
          {
            version: "********",
            date: "2025-05-04",
            title: "Utilisation Graph Refinements",
            description: "Enhanced utilisation graph with improved styling and edge case handling",
            changes: [
              "Adjusted utilisation threshold to 70% for green indicator (from 75%)",
              "Added special handling for 0% utilisation with light red styling",
              "Optimized spacing and layout for better readability",
              "Improved responsive behavior across device sizes",
              "Standardized utilisation bar height and spacing"
            ]
          },
          {
            version: "********",
            date: "2025-05-04",
            title: "Help Documentation Updates",
            description: "Added Staff Utilisation Report documentation to help page",
            changes: [
              "Added comprehensive documentation for Staff Utilisation Report feature",
              "Included detailed explanation of utilisation calculation methodology",
              "Added instructions for using period selection and task breakdown",
              "Clarified difference between our utilisation calculation and Harvest's approach",
              "Enhanced overall help page structure and navigation"
            ]
          },
          {
            version: "********",
            date: "2025-05-04",
            title: "Australian English Standardisation",
            description: "Standardised Australian English spelling throughout application",
            changes: [
              "Updated ReportsPage tabs to use 'Utilisation' spelling consistently",
              "Aligned all component naming with Australian English standards",
              "Ensured consistent spelling in help documentation and user interface elements",
              "Updated README with standardised terminology",
              "Improved overall language consistency across the application"
            ]
          },
          {
            version: "********",
            date: "2025-05-04",
            title: "Technical Documentation",
            description: "Added technical documentation for Staff Utilisation Report feature",
            changes: [
              "Created comprehensive staff-utilisation-report.md in feature documentation directory",
              "Documented calculation methodology with clear examples",
              "Outlined key features including summary statistics and task breakdown",
              "Detailed time period selection options",
              "Added implementation details and future enhancement plans"
            ]
          },
          {
            version: "********",
            date: "2025-05-04",
            title: "Utilisation Summary Enhancements",
            description: "Enhanced utilisation summary with task breakdown and billable amount",
            changes: [
              "Added task breakdown segmented bar to UtilizationSummary component",
              "Implemented billable amount calculation with currency grouping",
              "Enhanced summary data visualization with color-coded segments",
              "Optimized 2-column grid layout (1:1 ratio) for summary dashboard",
              "Added detailed task legend with hours tracking"
            ]
          },
          {
            version: "********",
            date: "2025-05-05",
            title: "Task Breakdown Visualization Enhancements",
            description: "Added dedicated task breakdown components and utilities",
            changes: [
              "Created dedicated TaskBreakdownSummary component for improved visualization",
              "Implemented SimpleTooltip component for better tooltips across the app",
              "Added colorUtils utility library for consistent task coloring",
              "Created consistent color palette for task breakdown visualization",
              "Added cost constants for financial calculations",
              "Improved Harvest time report service for better task breakdown data"
            ]
          },
          {
            version: "********",
            date: "2025-05-05",
            title: "Task Breakdown Integration",
            description: "Integrated task breakdown component into main utilisation report",
            changes: [
              "Added TaskBreakdownSummary to main report layout",
              "Improved time report service task attribution",
              "Enhanced task coloring with consistent palette across visualizations",
              "Optimized report page layout for better information hierarchy",
              "Improved tooltip functionality for task breakdown visualization"
            ]
          },
          {
            version: "********",
            date: "2025-05-12",
            title: "Leave Tracking Enhancement",
            description: "Added leave tracking and exclusion to utilization calculations",
            changes: [
              "Added detailed leave hours tracking for staff utilization metrics",
              "Implemented toggle for excluding leave from utilization calculations",
              "Added adjusted utilization calculation that excludes leave from capacity",
              "Enhanced time report service with leave task detection",
              "Improved visualization components to display leave-adjusted metrics",
              "Standardized leave task detection with common leave type definitions"
            ]
          },
          {
            version: "********",
            date: "2025-05-14",
            title: "Financial Summary Enhancements",
            description: "Improved utilization summary with enhanced financial metrics",
            changes: [
              "Fixed JSX structure issue in UtilizationSummary component",
              "Added NonBillableCostBreakdown component for financial analysis",
              "Enhanced financial summary with fixed price project income tracking",
              "Improved cost calculation for contractor vs. salaried staff",
              "Added detailed logging for staff cost calculations",
              "Enhanced component organization with better grid layout"
            ]
          }
        ]
      }
    ]
  },
  {
    version: "0.15.0",
    date: "2025-05-01",
    title: "Navigation Refactoring & Code Cleanup",
    description: "Implemented unified navigation system with centralized tab definitions and comprehensive code cleanup",
    changes: [
      "Created unified navigation component with adaptive design for all screen sizes",
      "Simplified navigation architecture with single source of truth for tab definitions",
      "Moved Help button to header next to dark mode toggle for better accessibility",
      "Removed redundant navigation components and consolidated CSS",
      "Consolidated type definitions for better maintenance",
      "Removed deprecated files and directories",
      "Enhanced formatting utilities with more options and better typings",
      "Finalized environment definition cleanup"
    ],
    minorReleases: [
      {
        version: "0.15.1",
        date: "2025-05-01",
        title: "Navigation Refactoring",
        description: "Implemented unified navigation system with centralized tab definitions",
        changes: [
          "Created centralized navigation configuration in a single file",
          "Implemented unified navigation component that adapts to all screen sizes",
          "Created responsive CSS using modern techniques",
          "Eliminated special case handling for different navigation items",
          "Simplified state management with a single source of truth",
          "Removed redundant navigation components"
        ],
        patches: []
      },
      {
        version: "0.15.2",
        date: "2025-05-01",
        title: "Code Cleanup",
        description: "Comprehensive code cleanup with improved type definitions and utilities",
        changes: [
          "Removed deprecated files and directories",
          "Consolidated formatting utilities with enhanced functionality",
          "Cleaned up environment definitions",
          "Removed redundant type definitions",
          "Enhanced type safety throughout the application"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.14.0",
    date: "2025-04-26",
    title: "Add Reports Tab & Improve UI Components",
    description: "Update VS Code TypeScript settings to resolve type checking issues",
    changes: [
      "Add Reports tab to navigation",
      "Improve UI components and add GST transaction support",
      "Update VS Code TypeScript settings to resolve type checking issues",
      "Update CLAUDE.md with improved developer guidance and update chart components"
    ],
    minorReleases: [
      {
        version: "0.14.1",
        date: "2025-04-26",
        title: "Add Reports Tab",
        description: "Add Reports tab to navigation",
        changes: [
      "Add Reports tab to navigation"
        ],
        patches: []
      },
      {
        version: "0.14.2",
        date: "2025-04-26",
        title: "Improve UI Components",
        description: "Improve UI components and add GST transaction support",
        changes: [
      "Improve UI components and add GST transaction support"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.13.0",
    date: "2025-04-26",
    title: "Improve UI Components",
    description: "Update VS Code TypeScript settings to resolve type checking issues",
    changes: [
      "Improve UI components and add GST transaction support",
      "Update VS Code TypeScript settings to resolve type checking issues",
      "Update CLAUDE.md with improved developer guidance and update chart components"
    ],
    minorReleases: [
      {
        version: "0.13.1",
        date: "2025-04-26",
        title: "Improve UI Components",
        description: "Improve UI components and add GST transaction support",
        changes: [
      "Improve UI components and add GST transaction support"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.12.8",
    date: "2025-04-25",
    title: "Add Global Loading & Enhance Estimates List",
    description: "Complete phase 1 of enhanced GST prediction with updated documentation and UI components",
    changes: [
      "Add global loading system with source-specific indicators",
      "Enhance estimates list with financial metrics and staff visualization",
      "Complete phase 1 of enhanced GST prediction with updated documentation and UI components",
      "Chore: Update version history with enhanced GST projection feature",
      "Initial implementation of enhanced GST projection with invoice-based forecasting",
      "Add ATO logo for tax components and improve UI with 'NEW' badges",
      "Improve GST expense detection with more robust checks to prevent false positives",
      "GST prediction enhancement documentation"
    ],
    minorReleases: [
      {
        version: "0.12.1",
        date: "2025-04-25",
        title: "Add Global Loading",
        description: "Add global loading system with source-specific indicators",
        changes: [
      "Add global loading system with source-specific indicators"
        ],
        patches: []
      },
      {
        version: "0.12.2",
        date: "2025-04-25",
        title: "Enhance Estimates List",
        description: "Enhance estimates list with financial metrics and staff visualization",
        changes: [
      "Enhance estimates list with financial metrics and staff visualization"
        ],
        patches: []
      },
      {
        version: "0.12.3",
        date: "2025-04-25",
        title: "Complete Phase 1",
        description: "Complete phase 1 of enhanced GST prediction with updated documentation and UI components",
        changes: [
      "Complete phase 1 of enhanced GST prediction with updated documentation and UI components"
        ],
        patches: []
      },
      {
        version: "0.12.4",
        date: "2025-04-25",
        title: " Update Version",
        description: "Chore: Update version history with enhanced GST projection feature",
        changes: [
      "Chore: Update version history with enhanced GST projection feature"
        ],
        patches: []
      },
      {
        version: "0.12.5",
        date: "2025-04-25",
        title: "Initial Implementation Of",
        description: "Initial implementation of enhanced GST projection with invoice-based forecasting",
        changes: [
      "Initial implementation of enhanced GST projection with invoice-based forecasting"
        ],
        patches: []
      },
      {
        version: "0.12.6",
        date: "2025-04-25",
        title: "Add ATO Logo",
        description: "Add ATO logo for tax components and improve UI with 'NEW' badges",
        changes: [
      "Add ATO logo for tax components and improve UI with 'NEW' badges"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.12.0",
    date: "2025-04-21",
    title: "Enhance Estimates List & Complete Phase 1",
    description: "Complete phase 1 of enhanced GST prediction with updated documentation and UI components",
    changes: [
      "Enhance estimates list with financial metrics and staff visualization",
      "Complete phase 1 of enhanced GST prediction with updated documentation and UI components",
      "Chore: Update version history with enhanced GST projection feature",
      "Initial implementation of enhanced GST projection with invoice-based forecasting",
      "Add ATO logo for tax components and improve UI with 'NEW' badges",
      "Add comprehensive version history with automated update script",
      "Improve GST expense detection with more robust checks to prevent false positives",
      "GST prediction enhancement documentation",
      "Add global loading system with source-specific indicators"
    ],
    minorReleases: [
      {
        version: "0.12.1",
        date: "2025-04-23",
        title: "Global Loading System",
        description: "Implement global loading system with source-specific indicators and animations",
        changes: [
          "Implement LoadingContext provider with typed loading states (xero, harvest, generic)",
          "Create GlobalLoadingIndicator with smooth animations and source-specific branding",
          "Integrate loading states across all API requests and components",
          "Add performance monitoring for API requests",
          "Enhance user experience during loading with better visual feedback",
          "Update Tailwind config with new animations for loading indicators"
        ],
        patches: []
      },
      {
        version: "0.12.2",
        date: "2025-04-21",
        title: "Enhance Estimates List",
        description: "Enhance estimates list with financial metrics and staff visualization",
        changes: [
      "Enhance estimates list with financial metrics and staff visualization"
        ],
        patches: []
      },
      {
        version: "0.12.3",
        date: "2025-04-21",
        title: "Complete Phase 1",
        description: "Complete phase 1 of enhanced GST prediction with updated documentation and UI components",
        changes: [
      "Complete phase 1 of enhanced GST prediction with updated documentation and UI components"
        ],
        patches: []
      },
      {
        version: "0.12.4",
        date: "2025-04-21",
        title: "Update Version",
        description: "Chore: Update version history with enhanced GST projection feature",
        changes: [
      "Chore: Update version history with enhanced GST projection feature"
        ],
        patches: []
      },
      {
        version: "0.12.5",
        date: "2025-04-21",
        title: "Initial Implementation Of",
        description: "Initial implementation of enhanced GST projection with invoice-based forecasting",
        changes: [
      "Initial implementation of enhanced GST projection with invoice-based forecasting"
        ],
        patches: []
      },
      {
        version: "0.12.6",
        date: "2025-04-21",
        title: "Add ATO Logo",
        description: "Add ATO logo for tax components and improve UI with 'NEW' badges",
        changes: [
      "Add ATO logo for tax components and improve UI with 'NEW' badges"
        ],
        patches: []
      },
      {
        version: "0.12.7",
        date: "2025-04-24",
        title: "Add Comprehensive Version",
        description: "Add comprehensive version history with automated update script",
        changes: [
      "Add comprehensive version history with automated update script"
        ],
        patches: []
      }
    ]
  },
  {
    version: "0.11.3",
    date: "2025-04-21",
    title: "Enhanced GST Projection & Robust Expense Tracking",
    description: "Improved GST projection system with robust expense identification",
    changes: [
      "Initial implementation of enhanced GST projection with invoice-based forecasting",
      "Added integration with invoice projection for more accurate GST calculations",
      "Updated data models to support enhanced GST prediction",
      "Implemented robust expense identification with improved Xero source checks",
      "Enhanced expense tracking with more reliable duplicate detection",
      "Added ATO logo for tax components and improved UI with 'NEW' badges",
      "Improved documentation for GST integration features",
      "Add comprehensive version history with automated update script",
      "Enhance expense summary with quarter info and fix typescript errors",
      "Add quarter utilities and enhance GST badges with quarter info",
      "Add badge components for predicted and accrued values",
      "Add metadata support and Xero source constants for consistent expense tracking",
      "Replace Xero blue checkmark with official Xero logo",
      "Enhanced Xero GST integration with accrued and predicted amounts",
      "Enhance tax calendar with BAS prediction and improve hover interactions",
      "Remove debug logging statements from expense components",
      "Improve tooltip positioning to follow cursor precisely"
    ],
    minorReleases: [
      {
        version: "0.11.10",
        date: "2025-04-21",
        title: "Robust Expense Identification",
        description: "Improved expense identification with robust Xero source checks",
        changes: [
          "Implemented robust expense identification with improved Xero source checks",
          "Enhanced expense tracking with more reliable duplicate detection",
          "Improved isXeroExpense and isGSTExpense utility functions",
          "Updated documentation with expense identification details",
          "Removed error-prone string matching in favor of source field checks"
        ],
        patches: []
      },
      {
        version: "0.11.9",
        date: "2025-04-21",
        title: "Enhanced GST Projection",
        description: "Initial implementation of enhanced GST projection with invoice-based forecasting",
        changes: [
          "Initial implementation of enhanced GST projection with invoice-based forecasting",
          "Added integration with invoice projection for more accurate GST calculations",
          "Updated data models to support enhanced GST prediction",
          "Added ATO logo for tax components and improved UI with 'NEW' badges"
        ],
        patches: []
      },
      {
        version: "0.11.1",
        date: "2025-04-21",
        title: "Add Comprehensive Version",
        description: "Add comprehensive version history with automated update script",
        changes: [
      "Add comprehensive version history with automated update script"
        ],
        patches: []
      },
      {
        version: "0.11.2",
        date: "2025-04-21",
        title: "Enhance Expense Summary",
        description: "Enhance expense summary with quarter info and fix typescript errors",
        changes: [
      "Enhance expense summary with quarter info and fix typescript errors"
        ],
        patches: []
      },
      {
        version: "0.11.3",
        date: "2025-04-21",
        title: "Add Quarter Utilities",
        description: "Add quarter utilities and enhance GST badges with quarter info",
        changes: [
      "Add quarter utilities and enhance GST badges with quarter info"
        ],
        patches: []
      },
      {
        version: "0.11.4",
        date: "2025-04-21",
        title: "Add Badge Components",
        description: "Add badge components for predicted and accrued values",
        changes: [
      "Add badge components for predicted and accrued values"
        ],
        patches: []
      },
      {
        version: "0.11.5",
        date: "2025-04-21",
        title: "Add Metadata Support",
        description: "Add metadata support and Xero source constants for consistent expense tracking",
        changes: [
      "Add metadata support and Xero source constants for consistent expense tracking"
        ],
        patches: []
      },
      {
        version: "0.11.6",
        date: "2025-04-21",
        title: "Replace Xero Blue",
        description: "Replace Xero blue checkmark with official Xero logo",
        changes: [
      "Replace Xero blue checkmark with official Xero logo"
        ],
        patches: []
      },
      {
        version: "0.11.7",
        date: "2025-04-21",
        title: "Enhanced Xero GST",
        description: "Enhanced Xero GST integration with accrued and predicted amounts",
        changes: [
      "Enhanced Xero GST integration with accrued and predicted amounts"
        ],
        patches: []
      },
      {
        version: "0.11.8",
        date: "2025-04-21",
        title: "Enhance Tax Calendar",
        description: "Enhance tax calendar with BAS prediction and improve hover interactions",
        changes: [
      "Enhance tax calendar with BAS prediction and improve hover interactions"
        ],
        patches: []
      }
    ]
  },
  // Major Release: GST & Quarter Utils (v0.10)
  {
    version: "0.10.0",
    date: "2025-04-20", // Based on actual git commit date
    title: "GST & Quarter Utilities",
    description: "Enhanced financial tracking with GST badges, quarter information, and tax calendar visualization",
    changes: [
      "Complete GST integration with Xero Balance Sheet data",
      "Added quarter utilities for consistent date handling across the app",
      "Enhanced expense summary with quarter information and identifiers",
      "Added badges for accrued and predicted GST values",
      "Added tax calendar visualization for BAS and PAYG payments",
      "Improved tooltip positioning and fixed TypeScript errors"
    ],
    minorReleases: [
      {
        version: "0.10.1",
        date: "2025-04-20",
        title: "Badge Components",
        description: "Added badge components for predicted and accrued values",
        changes: [
          "Added badges to GST expenses showing accrued or predicted status",
          "Added visual indicators for quarter information",
          "Enhanced user experience with visual status indicators"
        ],
        patches: [
          {
            version: "********",
            date: "2025-04-20",
            title: "Typescript Fixes",
            description: "Fixed TypeScript errors in expense components",
            changes: [
              "Fixed TypeScript errors in expense components",
              "Removed debug logging statements from expense components"
            ]
          }
        ]
      },
      {
        version: "0.10.2",
        date: "2025-04-20",
        title: "Quarter Utilities",
        description: "Added quarter utilities for consistent date handling",
        changes: [
          "Added quarter utilities for consistent date handling across the app",
          "Enhanced expense summary with quarter information and identifiers",
          "Improved date handling for GST/BAS quarters"
        ],
        patches: []
      }
    ]
  },

  // Major Release: Xero Integration (v0.9)
  {
    version: "0.9.0",
    date: "2025-04-19", // Based on actual git commit date
    title: "Xero Integration",
    description: "Comprehensive Xero integration for bills, expenses, and financial data",
    changes: [
      "Implemented Xero bills integration for expense tracking",
      "Added Xero additional expenses integration",
      "Enhanced Xero UI components with consistent styling",
      "Improved expense management and categorization",
      "Added bill duplication prevention mechanisms",
      "Fixed Xero API integration errors and permission handling"
    ],
    minorReleases: [
      {
        version: "0.9.1",
        date: "2025-04-19",
        title: "Styling & UI",
        description: "Enhanced Xero pages with consistent Card styling",
        changes: [
          "Enhanced Xero pages with consistent Card styling",
          "Improved visual hierarchy in all Xero components",
          "Updated Xero expense components styling and structure"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-19",
            title: "Card Styles",
            description: "Updated Xero components with consistent Card styling",
            changes: [
              "Enhanced UI with consistent Card styling",
              "Updated remaining Xero components with consistent Card styling",
              "Improved visual hierarchy for better readability"
            ]
          }
        ]
      },
      {
        version: "0.9.2",
        date: "2025-04-18",
        title: "API & Data",
        description: "Updated Xero API client implementation",
        changes: [
          "Updated Xero API client implementation",
          "Fixed permission checking in Xero API calls",
          "Improved API request reliability and response parsing"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-18",
            title: "Payroll Integration",
            description: "Enhanced payroll integration with real Xero API data",
            changes: [
              "Replaced mock payroll data with real Xero API data",
              "Fixed date handling in payroll integration",
              "Provided realistic sample data when Xero API is unavailable"
            ]
          }
        ]
      }
    ]
  },

  // Major Release: Xero Bills & Expenses (v0.8)
  {
    version: "0.8.0",
    date: "2025-04-18", // Based on actual git commit date
    title: "Xero Bills & Expenses",
    description: "Integration of Xero bills and additional expenses",
    changes: [
      "Implemented Xero bills integration for expense tracking",
      "Added Xero additional expenses integration",
      "Created frontend components for viewing and managing Xero data",
      "Added bill duplication prevention mechanisms",
      "Fixed Xero API integration errors and permission handling",
      "Enhanced Xero payroll integration with improved error handling"
    ],
    minorReleases: [
      {
        version: "0.8.1",
        date: "2025-04-18",
        title: "Bills Integration",
        description: "Implemented Xero bills integration for expense tracking",
        changes: [
          "Implemented Xero bills integration for expense tracking",
          "Created dedicated BillService for handling Xero bills data",
          "Added UI components for viewing and filtering bills"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-18",
            title: "Date Handling",
            description: "Fixed date handling issue when converting Xero bills to expenses",
            changes: [
              "Fixed date handling issue when converting Xero bills to expenses",
              "Implemented selective import of bills to custom expenses",
              "Added visual indicators for bill status and import progress"
            ]
          }
        ]
      },
      {
        version: "0.8.2",
        date: "2025-04-18",
        title: "Additional Expenses",
        description: "Added Xero additional expenses integration",
        changes: [
          "Added Xero additional expenses integration",
          "Implemented new API endpoints for additional expenses",
          "Enhanced expense handling with repository improvements"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-18",
            title: "API Error Handling",
            description: "Fixed Xero API integration errors",
            changes: [
              "Fixed Xero API integration errors and permission handling",
              "Improved handling of missing API permissions",
              "Enhanced Xero data caching for better performance"
            ]
          }
        ]
      }
    ]
  },

  // Major Release: Cashflow Chart (v0.7)
  {
    version: "0.7.0",
    date: "2025-04-16", // Based on actual git commit date
    title: "Cashflow Chart Enhancements",
    description: "Enhanced cashflow chart with transaction labels and extended timeframes",
    changes: [
      "Added transaction labels to cashflow chart for significant transactions",
      "Added toggle for cashflow chart transaction labels",
      "Enhanced tooltip display with client and project information",
      "Added 120, 150, 180, and 210 day timeframe options",
      "Improved transaction filters UI behavior",
      "Added project and client names to cashflow chart tooltips"
    ],
    minorReleases: [
      {
        version: "0.7.1",
        date: "2025-04-16",
        title: "Transaction Labels",
        description: "Added transaction labels to cashflow chart",
        changes: [
          "Added transaction labels to cashflow chart for significant transactions",
          "Added toggle for cashflow chart transaction labels",
          "Enhanced tooltip display with client and project information"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-14",
            title: "Label Positioning",
            description: "Improved label positioning to prevent overlap",
            changes: [
              "Increased label height variation to prevent overlap in cashflow chart",
              "Added project name in cashflow chart labels for income transactions",
              "Improved tooltip display with more detailed transaction information"
            ]
          }
        ]
      },
      {
        version: "0.7.2",
        date: "2025-04-14",
        title: "Extended Timeframes",
        description: "Added extended timeframe options",
        changes: [
          "Added 120, 150, 180, and 210 day timeframe options",
          "Improved transaction filters UI behavior",
          "Enhanced tooltip display with client and project information"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-14",
            title: "Tooltip Enhancements",
            description: "Enhanced tooltip display in cashflow chart",
            changes: [
              "Enhanced tooltip display with client name below project name",
              "Improved label visibility and readability",
              "Fixed minor visual inconsistencies in transaction display"
            ]
          }
        ]
      }
    ]
  },

  // Major Release: Estimates Export (v0.6)
  {
    version: "0.6.0",
    date: "2025-04-13", // Based on actual git commit date
    title: "Estimates Export & Discounts",
    description: "Added CSV export and discount functionality to estimates",
    changes: [
      "Implemented CSV export functionality for estimates",
      "Added discount functionality with bidirectional inputs",
      "Enhanced discount row alignment and display in BudgetSummary",
      "Added discount field support in estimate drafts repository",
      "Added database migration for discount fields with backward compatibility",
      "Updated estimate notes with comprehensive terms and conditions"
    ],
    minorReleases: [
      {
        version: "0.6.1",
        date: "2025-04-13",
        title: "CSV Export",
        description: "Implemented CSV export functionality for estimates",
        changes: [
          "Implemented CSV export functionality for estimates",
          "Updated documentation to include CSV export feature",
          "Added export utilities for formatting and data preparation"
        ],
        patches: []
      },
      {
        version: "0.6.2",
        date: "2025-04-13",
        title: "Discount Functionality",
        description: "Added discount functionality to estimates",
        changes: [
          "Added discount functionality with bidirectional inputs",
          "Enhanced discount row alignment and display in BudgetSummary",
          "Added discount field support in estimate drafts repository"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-13",
            title: "Database Support",
            description: "Added database migration for discount fields",
            changes: [
              "Added database migration for discount fields with backward compatibility",
              "Updated estimate notes with comprehensive terms and conditions",
              "Fixed taxed flag on all line items when publishing estimates to Harvest"
            ]
          }
        ]
      }
    ]
  },

  // Major Release: Weekly Effort Histogram (v0.5)
  {
    version: "0.5.0",
    date: "2025-04-11", // Based on actual git commit date
    title: "Weekly Effort Histogram",
    description: "Implementation of Weekly Effort Histogram for team utilization visualization",
    changes: [
      "Implemented weekly effort histogram in Team Utilisation section",
      "Enhanced histogram visualization with interactive features",
      "Added dark mode support to visualization",
      "Improved tooltip display and positioning",
      "Fixed layout issues and column alignment",
      "Extracted major functional sections from EstimateTable"
    ],
    minorReleases: [
      {
        version: "0.5.1",
        date: "2025-04-11",
        title: "Histogram Implementation",
        description: "Implemented weekly effort histogram in Team Utilisation section",
        changes: [
          "Implemented weekly effort histogram in Team Utilisation section",
          "Extracted WeeklyEffortHistogram component from EstimateTable",
          "Enhanced histogram visualization with interactive features"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-11",
            title: "Visual Improvements",
            description: "Enhanced visual display of Weekly Effort Histogram",
            changes: [
              "Refined histogram visualization aesthetics",
              "Repositioned Weekly Effort Histogram x-axis labels below chart",
              "Enhanced Weekly Effort Histogram with modern UI design"
            ]
          }
        ]
      },
      {
        version: "0.5.2",
        date: "2025-04-11",
        title: "Layout & Dark Mode",
        description: "Added dark mode support and fixed layout issues",
        changes: [
          "Added dark mode support to visualization",
          "Fixed layout issues in Weekly Effort Histogram",
          "Restored individual day boxes in Weekly Effort Histogram"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-11",
            title: "Tooltip & Spacing",
            description: "Improved tooltip display and chart spacing",
            changes: [
              "Fixed tooltip positioning and improved chart spacing",
              "Aligned columns with x-axis labels and fixed grid spillover",
              "Aligned chart to gray container box boundaries"
            ]
          }
        ]
      }
    ]
  },

  // Major Release: Time Allocation (v0.4)
  {
    version: "0.4.0",
    date: "2025-04-10", // Based on actual git commit date
    title: "Time Allocation & Sharing",
    description: "Enhanced Time Allocation table and implemented sharing functionality",
    changes: [
      "Optimized Time Allocation table layout with freeze panes",
      "Added fullscreen mode to Time Allocation table",
      "Improved floating panel layout and positioning",
      "Fixed sharing functionality for estimate links",
      "Improved share user experience to work around authentication issues",
      "Simplified Time Allocation table with better sticky columns"
    ],
    minorReleases: [
      {
        version: "0.4.1",
        date: "2025-04-10",
        title: "Table Improvements",
        description: "Enhanced Time Allocation table with freeze panes and fullscreen mode",
        changes: [
          "Optimized Time Allocation table layout with freeze panes",
          "Added fullscreen mode to Time Allocation table",
          "Removed instructional text from Time Allocation table"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-10",
            title: "Sticky Columns",
            description: "Ensured consistent column widths and fixed sticky columns",
            changes: [
              "Ensured consistent column widths and fix sticky columns stacking",
              "Simplified Time Allocation table with better sticky columns",
              "Fixed allocation input visibility in light mode"
            ]
          }
        ]
      },
      {
        version: "0.4.2",
        date: "2025-04-10",
        title: "Share Functionality",
        description: "Fixed sharing functionality for estimate links",
        changes: [
          "Fixed sharing functionality for estimate links",
          "Improved share functionality with better error handling and sequencing",
          "Improved share user experience to work around authentication issues"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-10",
            title: "Authentication Fixes",
            description: "Fixed authentication issues with share functionality",
            changes: [
              "Fixed authentication issues with share functionality",
              "Addressed clipboard issues with share functionality",
              "Simplified share functionality by disabling share button until draft exists"
            ]
          }
        ]
      }
    ]
  },

  // Major Release: Floating UI (v0.3)
  {
    version: "0.3.0",
    date: "2025-04-09", // Based on actual git commit date
    title: "Floating UI & Rebranding",
    description: "Implemented floating UI elements and rebranded to Upstream",
    changes: [
      "Implemented floating actions panel for Estimates page",
      "Enhanced floating panels with improved animations and mobile responsiveness",
      "Simplified financial summary panel and improved layout",
      "Anchored floating panels to main content area",
      "Added dynamic header with client and project info",
      "Rebranded from Onbord to 'Upstream by onbord'"
    ],
    minorReleases: [
      {
        version: "0.3.1",
        date: "2025-04-09",
        title: "Floating Panels",
        description: "Implemented floating panels for better user experience",
        changes: [
          "Implemented floating actions panel for Estimates page",
          "Enhanced floating panels with improved animations and mobile responsiveness",
          "Simplified financial summary panel and improved layout"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-09",
            title: "Panel Positioning",
            description: "Improved floating panel positioning and layout",
            changes: [
              "Anchored floating panels to main content area",
              "Properly aligned floating panels with main content container",
              "Positioned floating panels to the right of main content"
            ]
          },
          {
            version: "*******",
            date: "2025-04-09",
            title: "Panel Spacing",
            description: "Refined spacing between floating panels",
            changes: [
              "Reduced spacing between floating panels to match main content",
              "Corrected spacing between floating panels to prevent overlap",
              "Removed shadows and repositioned help icon"
            ]
          }
        ]
      },
      {
        version: "0.3.2",
        date: "2025-04-09",
        title: "Rebranding",
        description: "Rebranded from Onbord to Upstream",
        changes: [
          "Rebranded from Onbord to 'Upstream by onbord'",
          "Added dynamic header with client and project info",
          "Repositioned dynamic header below main title"
        ],
        patches: []
      }
    ]
  },

  // Major Release: Estimate Drafts (v0.2)
  {
    version: "0.2.0",
    date: "2025-04-08", // Based on actual git commit date
    title: "Estimate Drafts & State Management",
    description: "Enhanced estimate functionality with improved state management",
    changes: [
      "Changed estimate drafts to be visible to all users",
      "Restructured Estimate feature with custom hooks",
      "Enhanced estimate saving with auto-draft functionality",
      "Improved UI with help elements and Australian spelling",
      "Added placeholder staff functionality to Estimate feature",
      "Improved placeholder staff UI with integrated card approach"
    ],
    minorReleases: [
      {
        version: "0.2.1",
        date: "2025-04-08",
        title: "Shared Drafts",
        description: "Made estimate drafts visible to all users",
        changes: [
          "Changed estimate drafts to be visible to all users",
          "Updated estimate implementation documentation",
          "Clarified that draft visibility and authorization are intentional design choices"
        ],
        patches: []
      },
      {
        version: "0.2.2",
        date: "2025-04-07",
        title: "Custom Hooks",
        description: "Restructured Estimate feature with custom hooks",
        changes: [
          "Restructured Estimate feature with custom hooks",
          "Enhanced estimate saving with auto-draft functionality",
          "Separated estimate hooks for better separation of concerns"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-07",
            title: "UI Improvements",
            description: "Enhanced estimate UI with help elements and Australian spelling",
            changes: [
              "Enhanced estimate UI with help elements and Australian spelling",
              "Updated user-facing documentation with clearer save vs publish terminology",
              "Renamed 'Save to Harvest' to 'Publish to Harvest' for clarity"
            ]
          }
        ]
      },
      {
        version: "0.2.3",
        date: "2025-04-07",
        title: "Staff Management",
        description: "Added placeholder staff functionality to Estimate feature",
        changes: [
          "Added placeholder staff functionality to Estimate feature",
          "Improved placeholder staff UI with integrated card approach",
          "Removed space reservation for message area when empty"
        ],
        patches: []
      }
    ]
  },

  // Major Release: Estimate Creation (v0.1)
  {
    version: "0.1.0",
    date: "2025-04-06", // Based on actual git commit date
    title: "Estimate Creation & Budget Summary",
    description: "Enhanced Estimate creation with financial metrics and budget summary",
    changes: [
      "Enhanced Estimate creation UX and allocation interface",
      "Added GST to estimates for Australian businesses",
      "Enhanced estimates with Budget Summary table",
      "Improved estimate UI with better layout and styling",
      "Consolidated estimate creation into single-page interface",
      "Implemented Save Draft functionality with SQLite storage"
    ],
    minorReleases: [
      {
        version: "0.1.1",
        date: "2025-04-06",
        title: "UX & Interface",
        description: "Enhanced Estimate creation UX and allocation interface",
        changes: [
          "Enhanced Estimate creation UX and allocation interface",
          "Improved estimate UI with better layout and styling",
          "Consolidated estimate creation into single-page interface"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-06",
            title: "Space Optimization",
            description: "Optimized horizontal space in Weekly Allocation table",
            changes: [
              "Optimized horizontal space in Weekly Allocation table",
              "Reorganized Estimate UI for improved horizontal space",
              "Improved Estimate Settings panel layout"
            ]
          },
          {
            version: "*******",
            date: "2025-04-06",
            title: "Action Buttons",
            description: "Redesigned Estimate Settings layout with external buttons",
            changes: [
              "Redesigned Estimate Settings layout with external buttons",
              "Placed action buttons in their own styled box",
              "Fixed client selection bug in searchable dropdown"
            ]
          }
        ]
      },
      {
        version: "0.1.2",
        date: "2025-04-06",
        title: "GST & Budget Summary",
        description: "Added GST to estimates and Budget Summary table",
        changes: [
          "Added GST to estimates for Australian businesses",
          "Enhanced estimates with Budget Summary table",
          "Improved Budget Summary with better structure"
        ],
        patches: []
      },
      {
        version: "0.1.3",
        date: "2025-04-06",
        title: "Draft Storage",
        description: "Implemented Save Draft functionality with SQLite storage",
        changes: [
          "Implemented Save Draft functionality with SQLite storage",
          "Updated TypeScript interfaces for draft estimates",
          "Removed mock data mode for draft estimates in preview"
        ],
        patches: [
          {
            version: "*******",
            date: "2025-04-06",
            title: "Documentation Updates",
            description: "Updated documentation for Estimates feature",
            changes: [
              "Updated documentation for Estimates feature",
              "Created comprehensive implementation documentation",
              "Fixed TypeScript interfaces for draft estimates"
            ]
          }
        ]
      }
    ]
  },

  // Major Release: Smart Forecast (v0.0.5)
  {
    version: "0.0.5",
    date: "2025-03-15", // Based on actual git commit date
    title: "Smart Forecast & Transaction Handling",
    description: "Enhanced Smart Forecast functionality and transaction display",
    changes: [
      "Fixed future project handling in Smart Forecast",
      "Improved Smart Forecast reliability and separate invoice handling",
      "Enhanced transaction table with improved invoice highlighting",
      "Added Harvest invoice links to transaction IDs",
      "Improved projected income styling and filtering",
      "Fixed projected income showing archived projects"
    ],
    minorReleases: [
      {
        version: "*******",
        date: "2025-03-15",
        title: "Project Handling",
        description: "Fixed future project handling in Smart Forecast",
        changes: [
          "Fixed Smart Forecast double counting of income",
          "Fixed future project handling in Smart Forecast",
          "Fixed invoice scheduling for future projects in Smart Forecast"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-15",
            title: "Documentation Updates",
            description: "Updated documentation for future project support",
            changes: [
              "Updated documentation for future project support in Smart Forecast",
              "Clarified invoice scheduling logic for future projects",
              "Provided comprehensive explanation of future income projection"
            ]
          }
        ]
      },
      {
        version: "*******",
        date: "2025-03-15",
        title: "Transaction Improvements",
        description: "Enhanced transaction table with improved filtering and display",
        changes: [
          "Enhanced transaction table with improved invoice highlighting",
          "Added Harvest invoice links to transaction IDs",
          "Improved transaction display and filter organization"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-15",
            title: "Income Filtering",
            description: "Improved projected income styling and filtering",
            changes: [
              "Improved projected income styling and filtering",
              "Fixed invoice settings not saving for certain projects",
              "Fixed projected income showing archived projects"
            ]
          }
        ]
      },
      {
        version: "*******",
        date: "2025-03-15",
        title: "Expense Handling",
        description: "Improved expenses handling and UI organization",
        changes: [
          "Removed fixed monthly expenses functionality",
          "Updated documentation and UI to remove fixed expenses",
          "Fixed cashflow controller to work with updated service"
        ],
        patches: []
      }
    ]
  },

  // Major Release: Smart Forecast Beta (v0.0.4)
  {
    version: "0.0.4",
    date: "2025-03-12", // Based on actual git commit date
    title: "Smart Forecast Beta & Documentation",
    description: "Enhanced projection system (Smart Forecast) and comprehensive documentation",
    changes: [
      "Renamed Enhanced Projection System to Smart Forecast (Beta)",
      "Added Smart Forecast status indicator to the projection page",
      "Improved projection system UI and reliability",
      "Added help documentation with improved accessibility",
      "Fixed session handling for preview deployments",
      "Added project status filter to ProjectInvoiceSettings"
    ],
    minorReleases: [
      {
        version: "*******",
        date: "2025-03-12",
        title: "Smart Forecast UI",
        description: "Improved Smart Forecast UI and indicators",
        changes: [
          "Renamed Enhanced Projection System to Smart Forecast (Beta)",
          "Added Smart Forecast status indicator to the projection page",
          "Simplified forecast indicator by removing explanatory text"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-12",
            title: "Toggle Functionality",
            description: "Fixed Smart Forecast toggle functionality",
            changes: [
              "Fixed Smart Forecast toggle functionality",
              "Improved toggle UI for better user experience",
              "Added clear visual indicator of Smart Forecast status"
            ]
          }
        ]
      },
      {
        version: "*******",
        date: "2025-03-12",
        title: "Tables & Filters",
        description: "Enhanced projection system tables and filters",
        changes: [
          "Improved tables in projection system settings",
          "Added collapsible filter accordions to Projection System tables",
          "Added project start date, end date, and status to ProjectInvoiceSettings table"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-12",
            title: "Project Settings",
            description: "Fixed ProjectInvoiceSettings table layout",
            changes: [
              "Fixed ProjectInvoiceSettings table layout using HTML tables",
              "Added project status filter to ProjectInvoiceSettings",
              "Fixed issue with Unknown project/client in Projected Income table"
            ]
          }
        ]
      },
      {
        version: "*******",
        date: "2025-03-12",
        title: "Help Documentation",
        description: "Added help documentation with improved accessibility",
        changes: [
          "Added help documentation with improved accessibility",
          "Updated documentation for enhanced projection system",
          "Renamed Enhanced Projection System to Smart Forecast (Beta) in all documentation"
        ],
        patches: []
      }
    ]
  },

  // Major Release: Preview Deployment Fixes (v0.0.3)
  {
    version: "0.0.3",
    date: "2025-03-12", // Based on actual git commit date
    title: "Authentication & Preview Fixes",
    description: "Fixed authentication and deployment issues for preview environments",
    changes: [
      "Fixed session handling for preview deployments",
      "Fixed authentication in production preview deployment",
      "Fixed session cookies for preview deployments",
      "Improved frontend authentication for preview deployments",
      "Fixed TypeScript errors in cookie options",
      "Fixed API URL for preview deployments"
    ],
    minorReleases: [
      {
        version: "*******",
        date: "2025-03-12",
        title: "Session Handling",
        description: "Fixed session handling for preview deployments",
        changes: [
          "Fixed session handling for preview deployments",
          "Fixed authentication in production preview deployment",
          "Fixed session cookies for preview deployments"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-12",
            title: "Cookie Options",
            description: "Fixed TypeScript errors in cookie options",
            changes: [
              "Fixed TypeScript errors in cookie options",
              "Added previewMode to session type",
              "Improved type definitions for authentication flow"
            ]
          }
        ]
      },
      {
        version: "*******",
        date: "2025-03-12",
        title: "OAuth Flow",
        description: "Fixed OAuth flow in Render preview deployments",
        changes: [
          "Fixed OAuth flow in Render preview deployments",
          "Improved frontend authentication for preview deployments",
          "Fixed persistent authentication in preview deployments"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-12",
            title: "API URL Configuration",
            description: "Fixed API URL for preview deployments",
            changes: [
              "Fixed API URL for preview deployments",
              "Improved API endpoint handling for different environments",
              "Enhanced redirect URI handling for preview deployments"
            ]
          }
        ]
      }
    ]
  },

  // Major Release: Enhanced Projection System (v0.0.2)
  {
    version: "0.0.2",
    date: "2025-03-11", // Based on actual git commit date
    title: "Enhanced Projection System",
    description: "Implemented enhanced invoice projection system with improved UI and reliability",
    changes: [
      "Added first phase of improved invoice projection system",
      "Fixed build errors in harvest API implementation",
      "Fixed circular dependency with ProjectedInvoice interface",
      "Fixed TypeScript errors in ProjectInvoiceSettings component",
      "Improved financial projection system UI and reliability",
      "Updated documentation with enhanced projection system details"
    ],
    minorReleases: [
      {
        version: "*******",
        date: "2025-03-11",
        title: "Projection System Phase 1",
        description: "Added first phase of improved invoice projection system",
        changes: [
          "Added first phase of improved invoice projection system",
          "Fixed build errors in harvest API implementation",
          "Fixed circular dependency with ProjectedInvoice interface"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-11",
            title: "TypeScript Fixes",
            description: "Fixed TypeScript errors in ProjectInvoiceSettings component",
            changes: [
              "Fixed TypeScript errors in ProjectInvoiceSettings component",
              "Improved type definitions for projection system",
              "Enhanced type safety in financial calculations"
            ]
          }
        ]
      },
      {
        version: "*******",
        date: "2025-03-11",
        title: "UI & Reliability",
        description: "Improved financial projection system UI and reliability",
        changes: [
          "Improved financial projection system UI and reliability",
          "Made UI more information dense and added filters accordion",
          "Added project status filter to projection settings"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-11",
            title: "Documentation Updates",
            description: "Updated documentation with enhanced projection system details",
            changes: [
              "Updated documentation with enhanced projection system details",
              "Added clear explanation of projection system features",
              "Provided comprehensive user guide for new functionality"
            ]
          }
        ]
      },
      {
        version: "*******",
        date: "2025-03-11",
        title: "Component Cleanup",
        description: "Removed deprecated components and enhanced UI",
        changes: [
          "Removed deprecated CashFlowSummary component",
          "Made UI more information dense and added filters accordion",
          "Removed minimum balance threshold functionality from chart"
        ],
        patches: []
      }
    ]
  },

  // Major Release: Initial Release (v0.0.1)
  {
    version: "0.0.1",
    date: "2025-03-05", // Based on actual git commit date
    title: "Initial Release",
    description: "First release of the financial dashboard with core functionality",
    changes: [
      "Implemented comprehensive dark mode support across all components",
      "Added running balance column to transactions list",
      "Enhanced chart-table interaction with hover functionality",
      "Core cashflow projection functionality",
      "Custom expenses management with SQLite storage",
      "Basic Xero and Harvest integrations"
    ],
    minorReleases: [
      {
        version: "*******",
        date: "2025-03-05",
        title: "Dark Mode Support",
        description: "Implemented comprehensive dark mode support",
        changes: [
          "Added dark mode support with toggle and updated styling",
          "Added dark mode support for charts and tables",
          "Updated summary cards and tables with dark mode text colors"
        ],
        patches: []
      },
      {
        version: "*******",
        date: "2025-03-05",
        title: "Running Balance",
        description: "Added running balance column to transactions list",
        changes: [
          "Added running balance column to transactions list",
          "Added percentage change indicators to Running Balance column",
          "Fixed running balance calculation in TransactionsList component"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-05",
            title: "Chart Interaction",
            description: "Enhanced chart-table interaction with hover functionality",
            changes: [
              "Enhanced chart-table interaction with hover functionality",
              "Standardized percentage change calculation between chart and table",
              "Improved UI layout of projection settings and timeframe controls"
            ]
          }
        ]
      },
      {
        version: "*******",
        date: "2025-03-05",
        title: "Visual Consistency",
        description: "Improved UI layout and color standards",
        changes: [
          "Improved UI layout of projection settings and timeframe controls",
          "Updated expense category and frequency badge colors",
          "Updated transaction legend colors to match pill badges"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-05",
            title: "Color Standards",
            description: "Updated color standards for financial visualization",
            changes: [
              "Changed running balance text color from blue to neutral/red",
              "Updated monthly equivalent text color from blue to neutral",
              "Updated documentation to reflect UI improvements and color conventions"
            ]
          }
        ]
      },
      {
        version: "*******",
        date: "2025-03-05",
        title: "Navigation & UI",
        description: "Improved tab navigation UI with cleaner, modern design",
        changes: [
          "Improved tab navigation UI with cleaner, modern design",
          "Removed icons from tab navigation for cleaner appearance",
          "Updated financial icons with more appropriate symbols"
        ],
        patches: [
          {
            version: "*******.1",
            date: "2025-03-05",
            title: "Expense Form",
            description: "Improved Add New Expense UI with collapsible details pattern",
            changes: [
              "Improved Add New Expense UI with collapsible details pattern",
              "Integrated expense form into table for better UX",
              "Updated Expense Summary icon to financial cash flow SVG"
            ]
          }
        ]
      }
    ]
  }
];

/**
 * Get the latest version update
 * @returns The most recent version update
 */
export const getLatestUpdate = (): MajorRelease => {
  // Find the most recent version by both semver and date
  let latestVersion = versionHistory[0];
  let latestVersionDate = new Date(latestVersion.date);

  // First check major releases
  versionHistory.forEach(major => {
    const majorDate = new Date(major.date);
    // If this major release is more recent
    if (majorDate > latestVersionDate) {
      latestVersion = major;
      latestVersionDate = majorDate;
    }

    // Then check minor releases
    major.minorReleases.forEach(minor => {
      const minorDate = new Date(minor.date);
      // If this minor release is more recent
      if (minorDate > latestVersionDate) {
        // Create a copy of the major release but update with minor release info
        latestVersion = {
          ...major,
          version: minor.version,
          date: minor.date,
          title: minor.title,
          description: minor.description,
          changes: minor.changes
        };
        latestVersionDate = minorDate;
      }

      // Then check patch releases
      minor.patches.forEach(patch => {
        const patchDate = new Date(patch.date);
        // If this patch release is more recent
        if (patchDate > latestVersionDate) {
          // Create a copy of the major release but update with patch release info
          latestVersion = {
            ...major,
            version: patch.version,
            date: patch.date,
            title: patch.title,
            description: patch.description,
            changes: patch.changes
          };
          latestVersionDate = patchDate;
        }
      });
    });
  });

  return latestVersion;
};

/**
 * Parse a version string into an array of numbers for comparison
 */
const parseVersionNumber = (version: string): number[] => {
  return version.split('.').map(Number);
};

/**
 * Compare two version number arrays
 * @returns -1 if v1 < v2, 0 if v1 === v2, 1 if v1 > v2
 */
const compareVersionNumbers = (v1: number[], v2: number[]): number => {
  for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
    const num1 = i < v1.length ? v1[i] : 0;
    const num2 = i < v2.length ? v2[i] : 0;
    if (num1 < num2) return -1;
    if (num1 > num2) return 1;
  }
  return 0;
};

/**
 * Get all major releases
 * @returns Array of major releases
 */
export const getMajorReleases = (): MajorRelease[] => {
  return versionHistory;
};

/**
 * Get all minor releases across all major versions
 * @returns Array of minor releases
 */
export const getAllMinorReleases = (): MinorRelease[] => {
  const minorReleases: MinorRelease[] = [];
  versionHistory.forEach(major => {
    minorReleases.push(...major.minorReleases);
  });
  return minorReleases;
};

/**
 * Get all patch releases across all versions
 * @returns Array of patch releases
 */
export const getAllPatchReleases = (): PatchRelease[] => {
  const patchReleases: PatchRelease[] = [];
  versionHistory.forEach(major => {
    major.minorReleases.forEach(minor => {
      patchReleases.push(...minor.patches);
    });
  });
  return patchReleases;
};

/**
 * Calculate how long ago an update was released
 * @param dateString ISO format date string
 * @returns String representation of time elapsed (e.g., "2 hours ago", "3 days ago")
 */
export const getTimeAgo = (dateString: string): string => {
  const now = new Date();

  // Properly parse the date string to handle YYYY-MM-DD format
  const [year, month, day] = dateString.split('-').map(num => parseInt(num));
  const updateDate = new Date(year, month - 1, day); // Month is 0-indexed in JS Date

  // Check if it's today
  const isToday =
    updateDate.getDate() === now.getDate() &&
    updateDate.getMonth() === now.getMonth() &&
    updateDate.getFullYear() === now.getFullYear();

  if (isToday) {
    return 'just now';
  }

  // Handle other time differences
  const diffMs = now.getTime() - updateDate.getTime();
  const diffSecs = Math.floor(diffMs / 1000);
  const diffMins = Math.floor(diffSecs / 60);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) {
    return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
  } else if (diffHours > 0) {
    return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffMins > 0) {
    return `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
  } else {
    return 'just now';
  }
};