/**
 * Database module
 *
 * This is the main entry point for the database module.
 * It exports the database instance and initialization functions.
 */

import BetterSqlite3 from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { execSync } from 'child_process';
import { initializeSchema } from './schema';

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;

// Database filename - consistent across environments
const DB_FILENAME = 'upstream.db';

/**
 * Get the database path based on the environment
 * @returns The path to the database file
 */
export function getDatabasePath(): string {
  let dataDir = '';

  if (process.env.NODE_ENV === 'production') {
    // In production (Render), use the mounted persistent disk
    dataDir = '/data';
    console.log('Running in production mode, using data directory:', dataDir);
  } else {
    // In development, use local directory
    dataDir = path.resolve(__dirname, '../../data');
  }

  // Ensure the data directory exists
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  // Return the full path to the database file
  return path.join(dataDir, DB_FILENAME);
}

// Database instance
let db: any = null;

/**
 * Get the database instance
 * @returns The database instance
 */
export function getDatabase(): any {
  if (!db) {
    const dbPath = getDatabasePath();
    console.log('Using database path:', dbPath);

    try {
      // Check if we can write to the directory
      const dataDir = path.dirname(dbPath);
      const testFile = path.join(dataDir, '.write-test');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      console.log(`Successfully verified write access to ${dataDir}`);
    } catch (error) {
      console.error(`ERROR: Cannot write to data directory ${path.dirname(dbPath)}:`, error);
      throw error;
    }

    try {
      // Create database connection
      db = new BetterSqlite3(dbPath);
      console.log('Successfully connected to SQLite database');
    } catch (error) {
      console.error(`ERROR: Failed to initialize SQLite database at ${dbPath}:`, error);
      throw error;
    }
  }

  return db;
}

/**
 * Get the current schema version
 * @param db Database instance
 * @returns The current schema version or null if not initialized
 */
export function getSchemaVersion(db: Database): number | null {
  try {
    // Check if the schema_version table exists
    const tableExists = db.prepare(`
      SELECT 1 FROM sqlite_master
      WHERE type='table' AND name='schema_version'
    `).get();

    if (!tableExists) {
      return null;
    }

    // Get the highest version
    const result = db.prepare(`
      SELECT MAX(version) as version FROM schema_version
    `).get() as { version: number } | undefined;

    return result?.version || null;
  } catch (error) {
    console.error('Error getting schema version:', error);
    return null;
  }
}

/**
 * Initialize the database
 * This function should be called once at application startup
 * @returns The initialized database instance
 */
export function initializeDatabase(): any {
  const database = getDatabase();

  // Get the current schema version
  const currentVersion = getSchemaVersion(database);
  console.log(`Current database schema version: ${currentVersion || 'none'}`);

  // Check if we need to initialize the schema
  if (!currentVersion) {
    console.log('Database schema not found. Initializing schema directly...');
    
    try {
      // Run the schema initialization
      initializeSchema(database);
      
      // Insert initial schema version
      const now = new Date().toISOString();
      database.prepare(`
        INSERT INTO schema_version (version, applied_at, description)
        VALUES (1, ?, 'Initial unified data model')
      `).run(now);
      
      console.log('Database schema initialized successfully');
    } catch (error) {
      console.error('Error initializing database schema:', error);
      
      // If direct initialization fails, try the script method
      try {
        console.log('Attempting to run initialization script...');
        const scriptPath = path.join(__dirname, '../../scripts/initialize-fresh-database.js');
        
        if (fs.existsSync(scriptPath)) {
          console.log(`Executing: node ${scriptPath}`);
          execSync(`node ${scriptPath}`, { stdio: 'inherit' });
          console.log('Fresh database initialization complete');
          
          // Reconnect to the database after initialization
          database.close();
          db = null as any;
          return getDatabase();
        } else {
          console.warn('Initialization script not found, schema may be incomplete');
        }
      } catch (scriptError) {
        console.error('Script initialization also failed:', scriptError);
      }
    }
  } else {
    // Database exists, check for missing tables
    console.log('Checking for missing tables...');
    
    // Check for note table specifically (common issue after refactor)
    const noteTableExists = database.prepare(`
      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='note'
    `).get();
    
    if (!noteTableExists) {
      console.log('Note table missing. Creating it...');
      try {
        database.prepare(`
          CREATE TABLE note (
            id TEXT PRIMARY KEY,
            deal_id TEXT,
            content TEXT NOT NULL,
            created_at TEXT NOT NULL,
            created_by TEXT,
            FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
          )
        `).run();
        
        database.prepare(`
          CREATE INDEX idx_note_deal_id ON note(deal_id)
        `).run();
        
        console.log('Note table created successfully');
      } catch (error) {
        console.error('Error creating note table:', error);
      }
    }
  }

  // Return the database instance
  return database;
}

/**
 * Check if the database has been initialized with the complete schema
 * @returns True if the database has been initialized, false otherwise
 */
export function isDatabaseInitialized(): boolean {
  const database = getDatabase();
  return !!getSchemaVersion(database);
}

// Export the database instance for direct use
export default getDatabase();
