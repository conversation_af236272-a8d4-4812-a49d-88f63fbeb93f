/**
 * CRM domain schema
 *
 * This file contains the schema for the CRM domain tables.
 */

import BetterSqlite3 from 'better-sqlite3';

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;

/**
 * Create the CRM domain tables
 * @param db Database instance
 */
export function createCRMTables(db: Database): void {
  console.log('Creating CRM domain tables...');

  // Create deal table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS deal (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      stage TEXT NOT NULL,
      status TEXT, -- Separate from stage for clearer state management

      /* Financial Information */
      value REAL,
      currency TEXT DEFAULT 'AUD',
      probability REAL,

      /* Timing Information */
      expected_close_date TEXT,
      start_date TEXT,
      end_date TEXT,

      /* Invoicing Information */
      invoice_frequency TEXT,
      payment_terms INTEGER,

      /* Relationships */
      company_id TEXT NOT NULL,

      /* External System IDs */
      hubspot_id TEXT UNIQUE,
      harvest_project_id TEXT,

      /* Additional Information */
      description TEXT,
      source TEXT, -- 'HubSpot', 'Manual'
      priority TEXT,
      owner TEXT,
      custom_fields TEXT, -- JSON string for custom fields
      include_in_projections INTEGER DEFAULT 1,
      deleted_at TEXT, -- Soft delete implementation

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT NOT NULL,
      updated_by TEXT NOT NULL,

      FOREIGN KEY (company_id) REFERENCES company(id)
    )
  `).run();

  // Create indexes for deal table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_stage ON deal(stage)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_status ON deal(status)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_expected_close_date ON deal(expected_close_date)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_company_id ON deal(company_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_hubspot_id ON deal(hubspot_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_harvest_project_id ON deal(harvest_project_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_source ON deal(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_deleted_at ON deal(deleted_at)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_include_in_projections ON deal(include_in_projections)`).run();

  // Create contact table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS contact (
      id TEXT PRIMARY KEY,
      first_name TEXT NOT NULL,
      last_name TEXT,
      email TEXT,
      phone TEXT,
      job_title TEXT,

      /* External System IDs */
      hubspot_id TEXT UNIQUE,
      harvest_user_id TEXT UNIQUE,

      /* Source and Status Information */
      source TEXT, -- 'HubSpot', 'Harvest', 'Manual'
      deleted_at TEXT, -- Soft delete implementation

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,
      updated_by TEXT,
      notes TEXT
    )
  `).run();

  // Create indexes for contact table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_name ON contact(first_name, last_name)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_email ON contact(email)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_hubspot_id ON contact(hubspot_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_harvest_user_id ON contact(harvest_user_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_source ON contact(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_deleted_at ON contact(deleted_at)`).run();

  // Create company table with unified fields for Radar companies
  db.prepare(`
    CREATE TABLE IF NOT EXISTS company (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      industry TEXT,
      size TEXT,
      website TEXT,
      address TEXT,
      description TEXT,

      /* External System IDs */
      hubspot_id TEXT UNIQUE,
      harvest_id INTEGER UNIQUE,

      /* Source Information */
      source TEXT, -- 'HubSpot', 'Harvest', 'Manual'

      /* Radar-specific Fields */
      radar_state TEXT, -- 'Strategy', 'Transformation', 'BAU', 'Transition out'
      priority TEXT, -- 'High', 'Medium', 'Low', 'Qualified out'
      current_spend REAL,
      potential_spend REAL,
      last_interaction_date TEXT,

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,
      updated_by TEXT,
      deleted_at TEXT, -- For soft deletes
      
      /* Additional Fields (added via migration) */
      notes TEXT, -- Company-specific notes
      contacts INTEGER -- Number of contacts (for Radar view)
    )
  `).run();

  // Create indexes for company table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_name ON company(name)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_hubspot_id ON company(hubspot_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_harvest_id ON company(harvest_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_source ON company(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_radar_state ON company(radar_state)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_priority ON company(priority)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_deleted_at ON company(deleted_at)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_last_interaction_date ON company(last_interaction_date)`).run();

  // Create deal_estimate junction table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS deal_estimate (
      deal_id TEXT NOT NULL,
      estimate_id TEXT NOT NULL,
      estimate_type TEXT NOT NULL, -- 'draft', 'harvest'

      /* Audit Information */
      linked_at TEXT NOT NULL,
      linked_by TEXT,

      PRIMARY KEY (deal_id, estimate_id, estimate_type),
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
      FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for deal_estimate table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_estimate_deal ON deal_estimate(deal_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_estimate_estimate ON deal_estimate(estimate_id)`).run();

  // Create contact_company junction table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS contact_company (
      contact_id TEXT NOT NULL,
      company_id TEXT NOT NULL,
      role TEXT, -- 'employee', 'contractor', etc.
      is_primary INTEGER DEFAULT 0, -- Boolean: 1 = primary company for this contact

      /* Audit Information */
      created_at TEXT NOT NULL,
      created_by TEXT,

      PRIMARY KEY (contact_id, company_id),
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for contact_company table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_company_contact ON contact_company(contact_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_company_company ON contact_company(company_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_company_role ON contact_company(role)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_company_is_primary ON contact_company(is_primary)`).run();

  // Create note table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS note (
      id TEXT PRIMARY KEY,
      deal_id TEXT,
      content TEXT NOT NULL,
      created_at TEXT NOT NULL,
      created_by TEXT,
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for note table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_note_deal_id ON note(deal_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_note_created_at ON note(created_at)`).run();

  // Create contact_role junction table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS contact_role (
      deal_id TEXT NOT NULL,
      contact_id TEXT NOT NULL,
      role TEXT NOT NULL,
      created_at TEXT NOT NULL,
      created_by TEXT,
      PRIMARY KEY (deal_id, contact_id),
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for contact_role table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_role_deal_id ON contact_role(deal_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_role_contact_id ON contact_role(contact_id)`).run();

  console.log('CRM domain tables created successfully');
}
