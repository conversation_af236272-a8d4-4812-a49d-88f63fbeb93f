/**
 * Integration domain schema
 *
 * This file contains the schema for the integration domain tables.
 */

import BetterSqlite3 from 'better-sqlite3';

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;

/**
 * Create the integration domain tables
 * @param db Database instance
 */
export function createIntegrationTables(db: Database): void {
  console.log('Creating integration domain tables...');

  // Create hubspot_import table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS hubspot_import (
      id TEXT PRIMARY KEY,
      import_date TEXT NOT NULL,
      status TEXT NOT NULL, -- 'pending', 'completed', 'failed'
      deals_count INTEGER,
      contacts_count INTEGER,
      companies_count INTEGER,
      error_message TEXT,
      created_at TEXT NOT NULL
    )
  `).run();

  // Create indexes for hubspot_import table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_hubspot_import_date ON hubspot_import(import_date)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_hubspot_import_status ON hubspot_import(status)`).run();

  // Create hubspot_settings table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS hubspot_settings (
      id TEXT PRIMARY KEY,
      key TEXT NOT NULL UNIQUE,
      value TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    )
  `).run();

  // Create settings table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS settings (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      updated_by TEXT
    )
  `).run();

  // Create harvest_invoice_cache table for performance
  db.prepare(`
    CREATE TABLE IF NOT EXISTS harvest_invoice_cache (
      harvest_client_id INTEGER PRIMARY KEY,
      total_invoiced REAL NOT NULL DEFAULT 0,
      invoice_count INTEGER NOT NULL DEFAULT 0,
      last_updated TEXT NOT NULL,
      created_at TEXT NOT NULL DEFAULT (datetime('now'))
    )
  `).run();

  // Create indexes for harvest_invoice_cache table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_harvest_invoice_cache_updated ON harvest_invoice_cache(last_updated)`).run();

  console.log('Integration domain tables created successfully');
}
