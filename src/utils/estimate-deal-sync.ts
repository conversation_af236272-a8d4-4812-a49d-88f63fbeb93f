import { DealRepository } from '../api/repositories/deal-repository';
import { DealEstimateRepository } from '../api/repositories/relationships/deal-estimate-repository';
import { EstimateDraftsRepository } from '../api/repositories/estimate-drafts-repository';
import { getHarvestService } from '../services/harvest';
import { DataSource } from '../frontend/types/crm-types';

/**
 * Update deals with estimate data
 *
 * This function finds all deals linked to a specific estimate and updates them
 * with the latest data from the estimate.
 *
 * @param estimateId The ID of the estimate
 * @param estimateType The type of estimate ('draft' or 'harvest')
 */
export async function updateDealsFromEstimate(estimateId: string, estimateType: 'draft' | 'harvest'): Promise<void> {
  try {
    // Get the repositories
    const dealRepository = new DealRepository();
    const dealEstimateRepository = new DealEstimateRepository();

    // Find deals linked to this estimate
    const dealRelationships = dealEstimateRepository.getDealsForEstimate(estimateId);
    const dealIds = dealRelationships.map(relationship => relationship.dealId);
    if (dealIds.length === 0) return;

    console.log(`Updating ${dealIds.length} deals linked to ${estimateType} estimate ${estimateId}`);

    // Get estimate data
    let estimateData = null;
    if (estimateType === 'draft') {
      // Get draft estimate data
      const estimateDraftsRepository = new EstimateDraftsRepository();
      estimateData = estimateDraftsRepository.getDraftEstimateById(estimateId);
    } else if (estimateType === 'harvest') {
      // Get Harvest estimate data
      const harvestService = getHarvestService();
      const harvestClient = harvestService.getClient();
      try {
        estimateData = await harvestClient.getEstimate(parseInt(estimateId));
      } catch (err) {
        console.error(`Error fetching Harvest estimate ${estimateId}:`, err);
        return;
      }
    }

    if (!estimateData) {
      console.warn(`No ${estimateType} estimate found with ID: ${estimateId}`);
      return;
    }

    // Update each linked deal
    for (const dealId of dealIds) {
      const deal = dealRepository.getDealById(dealId);
      if (!deal) {
        console.warn(`Deal ${dealId} not found, skipping update`);
        continue;
      }

      // Prepare deal update data
      const dealUpdateData: any = {};

      if (estimateType === 'draft') {
        // Update fields from draft estimate
        if (estimateData.startDate) {
          dealUpdateData.startDate = estimateData.startDate;
        }

        if (estimateData.endDate) {
          dealUpdateData.endDate = estimateData.endDate;
        }

        if (estimateData.invoiceFrequency) {
          dealUpdateData.invoiceFrequency = estimateData.invoiceFrequency;
        }

        if (estimateData.paymentTerms) {
          dealUpdateData.paymentTerms = estimateData.paymentTerms;
        }

        // Update value from totalFees
        if (estimateData.totalFees) {
          dealUpdateData.value = estimateData.totalFees;
          dealUpdateData.currency = 'AUD'; // Default to AUD
        }
      } else if (estimateType === 'harvest') {
        // Update value from Harvest estimate
        if (estimateData.amount) {
          dealUpdateData.value = parseFloat(estimateData.amount);
          dealUpdateData.currency = 'AUD'; // Default to AUD
        }

        // Update end date from Harvest estimate
        let endDate = null;
        if (estimateData.sent_at) {
          endDate = estimateData.sent_at;
        } else if (estimateData.issue_date) {
          // Calculate a date 3 months from the issue date
          try {
            const issueDate = new Date(estimateData.issue_date);
            endDate = new Date(issueDate);
            endDate.setMonth(endDate.getMonth() + 3);
            endDate = endDate.toISOString().split('T')[0];
          } catch (err) {
            console.error('Error calculating end date from issue date:', err);
          }
        }

        if (endDate) {
          dealUpdateData.endDate = endDate;
        }
      }

      // Apply the updates if there are any
      if (Object.keys(dealUpdateData).length > 0) {
        console.log(`Updating deal ${dealId} with ${estimateType} estimate data:`, dealUpdateData);
        dealRepository.updateDeal(dealId, dealUpdateData, 'Estimate' as DataSource);
      } else {
        console.log(`No updates needed for deal ${dealId}`);
      }
    }
  } catch (error) {
    console.error('Error updating deals from estimate:', error);
  }
}
