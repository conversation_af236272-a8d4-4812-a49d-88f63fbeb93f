/**
 * Deal tracking utilities
 *
 * This module provides utilities for tracking deal field ownership and changes.
 */

import { v4 as uuidv4 } from 'uuid';
import db from '../api/services/db-service';
import {
  Deal,
  DealUpdate,
  DataSource,
  FieldOwnership,
  ChangeLogEntry
} from '../frontend/types/crm-types';
import activityLogger from './activity-logger';

// Re-export DataSource type
export { DataSource };

/**
 * Get the owner source for a field
 *
 * @param dealId Deal ID
 * @param fieldName Field name
 * @returns Owner source or null if not found
 */
export function getFieldOwner(dealId: string, fieldName: string): DataSource | null {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='field_ownership'").get();
    if (!tableExists) {
      // Table doesn't exist yet, return null
      return null;
    }

    const result = db.prepare(`
      SELECT owner as ownerSource
      FROM field_ownership
      WHERE entity_type = 'deal' AND entity_id = ? AND field_name = ?
    `).get(dealId, fieldName) as { ownerSource: DataSource } | undefined;

    return result ? result.ownerSource : null;
  } catch (error) {
    console.error(`Error getting field owner for ${fieldName} of deal ${dealId}:`, error);
    return null;
  }
}

/**
 * Get all field ownership records for a deal
 *
 * @param dealId Deal ID
 * @returns Array of field ownership records
 */
export function getDealFieldOwnership(dealId: string): FieldOwnership[] {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='field_ownership'").get();
    if (!tableExists) {
      // Table doesn't exist yet, return empty array
      return [];
    }

    return db.prepare(`
      SELECT
        entity_id as dealId,
        field_name as fieldName,
        owner as ownerSource,
        set_at as lastUpdated
      FROM field_ownership
      WHERE entity_type = 'deal' AND entity_id = ?
    `).all(dealId) as FieldOwnership[];
  } catch (error) {
    console.error(`Error getting field ownership for deal ${dealId}:`, error);
    return [];
  }
}

/**
 * Set the owner source for a field
 *
 * @param dealId Deal ID
 * @param fieldName Field name
 * @param ownerSource Owner source
 * @returns True if successful
 */
export function setFieldOwner(dealId: string, fieldName: string, ownerSource: DataSource): boolean {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='field_ownership'").get();
    if (!tableExists) {
      // Table doesn't exist yet, create it
      db.prepare(`
        CREATE TABLE field_ownership (
          id TEXT PRIMARY KEY,
          entity_type TEXT NOT NULL,
          entity_id TEXT NOT NULL,
          field_name TEXT NOT NULL,
          owner TEXT NOT NULL,
          set_at TEXT NOT NULL,
          set_by TEXT,
          UNIQUE(entity_type, entity_id, field_name)
        )
      `).run();

      // Create indexes
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_field_ownership_entity ON field_ownership(entity_type, entity_id)`).run();
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_field_ownership_owner ON field_ownership(owner)`).run();
    }

    const id = uuidv4();
    const now = new Date().toISOString();

    // Check if the field ownership record already exists
    const existingOwner = getFieldOwner(dealId, fieldName);

    if (existingOwner) {
      // Update existing record
      db.prepare(`
        UPDATE field_ownership
        SET owner = ?, set_at = ?
        WHERE entity_type = 'deal' AND entity_id = ? AND field_name = ?
      `).run(ownerSource, now, dealId, fieldName);
    } else {
      // Insert new record
      db.prepare(`
        INSERT INTO field_ownership (
          id, entity_type, entity_id, field_name, owner, set_at, set_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(id, 'deal', dealId, fieldName, ownerSource, now, 'system');
    }

    return true;
  } catch (error) {
    console.error(`Error setting field owner for ${fieldName} of deal ${dealId}:`, error);
    return false;
  }
}

/**
 * Log a change to a deal field
 *
 * @param dealId Deal ID
 * @param fieldName Field name
 * @param oldValue Old value
 * @param newValue New value
 * @param changeSource Source of the change
 * @param changedBy User or system that made the change
 * @returns True if successful
 */
export function logFieldChange(
  dealId: string,
  fieldName: string,
  oldValue: any,
  newValue: any,
  changeSource: DataSource,
  changedBy?: string
): boolean {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='change_log'").get();
    if (!tableExists) {
      // Table doesn't exist yet, create it
      db.prepare(`
        CREATE TABLE change_log (
          id TEXT PRIMARY KEY,
          entity_type TEXT NOT NULL,
          entity_id TEXT NOT NULL,
          field_name TEXT NOT NULL,
          old_value TEXT,
          new_value TEXT,
          change_source TEXT NOT NULL,
          changed_at TEXT NOT NULL,
          changed_by TEXT
        )
      `).run();

      // Create indexes
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_change_log_entity ON change_log(entity_type, entity_id)`).run();
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_change_log_field ON change_log(field_name)`).run();
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_change_log_date ON change_log(changed_at)`).run();
    }

    const id = uuidv4();
    const now = new Date().toISOString();

    // Convert values to strings for storage
    const oldValueStr = oldValue !== undefined && oldValue !== null ?
      (typeof oldValue === 'object' ? JSON.stringify(oldValue) : String(oldValue)) :
      null;

    const newValueStr = newValue !== undefined && newValue !== null ?
      (typeof newValue === 'object' ? JSON.stringify(newValue) : String(newValue)) :
      null;

    // Insert change log entry
    db.prepare(`
      INSERT INTO change_log (
        id, entity_type, entity_id, field_name, old_value, new_value,
        change_source, changed_at, changed_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      id,
      'deal',
      dealId,
      fieldName,
      oldValueStr,
      newValueStr,
      changeSource,
      now,
      changedBy || null
    );

    return true;
  } catch (error) {
    console.error(`Error logging field change for ${fieldName} of deal ${dealId}:`, error);
    return false;
  }
}

/**
 * Get change history for a deal field
 *
 * @param dealId Deal ID
 * @param fieldName Field name (optional - if not provided, returns changes for all fields)
 * @param limit Maximum number of changes to return (default: 10)
 * @returns Array of change log entries
 */
export function getFieldChangeHistory(
  dealId: string,
  fieldName?: string,
  limit: number = 10
): ChangeLogEntry[] {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='change_log'").get();
    if (!tableExists) {
      // Table doesn't exist yet, return empty array
      return [];
    }

    let query = `
      SELECT
        id, entity_id as dealId, field_name as fieldName,
        old_value as oldValue, new_value as newValue,
        change_source as changeSource, changed_at as changedAt,
        changed_by as changedBy
      FROM change_log
      WHERE entity_type = 'deal' AND entity_id = ?
    `;

    const params: any[] = [dealId];

    if (fieldName) {
      query += ' AND field_name = ?';
      params.push(fieldName);
    }

    query += ' ORDER BY changed_at DESC LIMIT ?';
    params.push(limit);

    return db.prepare(query).all(...params) as ChangeLogEntry[];
  } catch (error) {
    console.error(`Error getting change history for deal ${dealId}:`, error);
    return [];
  }
}

/**
 * Track changes to a deal
 *
 * @param dealId Deal ID
 * @param oldDeal Original deal data
 * @param newData New deal data
 * @param source Source of the changes
 * @param changedBy User or system that made the changes
 * @returns True if successful
 */
export function trackDealChanges(
  dealId: string,
  oldDeal: Deal,
  newData: DealUpdate,
  source: DataSource,
  changedBy?: string
): boolean {
  try {
    // In the fresh unified data model, we don't use field_ownership table anymore
    // Instead, we track ownership through the source field and audit fields

    const changes: Record<string, any> = {};
    let hasChanges = false;

    // Track changes for audit purposes and activity logging
    for (const [key, newValue] of Object.entries(newData)) {
      // Skip undefined values (no change)
      if (newValue === undefined) continue;

      const oldValue = (oldDeal as any)[key];

      // Skip if values are the same
      if (oldValue === newValue) continue;

      // Log the change to the console
      console.log(`Deal ${dealId} field ${key} changed from ${oldValue} to ${newValue} by ${source}`);

      // Track the change for activity logging
      changes[key] = { oldValue, newValue };
      hasChanges = true;

      // Log field change to change_log table
      logFieldChange(dealId, key, oldValue, newValue, source, changedBy);
    }

    // Log activity for deal updates if there are changes
    if (hasChanges) {
      const dealName = newData.name || oldDeal.name || 'Unknown Deal';

      // Check for stage changes specifically
      if (changes.stage) {
        activityLogger.logDealStageChanged(
          dealId,
          dealName,
          changes.stage.oldValue,
          changes.stage.newValue,
          changedBy || source
        ).catch(error => {
          console.error('Error logging deal stage change activity:', error);
        });
      } else {
        // Log general deal update
        activityLogger.logDealUpdated(
          dealId,
          dealName,
          changes,
          changedBy || source
        ).catch(error => {
          console.error('Error logging deal update activity:', error);
        });
      }
    }

    return true;
  } catch (error) {
    console.error(`Error tracking changes for deal ${dealId}:`, error);
    return false;
  }
}

/**
 * Check if a source is allowed to update a field
 *
 * @param dealId Deal ID
 * @param fieldName Field name
 * @param source Source attempting the update
 * @returns True if the source is allowed to update the field
 */
export function canSourceUpdateField(
  dealId: string,
  fieldName: string,
  source: DataSource
): boolean {
  // In the fresh unified data model, we use a simpler approach to field ownership

  // System source can update any field
  if (source === 'System') return true;

  // Manual source can update any field except those from HubSpot
  if (source === 'Manual') {
    // Get the deal to check its source
    try {
      const deal = db.prepare(`
        SELECT source FROM deal WHERE id = ?
      `).get(dealId) as { source: string } | undefined;

      if (!deal) return true; // If deal doesn't exist, allow the update

      // If the deal is from HubSpot, only allow updates to non-HubSpot fields
      if (deal.source === 'HubSpot') {
        // These fields are controlled by HubSpot
        const hubspotFields = ['name', 'stage', 'value', 'probability', 'expectedCloseDate', 'owner'];
        return !hubspotFields.includes(fieldName);
      }

      return true;
    } catch (error) {
      console.error(`Error checking if source ${source} can update field ${fieldName} of deal ${dealId}:`, error);
      return true; // Default to allowing the update
    }
  }

  // HubSpot source can update any field
  if (source === 'HubSpot') return true;

  // Estimate source can update estimate-related fields
  if (source === 'Estimate') {
    const estimateFields = ['startDate', 'endDate', 'invoiceFrequency', 'paymentTerms', 'value'];
    return estimateFields.includes(fieldName);
  }

  // Default to allowing the update
  return true;
}

/**
 * Log deal creation activity
 *
 * @param dealId Deal ID
 * @param dealName Deal name
 * @param createdBy User or system that created the deal
 */
export function logDealCreation(dealId: string, dealName: string, createdBy: string = 'user'): void {
  activityLogger.logDealCreated(dealId, dealName, createdBy).catch(error => {
    console.error('Error logging deal creation activity:', error);
  });
}

/**
 * Initialize field ownership for a new deal
 *
 * @param dealId Deal ID
 * @param hasHubspotId Whether the deal has a HubSpot ID
 * @returns True if successful
 */
export function initializeFieldOwnership(dealId: string, hasHubspotId: boolean): boolean {
  try {
    // In the fresh unified data model, we don't use field_ownership table anymore
    // Instead, we track ownership through the source field and audit fields

    const now = new Date().toISOString();
    const source = hasHubspotId ? 'HubSpot' : 'Manual';

    // Update the deal with the source
    db.prepare(`
      UPDATE deal
      SET source = ?,
          updated_at = ?,
          updated_by = 'system'
      WHERE id = ?
    `).run(source, now, dealId);

    return true;
  } catch (error) {
    console.error(`Error initializing field ownership for deal ${dealId}:`, error);
    return false;
  }
}
