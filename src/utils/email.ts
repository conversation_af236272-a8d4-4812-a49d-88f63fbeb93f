import fetch from 'node-fetch';

/**
 * Simple email utility for sending notifications using Resend API
 * Resend (https://resend.com) offers a simple API for sending emails
 * with a generous free tier (100 emails/day, 3,000/month)
 */

/**
 * Send an email notification using Resend API
 * @param to Recipient email address
 * @param subject Email subject
 * @param text Plain text content
 * @param html HTML content (optional)
 * @returns Promise that resolves when email is sent
 */
export const sendEmail = async (
  to: string,
  subject: string,
  text: string,
  html?: string
): Promise<{ success: boolean; message?: string; error?: string }> => {
  // If API key is not set, just log to console
  if (!process.env.FEEDBACK_EMAIL_API_KEY) {
    console.log('========== EMAIL WOULD BE SENT ==========');
    console.log('To:', to);
    console.log('Subject:', subject);
    console.log('Text:', text);
    console.log('HTML:', html);
    console.log('========================================');

    return {
      success: true,
      message: 'Development mode - email logged to console',
    };
  }

  try {
    // Use Resend API to send email
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FEEDBACK_EMAIL_API_KEY}`,
      },
      body: JSON.stringify({
        from: 'Upstream Feedback <<EMAIL>>',
        to: [to],
        subject,
        text,
        html: html || text,
      }),
    });

    const data = await response.json() as any;

    if (!response.ok) {
      throw new Error(data.message || 'Failed to send email');
    }

    return {
      success: true,
      message: `Email sent: ${data.id}`,
    };
  } catch (error: any) {
    console.error('Error sending email:', error);
    return {
      success: false,
      error: error.message || 'Failed to send email',
    };
  }
};

/**
 * Format feedback as HTML for email
 * @param feedbackData The feedback data
 * @returns HTML string
 */
export const formatFeedbackHtml = (feedbackData: {
  type: string;
  title: string;
  description: string;
  email?: string;
  priority?: string;
  timestamp: string;
}): string => {
  const priorityColor = feedbackData.priority === 'high' || feedbackData.priority === 'critical'
    ? '#e53e3e' // red
    : feedbackData.priority === 'medium'
      ? '#dd6b20' // orange
      : '#3182ce'; // blue for low or undefined

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e2e8f0; border-radius: 5px;">
      <h2 style="color: #4a5568; border-bottom: 1px solid #e2e8f0; padding-bottom: 10px;">
        ${feedbackData.type === 'bug' ? '🐞 Bug Report' : '💡 Feature Request'}
      </h2>

      <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 5px 0; color: #2d3748;">${feedbackData.title}</h3>
        <p style="white-space: pre-wrap; color: #4a5568; line-height: 1.5;">${feedbackData.description}</p>
      </div>

      <div style="background-color: #f7fafc; padding: 15px; border-radius: 5px; margin-top: 20px;">
        <p style="margin: 0 0 5px 0;"><strong>Type:</strong> ${feedbackData.type}</p>
        ${feedbackData.priority ? `<p style="margin: 0 0 5px 0;"><strong>Priority:</strong> <span style="color: ${priorityColor};">${feedbackData.priority}</span></p>` : ''}
        ${feedbackData.email ? `<p style="margin: 0 0 5px 0;"><strong>Contact:</strong> ${feedbackData.email}</p>` : ''}
        <p style="margin: 0; color: #718096; font-size: 0.9em;">Submitted: ${new Date(feedbackData.timestamp).toLocaleString()}</p>
      </div>
    </div>
  `;
};
