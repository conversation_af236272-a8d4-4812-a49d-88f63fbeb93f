/* Radar Component Styles */

/* Radar Page Container */
.radar-page {
  @apply relative;
}

/* Radar Grid Layout */
.radar-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  position: relative;
}

/* Add visual dividers between quadrants */
.radar-grid::before,
.radar-grid::after {
  content: '';
  position: absolute;
  background: linear-gradient(to right, transparent, rgb(*********** / 0.5), transparent);
  pointer-events: none;
}

.radar-grid::before {
  /* Vertical divider */
  width: 1px;
  height: 100%;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.radar-grid::after {
  /* Horizontal divider */
  width: 100%;
  height: 1px;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(to bottom, transparent, rgb(*********** / 0.5), transparent);
}

/* Dark mode dividers */
.dark .radar-grid::before,
.dark .radar-grid::after {
  background: linear-gradient(to right, transparent, rgb(55 65 81 / 0.5), transparent);
}

.dark .radar-grid::after {
  background: linear-gradient(to bottom, transparent, rgb(55 65 81 / 0.5), transparent);
}

/* Radar Quadrant */
.radar-quadrant {
  @apply relative overflow-hidden p-5 rounded-lg border flex flex-col;
  min-height: 400px;
  background: linear-gradient(135deg, rgb(*********** / 0.9), rgb(*********** / 0.9));
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-color: rgb(***********);
}

.dark .radar-quadrant {
  background: linear-gradient(135deg, rgb(31 41 55 / 0.9), rgb(17 24 39 / 0.9));
  border-color: rgb(55 65 81);
}

/* Quadrant hover effect */
.radar-quadrant:hover {
  box-shadow: 0 10px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  transform: translateY(-2px);
}

/* Quadrant drop states */
.radar-quadrant.can-drop {
  @apply border-blue-400 dark:border-blue-500;
  box-shadow: 0 0 0 3px rgb(96 165 250 / 0.2);
}

.radar-quadrant.is-over {
  @apply border-purple-500 bg-purple-50 dark:bg-purple-900/20;
  box-shadow: 0 0 0 3px rgb(168 85 247 / 0.3);
}

/* Quadrant header */
.quadrant-header {
  @apply mb-4 pb-3 border-b border-gray-200 dark:border-gray-700;
}

.quadrant-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quadrant-icon {
  @apply w-5 h-5 text-gray-400 dark:text-gray-500;
}

/* Quadrant badges for spend indicators */
.quadrant-badges {
  @apply flex gap-2 mt-2;
}

.spend-badge {
  @apply text-xs px-2 py-1 rounded-full font-medium;
}

.spend-badge.high {
  @apply bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400;
}

.spend-badge.low {
  @apply bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400;
}

/* Company Grid */
.company-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 1rem;
}

/* Radar Company Card */
.radar-company-card {
  @apply relative overflow-hidden p-4 rounded-lg shadow-sm border cursor-grab;
  background: linear-gradient(135deg, rgb(***********), rgb(***********));
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  border-color: rgb(***********);
}

.dark .radar-company-card {
  background: linear-gradient(135deg, rgb(55 65 81), rgb(31 41 55));
  border-color: rgb(75 85 99);
}

/* Card hover and drag states */
.radar-company-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.radar-company-card.is-dragging {
  opacity: 0.5;
  transform: scale(0.95);
  cursor: grabbing;
}

.radar-company-card:active {
  cursor: grabbing;
}

/* Company Logo */
.company-logo {
  @apply flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center text-white font-semibold text-lg shadow-sm;
  transition: transform 0.2s ease;
}

.radar-company-card:hover .company-logo {
  transform: scale(1.05);
}

/* Company Info Section */
.company-name {
  @apply text-sm font-semibold text-gray-900 dark:text-white truncate;
  line-height: 1.25rem;
}

.company-industry {
  @apply text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5;
  line-height: 1rem;
}


/* Remove Drop Zone */
.remove-drop-zone {
  @apply mt-8 p-8 border-2 border-dashed rounded-xl text-center transition-all duration-200;
  @apply border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/50;
}

.remove-drop-zone.is-over {
  @apply border-red-500 bg-red-50 dark:bg-red-900/20;
  transform: scale(1.02);
}

.remove-drop-zone-content {
  @apply flex items-center justify-center text-gray-500 dark:text-gray-400;
}

.remove-drop-zone.is-over .remove-drop-zone-content {
  @apply text-red-600 dark:text-red-400;
}

/* Add Company Button */
.add-company-button {
  @apply px-4 py-2.5 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg text-sm font-medium;
  @apply hover:from-purple-700 hover:to-purple-800 transition-all duration-200;
  @apply shadow-md hover:shadow-lg transform hover:scale-105;
  @apply flex items-center gap-2;
}

.add-company-button svg {
  @apply w-4 h-4;
}

/* Header Stats */
.radar-stats {
  @apply flex flex-wrap items-center gap-6 text-sm;
}

.stat-item {
  @apply flex flex-col;
}

.stat-label {
  @apply text-xs text-gray-500 dark:text-gray-400 mb-0.5;
}

.stat-value {
  @apply text-base font-semibold text-gray-900 dark:text-white;
}

/* Loading State */
.radar-loading {
  @apply flex flex-col items-center justify-center h-96;
}

.radar-loading-spinner {
  @apply animate-spin rounded-full h-12 w-12 border-4 border-purple-500 border-t-transparent;
}

.radar-loading-text {
  @apply mt-4 text-gray-500 dark:text-gray-400;
}

/* Empty State */
.quadrant-empty {
  @apply flex flex-col items-center justify-center py-12 text-gray-400 dark:text-gray-500;
}

.quadrant-empty-icon {
  @apply w-12 h-12 mb-2 opacity-50;
}

.quadrant-empty-text {
  @apply text-sm italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .radar-page {
    @apply p-4;
  }
  
  .radar-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .radar-grid::before,
  .radar-grid::after {
    display: none;
  }
  
  .company-grid {
    grid-template-columns: 1fr;
  }
  
  .radar-stats {
    @apply text-xs;
  }
  
  .add-company-button {
    @apply px-3 py-1.5 text-sm;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Quadrant-specific colors */
.quadrant-strategy {
  border-top: 3px solid #8b5cf6;
}

.quadrant-transformation {
  border-top: 3px solid #10b981;
}

.quadrant-bau {
  border-top: 3px solid #3b82f6;
}

.quadrant-transition {
  border-top: 3px solid #f59e0b;
}