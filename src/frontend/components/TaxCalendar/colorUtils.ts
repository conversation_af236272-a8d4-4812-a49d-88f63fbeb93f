// Color utilities for the Tax Calendar

// PAYGW colors (monthly)
export const paygwColors = {
  // Light mode colors (default)
  light: [
    'bg-blue-200',     // January - soft blue
    'bg-emerald-200',  // February - soft emerald
    'bg-purple-200',   // March - soft purple
    'bg-teal-200',     // April - soft teal
    'bg-violet-200',   // May - soft violet
    'bg-green-200',    // June - soft green
    'bg-indigo-200',   // July - soft indigo
    'bg-cyan-200',     // August - soft cyan
    'bg-blue-300',     // September - medium blue
    'bg-emerald-300',  // October - medium emerald
    'bg-purple-300',   // November - medium purple
    'bg-teal-300'      // December - medium teal
  ],
  // Dark mode colors (for hover in light mode, default in dark mode)
  dark: [
    'bg-blue-500',     // January - dark blue
    'bg-emerald-500',  // February - dark emerald
    'bg-purple-500',   // March - dark purple
    'bg-teal-500',     // April - dark teal
    'bg-violet-500',   // May - dark violet
    'bg-green-500',    // June - dark green
    'bg-indigo-500',   // July - dark indigo
    'bg-cyan-500',     // August - dark cyan
    'bg-blue-600',     // September - darker blue
    'bg-emerald-600',  // October - darker emerald
    'bg-purple-600',   // November - darker purple
    'bg-teal-600'      // December - darker teal
  ],
  // Payment date colors with border
  payment: {
    light: [
      'bg-blue-200 border-2 border-blue-500',       // January payment
      'bg-emerald-200 border-2 border-emerald-500',  // February payment
      'bg-purple-200 border-2 border-purple-500',    // March payment
      'bg-teal-200 border-2 border-teal-500',        // April payment
      'bg-violet-200 border-2 border-violet-500',    // May payment
      'bg-green-200 border-2 border-green-500',      // June payment
      'bg-indigo-200 border-2 border-indigo-500',    // July payment
      'bg-cyan-200 border-2 border-cyan-500',        // August payment
      'bg-blue-300 border-2 border-blue-600',        // September payment
      'bg-emerald-300 border-2 border-emerald-600',  // October payment
      'bg-purple-300 border-2 border-purple-600',    // November payment
      'bg-teal-300 border-2 border-teal-600'         // December payment
    ],
    dark: [
      'bg-blue-500 border-2 border-blue-300',       // January payment
      'bg-emerald-500 border-2 border-emerald-300',  // February payment
      'bg-purple-500 border-2 border-purple-300',    // March payment
      'bg-teal-500 border-2 border-teal-300',        // April payment
      'bg-violet-500 border-2 border-violet-300',    // May payment
      'bg-green-500 border-2 border-green-300',      // June payment
      'bg-indigo-500 border-2 border-indigo-300',    // July payment
      'bg-cyan-500 border-2 border-cyan-300',        // August payment
      'bg-blue-600 border-2 border-blue-200',        // September payment
      'bg-emerald-600 border-2 border-emerald-200',  // October payment
      'bg-purple-600 border-2 border-purple-200',    // November payment
      'bg-teal-600 border-2 border-teal-200'         // December payment
    ]
  }
};

// GST colors (quarterly)
export const gstColors = {
  // Light mode colors (default)
  light: [
    'bg-yellow-200',  // Q3 (Jan-Mar) - soft yellow
    'bg-red-200',     // Q4 (Apr-Jun) - soft red
    'bg-orange-300',  // Q1 (Jul-Sep) - medium orange
    'bg-pink-300'     // Q2 (Oct-Dec) - medium pink
  ],
  // Dark mode colors (for hover in light mode, default in dark mode)
  dark: [
    'bg-yellow-500',  // Q3 (Jan-Mar) - dark yellow
    'bg-red-500',     // Q4 (Apr-Jun) - dark red
    'bg-orange-600',  // Q1 (Jul-Sep) - dark orange
    'bg-pink-600'     // Q2 (Oct-Dec) - dark pink
  ],
  // Payment date colors with border
  payment: {
    light: [
      'bg-yellow-200 border-2 border-yellow-600',  // Q3 payment
      'bg-red-200 border-2 border-red-600',         // Q4 payment
      'bg-orange-300 border-2 border-orange-600',   // Q1 payment
      'bg-pink-300 border-2 border-pink-600'        // Q2 payment
    ],
    dark: [
      'bg-yellow-500 border-2 border-yellow-300',  // Q3 payment
      'bg-red-500 border-2 border-red-300',         // Q4 payment
      'bg-orange-600 border-2 border-orange-300',   // Q1 payment
      'bg-pink-600 border-2 border-pink-300'        // Q2 payment
    ]
  }
};

// Helper function to get color class based on theme
export const getColorClass = (
  type: 'paygw' | 'gst',
  index: number,
  isPayment: boolean,
  isDarkMode: boolean,
  isHighlighted: boolean
): string => {
  const colorSet = type === 'paygw' ? paygwColors : gstColors;
  
  // Determine which color variant to use based on theme and highlight state
  const variant = isDarkMode 
    ? (isHighlighted ? 'light' : 'dark')
    : (isHighlighted ? 'dark' : 'light');
  
  if (isPayment) {
    return colorSet.payment[variant][index % colorSet.payment[variant].length];
  }
  
  return colorSet[variant][index % colorSet[variant].length];
};
