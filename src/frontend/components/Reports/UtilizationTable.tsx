import { useState } from "react";
import { StaffUtilization } from "../../../services/harvest/time-report-service";
import TaskBreakdownChart from "./TaskBreakdownChart";

interface UtilizationTableProps {
  utilizationData: StaffUtilization[];
  isLoading: boolean;
  loadingType: string;
  fromDate?: string; // Optional date range for cost calculations
  toDate?: string;
  excludeLeave?: boolean; // Whether to exclude leave from capacity calculations
}

/**
 * Helper function to calculate hours for cost calculation based on employment type
 *
 * @param staff The staff member
 * @param fromDate Start date of the period
 * @param toDate End date of the period
 * @returns An object containing hours to use for cost calculation and period capacity
 */
const calculateHoursForCost = (
  staff: StaffUtilization,
  fromDate?: string,
  toDate?: string
): { hoursForCost: number; periodCapacity: number } => {
  let periodCapacity = 0;

  if (staff.isContractor) {
    // Contractors: use actual hours worked
    return { hoursForCost: staff.totalHours, periodCapacity };
  } else {
    // Salaried staff: always use full capacity regardless of actual hours worked
    // First, determine how many weeks the period covers
    const fromDateObj = fromDate ? new Date(fromDate) : null;
    const toDateObj = toDate ? new Date(toDate) : null;

    if (fromDateObj && toDateObj) {
      // Calculate number of weeks in the period
      const daysDifference =
        Math.ceil(
          (toDateObj.getTime() - fromDateObj.getTime()) / (1000 * 60 * 60 * 24)
        ) + 1;
      const weeksDifference = daysDifference / 7;

      // Calculate capacity for the period
      periodCapacity = staff.weeklyCapacity * weeksDifference;

      // For salaried staff, always use full period capacity
      // This reflects that salaried staff are paid their full salary
      // regardless of whether they work more or fewer hours than capacity
      return { hoursForCost: periodCapacity, periodCapacity };
    } else {
      // If we don't have date range, use weekly capacity as fallback
      return {
        hoursForCost: staff.weeklyCapacity,
        periodCapacity: staff.weeklyCapacity,
      };
    }
  }
};

/**
 * A component that displays a table of staff utilization data with task breakdown
 */
const UtilizationTable = ({
  utilizationData,
  isLoading,
  loadingType,
  fromDate,
  toDate,
  excludeLeave = false,
}: UtilizationTableProps) => {
  // State to toggle between compact and detailed view
  const [showBreakdown, setShowBreakdown] = useState(false);

  // Format percentage for display
  const formatPercentage = (value: number) => {
    return `${Math.round(value)}%`;
  };

  // Format hours for display
  const formatHours = (value: number) => {
    return value.toFixed(1);
  };

  // Toggle breakdown view
  const toggleBreakdown = () => {
    setShowBreakdown(!showBreakdown);
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      {/* Toggle button for breakdown view */}
      <div className="flex justify-end p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <button
          onClick={toggleBreakdown}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800 dark:focus:ring-blue-600"
        >
          {showBreakdown ? "Hide Task Breakdown" : "Show Task Breakdown"}
        </button>
      </div>

      {isLoading && loadingType === "harvest" ? (
        <div className="p-6 space-y-4 animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>

          {/* Skeleton table header */}
          <div className="flex bg-gray-100 dark:bg-gray-800 p-3 rounded-t-lg space-x-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[12%]"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[35%]"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[7%]"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[7%]"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[9%]"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[6%]"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[7%]"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[7%]"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[5%]"></div>
          </div>

          {/* Skeleton table rows */}
          {[...Array(5)].map((_, index) => (
            <div
              key={index}
              className="flex items-center bg-white dark:bg-gray-800 p-4 border-b border-gray-200 dark:border-gray-700 space-x-2"
            >
              <div className="flex items-center space-x-2 w-[12%]">
                <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
              </div>
              <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-[35%]"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[7%]"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[7%]"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[9%]"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[6%]"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[7%]"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[7%]"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-[5%]"></div>
            </div>
          ))}
        </div>
      ) : (
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[12%]"
              >
                Staff
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[35%]"
              >
                Utilization
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[7%]"
              >
                Harvest
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[7%]"
              >
                Bill Hrs
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[9%]"
              >
                Bill Amt
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[6%]"
              >
                Rate
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[7%]"
              >
                Cost
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[7%]"
              >
                Margin
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[5%]"
              >
                Hours
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300 w-[5%]"
              >
                Capacity
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
            {utilizationData.length > 0 ? (
              // Sort by utilization (highest to lowest)
              // Use adjusted utilization if excludeLeave is true
              [...utilizationData]
                .sort((a, b) =>
                  excludeLeave
                    ? b.adjustedUtilization - a.adjustedUtilization
                    : b.utilization - a.utilization
                )
                .map((staff) => (
                  <tr
                    key={staff.userId}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {staff.avatarUrl && (
                          <div className="flex-shrink-0 h-10 w-10 mr-3">
                            <img
                              className="h-10 w-10 rounded-full"
                              src={staff.avatarUrl}
                              alt=""
                            />
                          </div>
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {staff.userName}
                          </div>
                          {staff.isContractor && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Contractor
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex flex-col space-y-4">
                        {/* Always show utilization percentage with progress bar in compact view */}
                        {!showBreakdown && (
                          <div className="flex items-center">
                            <div className="w-full bg-gray-200 rounded-full h-5 dark:bg-gray-700 mr-3">
                              {/* Use adjusted utilization if excludeLeave is true */}
                              {(excludeLeave
                                ? staff.adjustedUtilization
                                : staff.utilization) > 0 ? (
                                <div
                                  className={`h-5 rounded-full ${
                                    (excludeLeave
                                      ? staff.adjustedUtilization
                                      : staff.utilization) >= 70
                                      ? "bg-green-500 dark:bg-green-600"
                                      : (excludeLeave
                                          ? staff.adjustedUtilization
                                          : staff.utilization) >= 50
                                      ? "bg-yellow-500 dark:bg-yellow-600"
                                      : "bg-red-500 dark:bg-red-600"
                                  }`}
                                  style={{
                                    width: `${Math.min(
                                      100,
                                      excludeLeave
                                        ? staff.adjustedUtilization
                                        : staff.utilization
                                    )}%`,
                                  }}
                                ></div>
                              ) : (
                                // Full width light red bar for 0% utilization
                                <div className="h-5 rounded-full bg-red-300 dark:bg-red-800 w-full"></div>
                              )}
                            </div>
                            <span className="text-sm font-medium text-gray-900 dark:text-white min-w-[45px]">
                              {formatPercentage(
                                excludeLeave
                                  ? staff.adjustedUtilization
                                  : staff.utilization
                              )}
                            </span>
                          </div>
                        )}

                        {/* Show task breakdown chart in detailed view */}
                        {showBreakdown && (
                          <div>
                            <TaskBreakdownChart
                              taskBreakdown={staff.taskBreakdown}
                              totalHours={staff.totalHours}
                              billableHours={staff.billableHours}
                              weeklyCapacity={staff.weeklyCapacity}
                              utilization={
                                excludeLeave
                                  ? staff.adjustedUtilization
                                  : staff.utilization
                              }
                              periodDays={5} // Using 5 workdays per week for capacity calculation
                              excludeLeave={excludeLeave}
                              leaveHours={staff.leaveHours}
                            />
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatPercentage(staff.harvestUtilization)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatHours(staff.billableHours)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {staff.billableAmount && staff.currency
                        ? new Intl.NumberFormat("en-AU", {
                            style: "currency",
                            currency: staff.currency,
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          }).format(staff.billableAmount)
                        : "-"}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {staff.costRate
                        ? new Intl.NumberFormat("en-AU", {
                            style: "currency",
                            currency: staff.currency || "AUD",
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          }).format(staff.costRate)
                        : "$40"}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {(() => {
                        // Use the actual cost rate from Harvest if available, otherwise use default
                        const DEFAULT_COST_RATE = 40; // Default hourly cost rate in AUD
                        const costRate = staff.costRate || DEFAULT_COST_RATE;

                        // Calculate hours for cost using our helper function
                        const { hoursForCost, periodCapacity } =
                          calculateHoursForCost(staff, fromDate, toDate);

                        const staffCost = hoursForCost * costRate;

                        // Add detailed debugging for cost calculation
                        console.log(`Cost calculation for ${staff.userName}:`, {
                          isContractor: staff.isContractor,
                          costRate: costRate,
                          totalHours: staff.totalHours,
                          weeklyCapacity: staff.weeklyCapacity,
                          periodCapacity: periodCapacity || "N/A",
                          hoursUsedForCost: hoursForCost,
                          costCalculation: `${hoursForCost} hours × $${costRate}/hour = $${staffCost}`,
                          explanation: staff.isContractor
                            ? "CONTRACTOR: Using all actual hours worked for cost calculation"
                            : "SALARIED: Using full period capacity for cost calculation (fixed salary)",
                          actualHoursLessThanCapacity:
                            !staff.isContractor &&
                            staff.totalHours < periodCapacity,
                          actualHoursMoreThanCapacity:
                            !staff.isContractor &&
                            staff.totalHours > periodCapacity,
                          staffCost: staffCost,
                        });

                        return staff.currency
                          ? new Intl.NumberFormat("en-AU", {
                              style: "currency",
                              currency: staff.currency || "AUD",
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0,
                            }).format(staffCost)
                          : new Intl.NumberFormat("en-AU", {
                              style: "currency",
                              currency: "AUD",
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0,
                            }).format(staffCost);
                      })()}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm">
                      {(() => {
                        // Calculate staff cost and profit margin using actual cost rate
                        const DEFAULT_COST_RATE = 40; // Default hourly cost rate in AUD
                        const costRate = staff.costRate || DEFAULT_COST_RATE;

                        // Calculate hours for cost using our helper function
                        const { hoursForCost, periodCapacity } =
                          calculateHoursForCost(staff, fromDate, toDate);

                        const staffCost = hoursForCost * costRate;
                        const billableAmount = staff.billableAmount || 0;
                        const profit = billableAmount - staffCost;
                        const profitMargin =
                          billableAmount > 0
                            ? (profit / billableAmount) * 100
                            : 0;

                        // Determine text color based on profit margin
                        const textColorClass =
                          profit >= 0
                            ? "text-green-600 dark:text-green-400"
                            : "text-red-600 dark:text-red-400";

                        return (
                          <span className={`font-medium ${textColorClass}`}>
                            {Math.round(profitMargin)}%
                            {staff.currency && (
                              <div className={`text-xs mt-1 ${textColorClass}`}>
                                {new Intl.NumberFormat("en-AU", {
                                  style: "currency",
                                  currency: staff.currency || "AUD",
                                  minimumFractionDigits: 0,
                                  maximumFractionDigits: 0,
                                }).format(profit)}
                              </div>
                            )}
                          </span>
                        );
                      })()}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatHours(staff.totalHours)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatHours(staff.weeklyCapacity)}
                    </td>
                  </tr>
                ))
            ) : (
              <tr>
                <td
                  colSpan={10}
                  className="px-4 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                >
                  No utilization data available for the selected time period.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default UtilizationTable;
