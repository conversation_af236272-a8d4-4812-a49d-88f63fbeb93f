import React, { useState, useEffect } from "react";
import { EstimateFormState } from "../../hooks/useEstimateFormState";
import { getLinkedDealsForEstimate } from "../../api/crm";
import DealLinkModal from "../CRM/DealEdit/DealLinkModal";
import LinkedDealModal from "../CRM/DealEdit/LinkedDealModal";
import DealSelectionModal from "../CRM/DealEdit/DealSelectionModal";
import { Deal } from "../../types/crm-types"; // Adjust path as needed
import { format } from "date-fns"; // Import date-fns format

// Constants for dropdown options
const INVOICE_FREQUENCY_OPTIONS = [
  { value: "weekly", label: "Weekly" },
  { value: "biweekly", label: "Every 2 Weeks" },
  { value: "monthly", label: "Monthly" },
  { value: "quarterly", label: "Quarterly" },
  { value: "milestone", label: "Milestone-based" },
  { value: "completion", label: "On Completion" },
];

const PAYMENT_TERMS_OPTIONS = [
  { value: 7, label: "Net 7" },
  { value: 14, label: "Net 14" },
  { value: 20, label: "Net 20" },
  { value: 30, label: "Net 30" },
  { value: 45, label: "Net 45" },
  { value: 60, label: "Net 60" },
];

// Define props based on what the form needs from the useEstimateFormState hook
interface EstimateConfigurationFormProps {
  formState: EstimateFormState;
  isReadOnly: boolean; // Combined read-only state (e.g., saved successfully or no permission)
  className?: string; // Add optional className prop
  draftUuid?: string; // Draft UUID for deal linking (optional)
}

const EstimateConfigurationForm: React.FC<EstimateConfigurationFormProps> = ({
  formState,
  isReadOnly,
  className = "w-[75%]", // Default to original width
  draftUuid,
}) => {
  // State for deal linking
  const [linkedDeal, setLinkedDeal] = useState<Deal | null>(null);

  // State for deal selection (new estimates)
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);

  // Modal states
  const [showDealLinkModal, setShowDealLinkModal] = useState(false);
  const [showLinkedDealModal, setShowLinkedDealModal] = useState(false);
  const [showDealSelectionModal, setShowDealSelectionModal] = useState(false);

  // Fetch linked deal when draftUuid changes (only if estimate is already saved)
  useEffect(() => {
    const fetchLinkedDeal = async () => {
      if (!draftUuid) {
        // If no draftUuid, we're in "new estimate" mode - no linked deal yet
        setLinkedDeal(null);
        return;
      }

      try {
        const deals = await getLinkedDealsForEstimate(draftUuid, "draft");
        setLinkedDeal(deals.length > 0 ? deals[0] : null);
      } catch (error) {
        console.error("Failed to fetch linked deal:", error);
        setLinkedDeal(null);
      }
    };

    fetchLinkedDeal();
  }, [draftUuid]);

  // Handle deal link modal close with refresh
  const handleDealLinkModalClose = () => {
    setShowDealLinkModal(false);

    // Refresh linked deal data after potential linking
    if (draftUuid) {
      getLinkedDealsForEstimate(draftUuid, "draft")
        .then((deals) => {
          setLinkedDeal(deals.length > 0 ? deals[0] : null);
        })
        .catch((error) => {
          console.error("Failed to refresh linked deal:", error);
        });
    }
  };

  // Handle linked deal modal close with refresh
  const handleLinkedDealModalClose = () => {
    setShowLinkedDealModal(false);

    // Refresh linked deal data after potential unlinking
    if (draftUuid) {
      getLinkedDealsForEstimate(draftUuid, "draft")
        .then((deals) => {
          setLinkedDeal(deals.length > 0 ? deals[0] : null);
        })
        .catch((error) => {
          console.error("Failed to refresh linked deal:", error);
        });
    }
  };

  // Handle deal selection for new estimates
  const handleDealSelection = (deal: Deal) => {
    console.log("🔍 DEAL SELECTED:", deal);
    setSelectedDeal(deal);

    // Populate form fields from deal
    formState.setFormDataFromDeal(deal);

    console.log("🔍 AFTER DEAL SELECTION - isDealLinked will be:", !!deal);
  };

  // Use deal linking state from form hook
  const isDealLinked = formState.isDealLinked;

  return (
    <div className={`${className} space-y-4`}>
      {/* Estimate Details Form */}
      <div className="flex gap-4">
        {/* Left: Client/Project */}
        <div className="w-[40%] bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="space-y-3">
            {/* Estimate Creation Method (only for new estimates) */}
            {!draftUuid && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  How would you like to create this estimate?
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  {/* Option 1: Link to Deal */}
                  <div
                    className={`relative py-4 px-3 border-2 rounded-lg transition-all cursor-pointer ${
                      selectedDeal
                        ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20"
                        : "border-gray-200 dark:border-gray-600 hover:border-emerald-300 dark:hover:border-emerald-500"
                    }`}
                    onClick={() =>
                      !selectedDeal && setShowDealSelectionModal(true)
                    }
                  >
                    <div className="flex items-center space-x-2">
                      <div className="flex-shrink-0">
                        {selectedDeal ? (
                          <svg
                            className="w-4 h-4 text-emerald-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                            />
                          </svg>
                        ) : (
                          <svg
                            className="w-4 h-4 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                            />
                          </svg>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h5 className="font-medium text-gray-900 dark:text-white text-xs">
                          {selectedDeal
                            ? "Deal Linked"
                            : "Link to Existing Deal"}
                        </h5>
                        <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                          {selectedDeal
                            ? `${selectedDeal.name} • ${selectedDeal.company?.name}`
                            : "Auto-populate from CRM deal"}
                        </p>
                      </div>
                      {selectedDeal && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowDealSelectionModal(true);
                          }}
                          className="px-2 py-1 bg-emerald-600 hover:bg-emerald-700 text-white text-xs font-medium rounded transition-colors"
                        >
                          Change
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Option 2: Manual Entry */}
                  <div
                    className={`relative py-4 px-3 border-2 rounded-lg transition-all cursor-pointer ${
                      !selectedDeal
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500"
                    }`}
                    onClick={() =>
                      selectedDeal &&
                      (setSelectedDeal(null), formState.resetForm())
                    }
                  >
                    <div className="flex items-center space-x-2">
                      <div
                        className={`flex-shrink-0 w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                          !selectedDeal
                            ? "border-blue-500 bg-blue-500"
                            : "border-gray-300 dark:border-gray-600"
                        }`}
                      >
                        {!selectedDeal && (
                          <svg
                            className="w-2.5 h-2.5 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        )}
                      </div>
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-900 dark:text-white text-xs">
                          {selectedDeal
                            ? "Switch to Manual"
                            : "Create Manually"}
                        </h5>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          Enter details manually
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Divider line */}
                <div className="my-4 border-t border-gray-200 dark:border-gray-600"></div>
              </div>
            )}
            <div>
              <label
                htmlFor="clientSearch"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                Client
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="clientSearch"
                  placeholder={
                    isDealLinked
                      ? "Client populated from deal"
                      : "Search clients..."
                  }
                  className={`w-full p-2 border rounded-lg dark:text-white ${
                    isDealLinked
                      ? "bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 cursor-not-allowed"
                      : "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary focus:border-primary"
                  }`}
                  disabled={
                    formState.isLoadingClients || isReadOnly || isDealLinked
                  }
                  value={formState.clientSearchTerm}
                  onFocus={() =>
                    !isDealLinked && formState.setClientDropdownOpen(true)
                  }
                  onChange={(e) =>
                    !isDealLinked &&
                    formState.setClientSearchTerm(e.target.value)
                  }
                />
                {/* Dropdown icon, loading indicator, dropdown list */}
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  {" "}
                  <svg
                    className="h-4 w-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>{" "}
                </div>
                {formState.isLoadingClients && (
                  <div className="absolute right-6 top-1/2 transform -translate-y-1/2">
                    {" "}
                    <svg
                      className="animate-spin h-4 w-4 text-gray-400"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>{" "}
                  </div>
                )}
                {formState.clientDropdownOpen &&
                  !isReadOnly &&
                  !formState.isLoadingClients && (
                    <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-48 overflow-y-auto">
                      {formState.filteredClients.length > 0 ? (
                        formState.filteredClients.map((client) => (
                          <div
                            key={client.id}
                            className="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-sm"
                            onClick={() =>
                              formState.handleClientChange(client.id)
                            }
                          >
                            {" "}
                            {client.name}{" "}
                          </div>
                        ))
                      ) : (
                        <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                          No clients found
                        </div>
                      )}
                    </div>
                  )}
              </div>
              {formState.clientFetchError && (
                <p className="text-xs text-red-500 mt-1">
                  {formState.clientFetchError}
                </p>
              )}
            </div>
            <div>
              <label
                htmlFor="projectName"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                Project Name
              </label>
              <input
                type="text"
                id="projectName"
                value={formState.projectName}
                onChange={(e) =>
                  !isDealLinked &&
                  formState.handleProjectNameChange(e.target.value)
                }
                placeholder={
                  isDealLinked
                    ? "Project name from deal"
                    : "e.g., Q3 Website Redesign"
                }
                className={`w-full p-2 border rounded-lg dark:text-white ${
                  isDealLinked
                    ? "bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 cursor-not-allowed"
                    : "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary focus:border-primary"
                }`}
                disabled={isReadOnly || isDealLinked}
              />
            </div>

            {/* View Deal Button - only show when deal is linked */}
            {(selectedDeal || linkedDeal) && (
              <div className="mt-3">
                <a
                  href={`/crm/deals/${(selectedDeal || linkedDeal)?.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-1.5 rounded text-xs font-medium bg-emerald-600 text-white hover:bg-emerald-700 transition-colors"
                >
                  <svg
                    className="w-3.5 h-3.5 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    />
                  </svg>
                  View Deal
                </a>
              </div>
            )}
          </div>
        </div>
        {/* Right: Dates/Duration */}
        <div className="w-[60%] bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <div className="space-y-3 flex flex-col justify-between h-full">
              <div>
                <label
                  htmlFor="startDate"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Start Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  id="startDate"
                  value={formState.startDateStr}
                  onChange={(e) =>
                    formState.handleDateChange(e.target.value, true)
                  }
                  className="w-[85%] p-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white text-sm"
                  disabled={isReadOnly}
                />
              </div>
              <div>
                <label
                  htmlFor="endDate"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  End Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  id="endDate"
                  value={formState.endDateStr}
                  onChange={(e) =>
                    formState.handleDateChange(e.target.value, false)
                  }
                  className="w-[85%] p-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white text-sm"
                  disabled={isReadOnly}
                />
              </div>
            </div>
            <div className="flex flex-col h-full">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Duration
              </label>
              <div className="flex flex-col space-y-1.5 h-full justify-between py-1">
                {/* Duration buttons */}
                <div className="grid grid-cols-4 gap-1.5">
                  {[
                    { label: "1W", value: "1week" },
                    { label: "2W", value: "2weeks" },
                    { label: "3W", value: "3weeks" },
                    { label: "4W/1M", value: "1month" },
                  ].map((d) => (
                    <button
                      key={d.value}
                      type="button"
                      onClick={() => formState.handleDurationSelect(d.value)}
                      className={`py-1 px-1 rounded border text-xs ${
                        formState.activeDuration === d.value
                          ? "border-primary bg-primary bg-opacity-10 text-primary dark:bg-opacity-20"
                          : "border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400"
                      }`}
                      disabled={isReadOnly}
                    >
                      {d.label}
                    </button>
                  ))}
                </div>
                <div className="grid grid-cols-4 gap-1.5">
                  {[
                    { label: "6W", value: "6weeks" },
                    { label: "2M/8W", value: "2months" },
                    { label: "3M/12W", value: "3months" },
                    { label: "6M/24W", value: "6months" },
                  ].map((d) => (
                    <button
                      key={d.value}
                      type="button"
                      onClick={() => formState.handleDurationSelect(d.value)}
                      className={`py-1 px-1 rounded border text-xs ${
                        formState.activeDuration === d.value
                          ? "border-primary bg-primary bg-opacity-10 text-primary dark:bg-opacity-20"
                          : "border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400"
                      }`}
                      disabled={isReadOnly}
                    >
                      {d.label}
                    </button>
                  ))}
                </div>
                <div className="border-t border-gray-200 dark:border-gray-600 my-1.5"></div>
                <div className="grid grid-cols-2 gap-1.5">
                  <button
                    type="button"
                    onClick={() => {
                      const d = new Date(formState.endDateStr);
                      d.setDate(d.getDate() - 7);
                      formState.handleDateChange(
                        format(d, "yyyy-MM-dd"),
                        false
                      );
                    }}
                    className="py-1 px-1 rounded border text-xs border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400"
                    disabled={isReadOnly}
                  >
                    - 1 Week
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      const d = new Date(formState.endDateStr);
                      d.setDate(d.getDate() + 7);
                      formState.handleDateChange(
                        format(d, "yyyy-MM-dd"),
                        false
                      );
                    }}
                    className="py-1 px-1 rounded border text-xs border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400"
                    disabled={isReadOnly}
                  >
                    + 1 Week
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Invoice & Payment Terms Section */}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="mb-3">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Invoice & Payment Terms
                <span className="ml-2 text-xs font-normal text-gray-500 dark:text-gray-400">
                  This is used for cashflow forecasting. It can be changed at
                  any time.
                </span>
              </h4>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Invoice Frequency */}
              <div>
                <label
                  htmlFor="invoiceFrequency"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  <div className="flex items-center space-x-2">
                    <svg
                      className="w-4 h-4 text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    <span>Invoice Frequency</span>
                    <span className="text-red-500">*</span>
                  </div>
                </label>
                <div className="relative">
                  <select
                    id="invoiceFrequency"
                    value={formState.invoiceFrequency}
                    onChange={(e) =>
                      formState.handleInvoiceFrequencyChange(e.target.value)
                    }
                    className={`w-full p-3 pr-10 border rounded-lg text-sm transition-all duration-200 appearance-none bg-white dark:bg-gray-700 ${
                      formState.invoiceFrequency
                        ? "border-blue-300 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-300 dark:border-gray-600"
                    } ${
                      !formState.invoiceFrequency && !isReadOnly
                        ? "border-red-300 dark:border-red-600"
                        : ""
                    } focus:ring-2 focus:ring-primary focus:border-primary dark:text-white`}
                    disabled={isReadOnly}
                  >
                    <option value="" disabled>
                      Choose how often to invoice...
                    </option>
                    {INVOICE_FREQUENCY_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    {formState.invoiceFrequency ? (
                      <svg
                        className="w-4 h-4 text-blue-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    )}
                  </div>
                </div>
                {!formState.invoiceFrequency && !isReadOnly && (
                  <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                    Please select an invoice frequency
                  </p>
                )}
              </div>

              {/* Payment Terms */}
              <div>
                <label
                  htmlFor="paymentTerms"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  <div className="flex items-center space-x-2">
                    <svg
                      className="w-4 h-4 text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                      />
                    </svg>
                    <span>Payment Terms</span>
                    <span className="text-red-500">*</span>
                  </div>
                </label>
                <div className="relative">
                  <select
                    id="paymentTerms"
                    value={formState.paymentTerms || ""}
                    onChange={(e) =>
                      formState.handlePaymentTermsChange(
                        e.target.value ? Number(e.target.value) : null
                      )
                    }
                    className={`w-full p-3 pr-10 border rounded-lg text-sm transition-all duration-200 appearance-none bg-white dark:bg-gray-700 ${
                      formState.paymentTerms
                        ? "border-blue-300 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-300 dark:border-gray-600"
                    } ${
                      !formState.paymentTerms && !isReadOnly
                        ? "border-red-300 dark:border-red-600"
                        : ""
                    } focus:ring-2 focus:ring-primary focus:border-primary dark:text-white`}
                    disabled={isReadOnly}
                  >
                    <option value="" disabled>
                      Choose payment terms...
                    </option>
                    {PAYMENT_TERMS_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    {formState.paymentTerms ? (
                      <svg
                        className="w-4 h-4 text-blue-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    )}
                  </div>
                </div>
                {!formState.paymentTerms && !isReadOnly && (
                  <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                    Please select payment terms
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Deal Link Modal */}
        {draftUuid && (
          <DealLinkModal
            isOpen={showDealLinkModal}
            onClose={handleDealLinkModalClose}
            estimateId={draftUuid}
            estimateType="draft"
          />
        )}

        {/* Linked Deal Modal */}
        {draftUuid && linkedDeal && (
          <LinkedDealModal
            isOpen={showLinkedDealModal}
            onClose={handleLinkedDealModalClose}
            estimateId={draftUuid}
            estimateType="draft"
          />
        )}

        {/* Deal Selection Modal for new estimates */}
        <DealSelectionModal
          isOpen={showDealSelectionModal}
          onClose={() => setShowDealSelectionModal(false)}
          onSelectDeal={handleDealSelection}
        />
      </div>
    </div>
  );
};

export default EstimateConfigurationForm;
