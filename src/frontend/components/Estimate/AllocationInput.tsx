import React, { useState, useCallback, useEffect } from 'react';

/**
 * Props for the AllocationInput component
 */
export interface AllocationInputProps {
  /**
   * Current allocation value
   */
  value: number;
  
  /**
   * Callback when value changes
   */
  onValueChange: (value: number) => void;
}

/**
 * Custom input component for handling decimal allocation values
 * Handles formatting, validation, and clamping values between 0 and 5 days
 */
const AllocationInput: React.FC<AllocationInputProps> = ({ value, onValueChange }) => {
  // Track the input value as a string for better input control
  const [inputValue, setInputValue] = useState<string>(value > 0 ? value.toString() : '');
  
  // Update the displayed input value when the prop changes (e.g., from external sources)
  useEffect(() => {
    setInputValue(value > 0 ? value.toFixed(1) : '');
  }, [value]);
  
  // Handle blur event to format and validate the final value
  const handleBlur = useCallback(() => {
    if (inputValue === '' || inputValue === '.') {
      onValueChange(0);
      setInputValue('');
      return;
    }
    
    // Normalize the input (add leading zero if needed)
    const normalizedValue = inputValue.startsWith('.') ? `0${inputValue}` : inputValue;
    const numericValue = parseFloat(normalizedValue);
    
    if (!isNaN(numericValue)) {
      // Round to 1 decimal place and clamp between 0 and 5
      const roundedValue = Math.round(numericValue * 10) / 10;
      const clampedValue = Math.min(5, Math.max(0, roundedValue));
      
      // Update the parent component
      onValueChange(clampedValue);
      
      // Format the display (or clear if zero)
      setInputValue(clampedValue > 0 ? clampedValue.toFixed(1) : '');
    } else {
      // Reset to the previous valid value
      setInputValue(value > 0 ? value.toFixed(1) : '');
    }
  }, [inputValue, onValueChange, value]);
  
  // Handle changes to the input field
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow digits and up to one decimal point
    let newValue = e.target.value.replace(/[^0-9.]/g, '');

    // Handle multiple decimal points
    const decimalCount = (newValue.match(/\./g) || []).length;
    if (decimalCount > 1) {
      const parts = newValue.split('.');
      newValue = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // Update the local state without immediate validation
    setInputValue(newValue);
  }, []); // No dependencies needed as it only calls setInputValue

  // Handle key press to submit on Enter
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.currentTarget.blur(); // Trigger the blur event
    }
  }, []);
  
  return (
    <div className="group relative">
      <input
        type="text"
        inputMode="decimal"
        value={inputValue}
        placeholder="–"
        title="Enter days from 0.1 to 5.0 (in 0.1 increments)"
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className="w-full text-center py-0.5 text-sm text-gray-800 dark:text-gray-300 bg-transparent border-0 focus:outline-none focus:ring-0 relative z-10"
        style={{ appearance: 'textfield' }}
      />
      <div className="absolute inset-0 pointer-events-none group-hover:bg-gray-100 dark:group-hover:bg-gray-700/30 group-focus-within:bg-blue-50 dark:group-focus-within:bg-blue-900/20 group-focus-within:ring-1 group-focus-within:ring-blue-500 dark:group-focus-within:ring-blue-400 rounded-sm opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity"></div>
    </div>
  );
};

export default AllocationInput;