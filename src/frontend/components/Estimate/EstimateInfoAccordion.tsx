import React from 'react';

interface EstimateInfoAccordionProps {
  showInformation: boolean;
  setShowInformation: (show: boolean) => void;
  variant?: 'list' | 'editor';
}

const EstimateInfoAccordion: React.FC<EstimateInfoAccordionProps> = ({
  showInformation,
  setShowInformation,
  variant = 'editor'
}) => {
  return (
    <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-blue-50/50 dark:bg-blue-900/10">
      <button
        className="w-full flex justify-between items-center focus:outline-none"
        onClick={() => setShowInformation(!showInformation)}
      >
        <div className="flex items-center">
          <svg className="w-5 h-5 text-blue-500 dark:text-blue-400 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="font-medium text-sm text-blue-700 dark:text-blue-300">Understanding Estimates</span>
        </div>
        <svg
          className={`w-5 h-5 text-blue-500 dark:text-blue-400 transition-transform duration-200 ${showInformation ? 'transform rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {showInformation && variant === 'list' && (
        <div className="mt-3 text-sm text-gray-600 dark:text-gray-300 border-l-2 border-blue-300 dark:border-blue-700 pl-4 py-0.5 ml-1.5">
          <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">Estimate Workflow:</h4>
          <p className="mb-3">
            Upstream estimates help you create detailed project pricing with a simple workflow:
          </p>
          <div className="grid md:grid-cols-2 gap-4 mt-2">
            <div className="p-3 border border-blue-200 dark:border-blue-800 rounded-lg bg-blue-50/50 dark:bg-blue-900/20">
              <h4 className="font-medium text-blue-800 dark:text-blue-300">Draft Estimate</h4>
              <p className="text-sm mt-1">
                Create and save drafts in Upstream to plan projects and allocate staff resources across time. Draft estimates can be modified, shared with team members, and refined without affecting Harvest.
              </p>
            </div>

            <div className="p-3 border border-blue-200 dark:border-blue-800 rounded-lg bg-blue-50/50 dark:bg-blue-900/20">
              <h4 className="font-medium text-blue-800 dark:text-blue-300">Publish to Harvest</h4>
              <p className="text-sm mt-1">
                When ready to share with clients, publish to Harvest. This creates a finalized estimate in Harvest that can be sent to clients. Once published, changes made in Upstream will create a new Harvest estimate rather than modifying the existing one.
              </p>
            </div>
          </div>

          <div className="mt-3 pt-1.5 border-t border-blue-200 dark:border-blue-800/30">
            <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1.5">
              <strong>Tip:</strong> Use the search function above to filter by client or date. Draft estimates are stored locally and won't appear in Harvest until published.
            </p>
          </div>

          <div className="mt-2 flex justify-between items-center">
            <span className="text-xs text-blue-800/80 dark:text-blue-300/80 italic">
              Create a new estimate using the button above
            </span>
            <a href="#estimates-section" className="text-xs text-blue-600 dark:text-blue-400 hover:underline">
              More details in Help docs →
            </a>
          </div>
        </div>
      )}

      {showInformation && variant === 'editor' && (
        <div className="mt-3 text-sm text-gray-600 dark:text-gray-300 border-l-2 border-blue-300 dark:border-blue-700 pl-4 py-0.5 ml-1.5">
          <p className="mb-2">
            The Estimate Editor helps you create detailed project pricing based on staff resources and timeline allocation.
          </p>
          <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1 mt-3">Key Features:</h4>
          <ul className="list-disc pl-5 space-y-1">
            <li><strong>Staff Selection:</strong> Add team members from Harvest with their rates</li>
            <li><strong>Weekly Allocation:</strong> Assign staff days across weeks in the project timeline</li>
            <li><strong>Visualisation:</strong> View the distribution of effort with the Weekly Effort Histogram</li>
            <li><strong>Margin Calculation:</strong> Automatic cost and revenue calculations with margin analysis</li>
            <li><strong>Draft Saving:</strong> Save work in progress locally to continue editing later</li>
            <li><strong>Harvest Integration:</strong> Publish finalised estimates to Harvest for client sharing</li>
          </ul>

          <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1 mt-3">Workflow:</h4>
          <ol className="list-decimal pl-5 space-y-1">
            <li>Create a draft estimate in Upstream</li>
            <li>Add staff and allocate time across the project timeline</li>
            <li>Save the draft to Upstream for continued editing</li>
            <li>Share with team members for review if needed</li>
            <li>When finalized, publish to Harvest to create a client-ready estimate</li>
            <li>You can continue to make changes to the draft even after publishing</li>
            <li>Publishing again will create a new version in Harvest rather than updating the existing one</li>
          </ol>

          <div className="mt-3 pt-1.5 border-t border-blue-200 dark:border-blue-800/30">
            <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1.5">
              <strong>Note:</strong> Once an estimate is published to Harvest and sent to clients, modifications should be made as new versions rather than updates to maintain client communication history.
            </p>
          </div>

          <div className="mt-2 flex justify-between items-center">
            <span className="text-xs text-blue-800/80 dark:text-blue-300/80 italic">
              Use &quot;Save Draft&quot; to store work in progress, &quot;Publish to Harvest&quot; when ready for clients
            </span>
            <a href="#estimates-section" className="text-xs text-blue-600 dark:text-blue-400 hover:underline">
              More details in Help docs →
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default EstimateInfoAccordion;
