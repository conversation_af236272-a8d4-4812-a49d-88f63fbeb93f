/**
 * Utility functions for the Price Estimation feature.
 */

// Constants
const HOURS_PER_DAY = 7.5; // Standard billable hours per day

/**
 * Converts an hourly rate to a daily rate based on standard hours.
 * @param hourlyRate - The hourly rate.
 * @returns The calculated daily rate. Returns 0 if hourlyRate is null or undefined.
 */
export const convertHourlyToDailyRate = (hourlyRate: number | null | undefined): number => {
  if (hourlyRate === null || hourlyRate === undefined) {
    return 0;
  }
  return hourlyRate * HOURS_PER_DAY;
};

/**
 * Converts a daily rate to an hourly rate based on specified hours per day.
 * @param dailyRate - The daily rate.
 * @param hoursPerDay - Hours per day (default: 7.5).
 * @returns The calculated hourly rate. Returns 0 if dailyRate is null or undefined.
 */
export const convertDailyToHourlyRate = (dailyRate: number | null | undefined, hoursPerDay: number = 7.5): number => {
  if (dailyRate === null || dailyRate === undefined || hoursPerDay <= 0) {
    return 0;
  }
  return dailyRate / hoursPerDay;
};

/**
 * Formats a rate for display based on display mode and hours per day setting.
 * @param dailyRate - The daily rate (stored value).
 * @param showHourly - Whether to display as hourly rate.
 * @param hoursPerDay - Hours per day for conversion (default: 7.5).
 * @param currencyCode - Currency code for formatting (default: 'AUD').
 * @returns The formatted rate string.
 */
export const formatRateDisplay = (
  dailyRate: number,
  showHourly: boolean = false,
  hoursPerDay: number = 7.5,
  currencyCode: string = 'AUD'
): string => {
  if (showHourly) {
    const hourlyRate = convertDailyToHourlyRate(dailyRate, hoursPerDay);
    return formatCurrency(hourlyRate, currencyCode);
  }
  return formatCurrency(dailyRate, currencyCode);
};

/**
 * Calculates the daily gross margin percentage.
 * @param proposedRateDaily - The proposed daily selling rate.
 * @param costRateDaily - The daily cost rate.
 * @returns The gross margin percentage. Returns 0 if proposedRateDaily is 0 or less.
 */
export const calculateDailyGrossMarginPercentage = (proposedRateDaily: number, costRateDaily: number): number => {
  if (proposedRateDaily <= 0) {
    return 0;
  }
  const margin = proposedRateDaily - costRateDaily;
  return (margin / proposedRateDaily) * 100;
};

/**
 * Calculates the total allocated days for a staff member from their weekly allocation.
 * @param weeklyAllocation - An object where keys are week identifiers and values are days allocated.
 * @returns The total number of days allocated.
 */
export const calculateTotalDays = (weeklyAllocation: { [weekIdentifier: string]: number }): number => {
  return Object.values(weeklyAllocation).reduce((sum, days) => sum + (days || 0), 0);
};

/**
 * Calculates the total cost for a staff member based on their daily cost rate and total days.
 * @param costRateDaily - The daily cost rate for the staff member.
 * @param totalDays - The total number of days allocated to the staff member.
 * @returns The total cost for the staff member.
 */
export const calculateTotalCost = (costRateDaily: number, totalDays: number): number => {
  return costRateDaily * totalDays;
};

/**
 * Calculates the total fees (revenue) for a staff member based on their proposed daily rate and total days.
 * @param proposedRateDaily - The proposed daily selling rate for the staff member.
 * @param totalDays - The total number of days allocated to the staff member.
 * @returns The total fees for the staff member.
 */
export const calculateTotalFees = (proposedRateDaily: number, totalDays: number): number => {
  return proposedRateDaily * totalDays;
};

// Define a structure for calculated totals per staff member
interface StaffTotals {
  totalDays: number;
  totalCost: number;
  totalFees: number;
}

/**
 * Calculates the overall project totals by summing up totals from each staff allocation.
 * @param staffAllocationsWithTotals - Array of staff allocations including their calculated totals.
 * @returns An object containing project metrics such as:
 *   - totalRevenue: Total revenue from all staff allocations
 *   - totalCost: Total cost from all staff allocations
 *   - marginAmount: Difference between revenue and cost
 *   - marginPercentage: Margin as a percentage of revenue
 *   - totalDays: Total days allocated across all staff
 *   - averageDailyRate: Average rate per day
 *   - targetTotalRevenue: What the revenue would be if all staff were at target rates
 *   - targetRateDifference: Difference between proposed revenue and target revenue
 *   - targetRatePercentageDifference: Percentage difference from target rates
 */
export const calculateProjectTotals = (
  staffAllocationsWithTotals: (StaffTotals & { [key: string]: any })[],
  discountType: 'percentage' | 'amount' | 'none' = 'none',
  discountValue: number = 0
): {
  totalRevenue: number;
  totalCost: number;
  marginAmount: number;
  marginPercentage: number;
  totalDays: number;
  averageDailyRate: number;
  targetTotalRevenue: number;
  targetRateDifference: number;
  targetRatePercentageDifference: number;
  discountType: 'percentage' | 'amount' | 'none';
  discountValue: number;
  discountAmount: number;
  discountedRevenue: number;
} => {
  const totalDays = staffAllocationsWithTotals.reduce((sum, alloc) => sum + alloc.totalDays, 0);
  const totalRevenue = staffAllocationsWithTotals.reduce((sum, alloc) => sum + alloc.totalFees, 0);
  const totalCost = staffAllocationsWithTotals.reduce((sum, alloc) => sum + alloc.totalCost, 0);

  // Calculate discount
  const { discountAmount, discountedTotal: discountedRevenue } =
    calculateDiscount(totalRevenue, discountType, discountValue);

  // Calculate margins based on discounted revenue
  const marginAmount = discountedRevenue - totalCost;
  const marginPercentage = discountedRevenue > 0 ? (marginAmount / discountedRevenue) * 100 : 0;

  // Other calculations
  const averageDailyRate = totalDays > 0 ? discountedRevenue / totalDays : 0;

  // Calculate target revenue and deviations
  const targetTotalRevenue = staffAllocationsWithTotals.reduce(
    (sum, alloc) => sum + ((alloc.onbordTargetRateDaily || 0) * alloc.totalDays),
    0
  );
  const targetRateDifference = discountedRevenue - targetTotalRevenue;
  const targetRatePercentageDifference = targetTotalRevenue > 0
    ? (targetRateDifference / targetTotalRevenue) * 100
    : 0;

  return {
    totalRevenue,
    totalCost,
    marginAmount,
    marginPercentage,
    totalDays,
    averageDailyRate,
    targetTotalRevenue,
    targetRateDifference,
    targetRatePercentageDifference,

    // Discount fields
    discountType,
    discountValue,
    discountAmount,
    discountedRevenue
  };
};

/**
 * Calculates discount amount and discounted total based on the discount type and value
 * @param total - The total amount before discount
 * @param discountType - The type of discount ('percentage', 'amount', or 'none')
 * @param discountValue - The discount value (percentage or fixed amount)
 * @returns Object containing calculated discount amount and discounted total
 */
export const calculateDiscount = (
  total: number,
  discountType: 'percentage' | 'amount' | 'none',
  discountValue: number
): { discountAmount: number, discountedTotal: number } => {
  // Ensure we have valid numbers
  const validTotal = isNaN(total) ? 0 : total;
  const validDiscountValue = isNaN(discountValue) ? 0 : discountValue;

  // Handle invalid cases: none type or non-positive value
  if (discountType === 'none' || validDiscountValue <= 0) {
    return { discountAmount: 0, discountedTotal: validTotal };
  }

  let discountAmount = 0;
  if (discountType === 'percentage') {
    // Cap percentage at 100
    const cappedPercentage = Math.min(validDiscountValue, 100);
    discountAmount = validTotal * (cappedPercentage / 100);
  } else if (discountType === 'amount') {
    // Cap amount at total
    discountAmount = Math.min(validDiscountValue, validTotal);
  }

  // Ensure discountAmount is a valid number
  discountAmount = isNaN(discountAmount) ? 0 : discountAmount;

  return {
    discountAmount,
    discountedTotal: validTotal - discountAmount
  };
};

/**
 * Formats a number as currency (e.g., $1,234.56).
 * Assumes AUD for now, but could be made dynamic.
 * @param value - The number to format.
 * @param currencyCode - The ISO currency code (default: 'AUD').
 * @returns The formatted currency string.
 */
export const formatCurrency = (value: number, currencyCode: string = 'AUD'): string => {
  // Basic check for non-numeric input, though TypeScript helps here
  if (typeof value !== 'number' || isNaN(value)) {
    // Return a default value instead of an empty string
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(0);
  }

  return new Intl.NumberFormat('en-AU', { // Use locale that matches currency for best formatting
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};


// TODO: Consider centralizing week generation logic here if needed elsewhere.
