import React, { useState } from "react";
import { useDrag } from "react-dnd";
import { useMutation, useQueryClient } from "react-query";
import type { RadarCompany, CompanyPriority } from "../../../types/leads-types";
import {
  PRIORITY_COLORS,
  PRIORITY_DESCRIPTIONS,
} from "../../../../types/company-types";
import { updateRadarCompany } from "../../../api/leads";
import { formatCurrency, formatRelativeDate } from "../../../utils/format";

interface RadarCompanyCardProps {
  company: RadarCompany;
}

// Define the drag type for companies
export const COMPANY_DRAG_TYPE = "company";

/**
 * Component for displaying a company card in the Radar view
 */
const RadarCompanyCard: React.FC<RadarCompanyCardProps> = ({ company }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const queryClient = useQueryClient();

  // Set up drag source
  const [{ isDragging }, drag] = useDrag({
    type: COMPANY_DRAG_TYPE,
    item: { id: company.id },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  // Mutation for updating company priority
  const updatePriorityMutation = useMutation(
    (priority: CompanyPriority) => updateRadarCompany(company.id, { priority }),
    {
      onSuccess: () => {
        // Invalidate and refetch
        queryClient.invalidateQueries("radarCompanies");
        setIsMenuOpen(false);
      },
    }
  );

  // Get priority color
  const priorityColor = company.priority
    ? PRIORITY_COLORS[company.priority]
    : "bg-gray-300";

  // Handle priority change
  const handlePriorityChange = (priority: CompanyPriority) => {
    updatePriorityMutation.mutate(priority);
  };

  // Toggle menu
  const toggleMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(!isMenuOpen);
  };

  // Get company logo placeholder based on company name
  const getCompanyInitial = (name: string): string => {
    return name.charAt(0).toUpperCase();
  };

  // Get random color based on company ID for logo background
  const getLogoColor = (id: string): string => {
    const colors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-purple-500",
      "bg-yellow-500",
      "bg-pink-500",
      "bg-indigo-500",
      "bg-red-500",
      "bg-teal-500",
    ];

    // Simple hash function to get consistent color for the same ID
    const hash = id
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  return (
    <div
      ref={drag}
      className={`radar-company-card ${
        isDragging ? "is-dragging" : ""
      }`}
    >
      {/* Header section */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-start gap-3 min-w-0 flex-1">
          {/* Company logo/initial */}
          <div
            className={`company-logo ${getLogoColor(
              company.id
            )}`}
          >
            {getCompanyInitial(company.name)}
          </div>

          <div className="min-w-0 flex-1">
            <h4 className="company-name">
              {company.name}
            </h4>
            {company.industry && (
              <p className="company-industry">
                {company.industry}
              </p>
            )}
          </div>
        </div>

        {/* Website link */}
        {company.website && (
          <a
            href={company.website}
            target="_blank"
            rel="noopener noreferrer"
            className="p-1.5 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-150 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
            title={`Visit ${company.website}`}
            onClick={(e) => e.stopPropagation()}
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              />
            </svg>
          </a>
        )}
      </div>

      {/* Priority section */}
      <div className="relative mb-3">
        <button
          onClick={toggleMenu}
          className={`w-full px-3 py-1.5 text-xs font-medium rounded-lg focus:outline-none transition-all duration-150 hover:shadow-sm flex items-center justify-center ${
            company.priority
              ? `${priorityColor.replace(
                  "bg-",
                  "bg-opacity-20 bg-"
                )} ${priorityColor.replace(
                  "bg-",
                  "text-"
                )} hover:${priorityColor.replace(
                  "bg-",
                  "bg-opacity-30 bg-"
                )}`
              : "bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600"
          }`}
          title={
            company.priority
              ? PRIORITY_DESCRIPTIONS[company.priority]
              : "Set priority"
          }
        >
          <span
            className={`w-2 h-2 rounded-full mr-2 ${
              company.priority ? priorityColor : "bg-gray-400"
            }`}
          />
          {company.priority || "Set priority"}
        </button>

        {/* Dropdown menu */}
        {isMenuOpen && (
          <div className="absolute left-0 right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-20 border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="py-1">
              <div className="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                Set Priority
              </div>
              {(
                [
                  "High",
                  "Medium",
                  "Low",
                  "Qualified out",
                ] as CompanyPriority[]
              ).map((priority) => (
                <button
                  key={priority}
                  className={`block w-full text-left px-4 py-2.5 text-sm transition-colors hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    company.priority === priority
                      ? `${PRIORITY_COLORS[priority].replace(
                          "bg-",
                          "bg-opacity-10 bg-"
                        )} ${PRIORITY_COLORS[priority].replace(
                          "bg-",
                          "text-"
                        )}`
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                  onClick={() => handlePriorityChange(priority)}
                >
                  <span
                    className={`inline-block w-3 h-3 rounded-full ${PRIORITY_COLORS[priority]} mr-2`}
                  />
                  {priority}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Last interaction */}
      {company.lastInteractionDate && (
        <div className="mb-3 text-xs text-gray-500 dark:text-gray-400">
          <svg className="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {formatRelativeDate(company.lastInteractionDate)}
        </div>
      )}

      {/* Metrics section */}
      <div className="space-y-2 pt-3 border-t border-gray-200 dark:border-gray-700">
        {/* Active deals row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
            <svg
              className="w-3 h-3 mr-1.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <span>Deals</span>
          </div>
          <div className="text-xs font-medium text-right">
            <span className="text-gray-700 dark:text-gray-300">
              {company.activeDealsCount || 0}
            </span>
            {company.totalDealValue ? (
              <span className="text-emerald-600 dark:text-emerald-400 ml-2">
                {formatCurrency(company.totalDealValue, { compact: true })}
              </span>
            ) : null}
          </div>
        </div>

        {/* Total spend row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
            <svg
              className="w-3 h-3 mr-1.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>Spend</span>
          </div>
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
            {company.totalSpend !== undefined && company.totalSpend !== null
              ? formatCurrency(company.totalSpend, { compact: true })
              : "—"}
          </span>
        </div>
      </div>
    </div>
  );
};

export default RadarCompanyCard;
