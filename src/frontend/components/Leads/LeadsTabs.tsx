import React from 'react';

interface LeadsTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

/**
 * Tabs navigation for the Leads section
 */
const LeadsTabs: React.FC<LeadsTabsProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    { id: 'radar', label: 'Radar' },
    { id: 'relationships', label: 'Relationships' },
    { id: 'wagov', label: 'WA Govt' }
  ];
  
  return (
    <div className="border-b border-gray-200 dark:border-gray-700">
      <nav className="flex -mb-px">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`py-2 px-4 text-sm font-medium border-b-2 ${
              activeTab === tab.id
                ? 'border-purple-500 text-purple-600 dark:text-purple-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => onTabChange(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </nav>
    </div>
  );
};

export default LeadsTabs;
