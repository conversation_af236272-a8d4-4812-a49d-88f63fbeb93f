import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import LeadsTabs from "./LeadsTabs";
import RadarPage from "./Radar/RadarPage";
import RelationshipsPage from "./Relationships/RelationshipsPage";
import WAGovtPage from "./WAGovt/WAGovtPage";

/**
 * Main Leads page component with tabs for different sections
 */
const LeadsPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Determine active tab based on URL
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes("/leads/relationships")) return "relationships";
    if (path.includes("/leads/wagov")) return "wagov";
    return "radar"; // Default tab
  };

  const [activeTab, setActiveTab] = useState(getActiveTab());

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    navigate(`/leads/${tab === "radar" ? "" : tab}`);
  };

  // Render the appropriate content based on the active tab
  const renderContent = () => {
    switch (activeTab) {
      case "relationships":
        return <RelationshipsPage />;
      case "wagov":
        return <WAGovtPage />;
      default:
        return <RadarPage />;
    }
  };

  return (
    <div className="leads-page">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
        Radar
      </h1>

      {/* Tabs navigation */}
      <LeadsTabs activeTab={activeTab} onTabChange={handleTabChange} />

      {/* Tab content */}
      <div className="mt-4">{renderContent()}</div>
    </div>
  );
};

export default LeadsPage;
