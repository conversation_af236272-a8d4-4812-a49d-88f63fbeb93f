import { CustomExpense } from '../../../types';
import { format } from 'date-fns';
import { isXeroSource } from '../../../constants/xero';

/**
 * Check if an expense is from Xero
 *
 * This function determines if an expense originated from Xero (not a payment TO Xero).
 * It uses specific source prefixes to make this determination rather than broad string matching.
 *
 * @param expense The expense to check
 * @returns True if the expense is from Xero
 */
export const isXeroExpense = (expense: CustomExpense): boolean => {
  // Check if the expense has a source field with a specific xero- prefix
  if (expense.source && isXeroSource(expense.source)) {
    return true;
  }

  // Check if the expense has metadata with a specific xero source
  if (expense.metadata?.source && typeof expense.metadata.source === 'string' &&
      isXeroSource(expense.metadata.source)) {
    return true;
  }

  // Check if the expense has an _source field with a specific xero type
  if (expense._source?.type && typeof expense._source.type === 'string' &&
      (expense._source.type === 'xero' || expense._source.type.startsWith('xero_'))) {
    return true;
  }

  // Check for metadata that explicitly marks this as from Xero
  if (expense.metadata?.isFromXero === true) {
    return true;
  }

  return false;
};

/**
 * Check if an expense is a GST Payment (BAS) expense from Xero
 *
 * This function first verifies that the expense is from Xero using the isXeroExpense function,
 * then checks if it has the specific 'xero-gst' source identifier.
 *
 * Note: We only rely on the source field and not name matching to avoid false positives
 * with manually created expenses that happen to have 'GST' or 'BAS' in their names.
 *
 * @param expense The expense to check
 * @returns True if the expense is a GST Payment (BAS) expense from Xero
 */
export const isGSTExpense = (expense: CustomExpense): boolean => {
  // First check if it's from Xero
  if (!isXeroExpense(expense)) {
    return false;
  }

  // Check if it's a Xero expense with source 'xero-gst'
  if (expense.source === 'xero-gst') {
    return true;
  }

  return false;
};

/**
 * Format currency
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD'
  }).format(value);
};

/**
 * Format date
 */
export const formatDate = (date: Date): string => {
  return format(new Date(date), 'dd MMM yyyy');
};

/**
 * Calculate monthly equivalent amount
 */
export const calculateMonthlyEquivalent = (expense: CustomExpense): number => {
  switch (expense.frequency) {
    case 'weekly':
      return expense.amount * 4.333; // Average weeks per month
    case 'fortnightly':
      return expense.amount * 2.167; // Average fortnights per month (4.333 / 2)
    case 'monthly':
      return expense.amount;
    case 'quarterly':
      return expense.amount / 3;
    case 'one-off':
      // For one-off expenses, the monthly equivalent is 0
      // as they only occur once and aren't part of regular monthly expenses
      return 0;
    default:
      return expense.amount;
  }
};

/**
 * Get expense type label
 */
export const getTypeLabel = (type: CustomExpense['type']): string => {
  switch (type) {
    case 'Monthly Payroll':
      return 'Monthly Payroll';
    case 'Superannuation':
      return 'Superannuation';
    case 'Insurances':
      return 'Insurances';
    case 'Taxes':
      return 'Taxes';
    case 'Subcontractor Fees':
      return 'Subcontractor Fees';
    case 'Rent':
      return 'Rent';
    case 'Reimbursements':
      return 'Reimbursements';
    case 'Professional Fees':
      return 'Professional Fees';
    case 'General Expenses':
      return 'General Expenses';
    case 'Director Distributions':
      return 'Director Distributions';
    case 'Hardware':
      return 'Hardware';
    case 'Subscriptions':
      return 'Subscriptions';
    case 'Other Fees':
      return 'Other Fees';
    case 'Other':
      return 'Other';
    default:
      return type;
  }
};

/**
 * Get frequency label
 */
export const getFrequencyLabel = (frequency: CustomExpense['frequency']): string => {
  switch (frequency) {
    case 'one-off':
      return 'One-off';
    case 'weekly':
      return 'Weekly';
    case 'monthly':
      return 'Monthly';
    case 'quarterly':
      return 'Quarterly';
    default:
      return frequency;
  }
};

/**
 * Get CSS class for expense type
 */
export const getTypeClass = (type: CustomExpense['type']): string => {
  switch (type) {
    case 'Monthly Payroll':
      return 'bg-blue-100 text-blue-800 border-blue-200 dark:border-blue-800/30';
    case 'Superannuation':
      return 'bg-amber-100 text-amber-800 border-amber-200 dark:border-amber-800/30';
    case 'Insurances':
      return 'bg-pink-100 text-pink-800 border-pink-200 dark:border-pink-800/30';
    case 'Taxes':
      return 'bg-indigo-100 text-indigo-800 border-indigo-200 dark:border-indigo-800/30';
    case 'Subcontractor Fees':
      return 'bg-purple-100 text-purple-800 border-purple-200 dark:border-purple-800/30';
    case 'Rent':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:border-emerald-800/30';
    case 'Reimbursements':
      return 'bg-teal-100 text-teal-800 border-teal-200 dark:border-teal-800/30';
    case 'Professional Fees':
      return 'bg-cyan-100 text-cyan-800 border-cyan-200 dark:border-cyan-800/30';
    case 'General Expenses':
      return 'bg-sky-100 text-sky-800 border-sky-200 dark:border-sky-800/30';
    case 'Director Distributions':
      return 'bg-violet-100 text-violet-800 border-violet-200 dark:border-violet-800/30';
    case 'Hardware':
      return 'bg-fuchsia-100 text-fuchsia-800 border-fuchsia-200 dark:border-fuchsia-800/30';
    case 'Subscriptions':
      return 'bg-rose-100 text-rose-800 border-rose-200 dark:border-rose-800/30';
    case 'Other Fees':
      return 'bg-orange-100 text-orange-800 border-orange-200 dark:border-orange-800/30';
    case 'Other':
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:border-gray-600';
  }
};

/**
 * Get CSS class for frequency
 */
export const getFrequencyClass = (frequency: CustomExpense['frequency']): string => {
  switch (frequency) {
    case 'one-off':
      return 'bg-slate-100 text-slate-800 border-slate-200 dark:border-slate-700';
    case 'weekly':
      return 'bg-blue-100 text-blue-800 border-blue-200 dark:border-blue-800/30';
    case 'monthly':
      return 'bg-violet-100 text-violet-800 border-violet-200 dark:border-violet-800/30';
    case 'quarterly':
      return 'bg-orange-100 text-orange-800 border-orange-200 dark:border-orange-800/30';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:border-gray-600';
  }
};