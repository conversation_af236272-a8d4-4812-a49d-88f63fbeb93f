import React from "react";
import { useNavigate } from "react-router-dom";
import { Header } from "./Header";
import { Footer } from "./Footer";
import { UnifiedNavigation } from "../Navigation"; // Import the new unified navigation
import FeedbackButton from "../common/FeedbackButton";

interface MainLayoutProps {
  children: React.ReactNode;
  handleLogout: () => void;
}

export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  handleLogout,
}) => {
  const navigate = useNavigate();

  // Handler for version history navigation
  const handleViewVersionHistory = () => {
    navigate("/version-history");
  };

  return (
    <>
      {/* Header with desktop navigation */}
      <Header handleLogout={handleLogout} />

      {/* Main Content Container */}
      <div className="max-w-7xl mx-auto bg-white dark:bg-gray-800 shadow-sm rounded-lg mb-6 overflow-visible">
        <div className="p-fluid-md sm:p-fluid-lg">{children}</div>
      </div>

      {/* Mobile Navigation - only visible on small screens */}
      <UnifiedNavigation variant="mobile" />

      {/* Feedback Button for bug reports and feature requests */}
      <FeedbackButton />

      <Footer onViewVersionHistory={handleViewVersionHistory} />
    </>
  );
};
