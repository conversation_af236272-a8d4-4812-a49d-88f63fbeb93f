import React from "react";
import { getLatestUpdate, getTimeAgo } from "../../../constants/versionHistory";

interface FooterProps {
  onViewVersionHistory?: () => void;
}

export const Footer: React.FC<FooterProps> = ({ onViewVersionHistory }) => {
  const latestUpdate = getLatestUpdate();
  const version = latestUpdate.version;
  const timeAgo = getTimeAgo(latestUpdate.date);

  return (
    <footer className="max-w-7xl mx-auto text-center text-gray-500 dark:text-gray-400 text-xs pb-20 sm:pb-6">
      <div className="flex flex-col sm:flex-row items-center justify-center space-y-1 sm:space-y-0 sm:space-x-2">
        <p>
          Upstream v{version} &copy; {new Date().getFullYear()}
        </p>
        {onViewVersionHistory && (
          <button
            onClick={onViewVersionHistory}
            className="text-indigo-500 hover:text-indigo-600 dark:text-indigo-400 dark:hover:text-indigo-300 underline focus:outline-none"
          >
            Updated {timeAgo} - View changelog
          </button>
        )}
      </div>
    </footer>
  );
};
