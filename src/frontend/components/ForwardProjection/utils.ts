import { format } from 'date-fns';
import { CashflowForecast, CustomExpense, DailyCashflow, ScenarioCashflowForecast } from '../../../types/financial'; // Direct import path

/**
 * Interface for chart data point
 */
export interface ChartDataPoint {
  date: string;
  balance: number;
  inflows: number;
  outflows: number;
  // Flags and data for significant transactions
  hasSignificantInflow: boolean;
  hasSignificantOutflow: boolean;
  significantInflowAmount?: number;
  significantOutflowAmount?: number;
  significantInflowName?: string;
  significantOutflowName?: string;
  // Staggering values to avoid overlap (0 = normal position, 1 = staggered)
  inflowLabelPosition: number;
  outflowLabelPosition: number;
}

/**
 * Format currency
 * @param value Value to format
 * @returns Formatted currency
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD'
  }).format(value);
};

/**
 * Format date
 * @param dateString Date string
 * @returns Formatted date or placeholder if invalid
 */
export const formatDate = (dateString: string | Date | null | undefined): string => {
  if (!dateString) return '—'; // Em dash for empty dates

  try {
    const date = new Date(dateString);
    // Check if date is valid (Invalid Date objects return NaN for getTime())
    if (isNaN(date.getTime())) return '—';
    return format(date, 'dd MMM yyyy');
  } catch (error) {
    console.warn('Error formatting date:', error, dateString);
    return '—';
  }
};

/**
 * Format chart data
 * @param projectionData Projection data
 * @param significanceThreshold Threshold for significant transactions (defaults to 20000)
 * @returns Formatted chart data
 */
export const formatChartData = (
  projectionData: CashflowForecast | null,
  significanceThreshold: number = 20000
): ChartDataPoint[] => {
  if (!projectionData) return [];

  // First pass to identify significant transactions
  const chartData = projectionData.dailyCashflow!.map((day: DailyCashflow) => {
    // Find significant transactions
    let significantInflow = day.transactions.find(t => t.amount >= significanceThreshold);
    let significantOutflow = day.transactions.find(t => t.amount <= -significanceThreshold);

    // Format transaction names (get project name for invoices, first line of description for others)
    const getTransactionName = (transaction: any) => {
      if (!transaction) return undefined;

      // For invoices from Harvest, use project name
      if (transaction.source === 'harvest' && transaction.type === 'invoice' && transaction.metadata?.projectName) {
        return transaction.metadata.projectName;
      }

      // For all other transactions use description
      const desc = transaction.description || '';
      return desc.split('\n')[0]; // Get first line only
    };

    return {
      date: new Date(day.date).toISOString().split('T')[0], // Store ISO date format for tooltip-table interaction
      balance: day.balance,
      inflows: day.inflows,
      outflows: -day.outflows, // Negate for visualization
      hasSignificantInflow: !!significantInflow,
      hasSignificantOutflow: !!significantOutflow,
      significantInflowAmount: significantInflow?.amount,
      significantOutflowAmount: significantOutflow?.amount,
      significantInflowName: getTransactionName(significantInflow),
      significantOutflowName: getTransactionName(significantOutflow),
      // Will be used for staggering labels to avoid overlap
      inflowLabelPosition: 0,
      outflowLabelPosition: 0
    };
  });

  // More sophisticated approach to stagger labels and avoid overlaps
  // We'll check multiple days in a window to handle dense clusters of transactions
  // We'll use up to 3 different positions for more flexibility

  // First reset all positions to default
  chartData.forEach(day => {
    day.inflowLabelPosition = 0;
    day.outflowLabelPosition = 0;
  });

  // Look ahead to handle clusters of significant transactions
  const windowSize = 4; // Check ahead and behind for better distribution - increased for more spacing

  for (let i = 0; i < chartData.length; i++) {
    const currDay = chartData[i];

    // Skip days without significant transactions
    if (!currDay.hasSignificantInflow && !currDay.hasSignificantOutflow) continue;

    // For inflows - check proximity of other inflows
    if (currDay.hasSignificantInflow) {
      // Count nearby inflows in window
      let nearbyInflows = 0;
      let positionsUsed = new Set<number>();

      // Look back
      for (let j = Math.max(0, i-windowSize); j < i; j++) {
        if (chartData[j].hasSignificantInflow) {
          nearbyInflows++;
          positionsUsed.add(chartData[j].inflowLabelPosition);
        }
      }

      // Look ahead
      for (let j = i+1; j < Math.min(chartData.length, i+windowSize+1); j++) {
        if (chartData[j].hasSignificantInflow) {
          nearbyInflows++;
        }
      }

      // If we have nearby inflows, distribute positions
      if (nearbyInflows > 0) {
        // Find first unused position (0, 1, or 2)
        for (let pos = 0; pos <= 2; pos++) {
          if (!positionsUsed.has(pos)) {
            currDay.inflowLabelPosition = pos;
            break;
          }
        }
      }
    }

    // For outflows - similar approach
    if (currDay.hasSignificantOutflow) {
      // Count nearby outflows in window
      let nearbyOutflows = 0;
      let positionsUsed = new Set<number>();

      // Look back
      for (let j = Math.max(0, i-windowSize); j < i; j++) {
        if (chartData[j].hasSignificantOutflow) {
          nearbyOutflows++;
          positionsUsed.add(chartData[j].outflowLabelPosition);
        }
      }

      // Look ahead
      for (let j = i+1; j < Math.min(chartData.length, i+windowSize+1); j++) {
        if (chartData[j].hasSignificantOutflow) {
          nearbyOutflows++;
        }
      }

      // If we have nearby outflows, distribute positions
      if (nearbyOutflows > 0) {
        // Find first unused position (0, 1, or 2)
        for (let pos = 0; pos <= 2; pos++) {
          if (!positionsUsed.has(pos)) {
            currDay.outflowLabelPosition = pos;
            break;
          }
        }
      }
    }
  }

  return chartData;
};


/**
 * Calculate monthly equivalent
 * @param expenses Expenses array
 * @returns Monthly equivalent
 */
export const calculateMonthlyEquivalent = (expenses: CustomExpense[]): number => {
  return expenses.reduce((sum, expense) => {
    switch (expense.frequency) {
      case 'weekly':
        return sum + (expense.amount * 4.333); // Average weeks per month
      case 'fortnightly':
        return sum + (expense.amount * 2.167); // Average fortnights per month (4.333 / 2)
      case 'monthly':
        return sum + expense.amount;
      case 'quarterly':
        return sum + (expense.amount / 3);
      case 'one-off':
        // For one-off expenses, the monthly equivalent is 0
        // as they only occur once and aren't part of regular monthly expenses
        return sum;
      default:
        return sum + expense.amount;
    }
  }, 0);
};

/**
 * Calculate percentage change between two values
 * @param currentValue Current value
 * @param previousValue Previous value
 * @returns Percentage change as a number (e.g., 5.2 for 5.2%)
 */
export const calculatePercentageChange = (currentValue: number, previousValue: number): number => {
  // Handle edge cases
  if (previousValue === 0) return currentValue > 0 ? 100 : 0; // Avoid division by zero
  if (currentValue === previousValue) return 0; // Explicitly handle no change

  // Calculate percentage change
  return ((currentValue - previousValue) / Math.abs(previousValue)) * 100;
};

/**
 * Extended chart data point with scenario data
 */
export interface ScenarioChartDataPoint extends ChartDataPoint {
  expectedCaseBalance: number;
  bestCaseBalance: number;
  dealImpact: number;
}

/**
 * Format chart data with scenario projections
 *
 * @param projectionData Projection data with scenarios
 * @param significanceThreshold Threshold for significant transactions
 * @returns Formatted chart data for scenarios
 */
export const formatScenarioChartData = (
  projectionData: ScenarioCashflowForecast | null,
  significanceThreshold: number = 20000
): ScenarioChartDataPoint[] => {
  if (!projectionData || !projectionData.scenarios) return [];

  // Get base chart data from worst case (confirmed transactions only)
  const baseChartData = formatChartData(projectionData);

  // Add scenario data to each day
  return baseChartData.map((day, index) => {
    // Get corresponding data points from scenarios
    const expectedDay = projectionData.scenarios.expectedCase.dailyCashflow[index];
    const bestDay = projectionData.scenarios.bestCase.dailyCashflow[index];

    return {
      ...day,
      // Add scenario balance values
      expectedCaseBalance: expectedDay ? expectedDay.balance : day.balance,
      bestCaseBalance: bestDay ? bestDay.balance : day.balance,
      // Add deal impact data for tooltips
      dealImpact: bestDay && day.balance !== bestDay.balance
        ? bestDay.balance - day.balance
        : 0
    };
  });
};
