import React, { useState, useLayoutEffect, useMemo, useCallback } from "react"; // Added useMemo and useCallback
import { format } from "date-fns";
import {
  // LineChart, // Removed LineChart
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  ComposedChart,
  ReferenceLine,
  Customized,
  // Removed Brush import
} from "recharts";

// Create component aliases with any type to work around TypeScript issues
const RechartLineSeries = Line as any;
const RechartXAxis = XAxis as any;
const RechartYAxis = YAxis as any;
const RechartGrid = CartesianGrid as any;
const RechartTooltip = Tooltip as any;
const RechartLegend = Legend as any; // Use the alias for consistency
const RechartContainer = ResponsiveContainer as any;
const RechartArea = Area as any;
const RechartComposed = ComposedChart as any;
const RechartReferenceLine = ReferenceLine as any;
const RechartCustomized = Customized as any;
// Removed Brush alias
import {
  useProjection,
  formatChartData,
  formatScenarioChartData,
  ChartTooltip,
  ScenarioChartDataPoint,
} from ".";
import ScenarioToggle from "./ScenarioToggle";

// Removed useResponsiveSize hook - using container queries and Tailwind responsive utilities instead

interface CashflowChartProps {
  labelsVisible?: boolean;
  height?: string;
  minHeight?: string;
  maxHeight?: string;
}

/**
 * Component for cashflow chart
 * Uses container queries for component-level adaptivity
 */
const CashflowChart = ({
  labelsVisible = true,
  height = "40vh",
  minHeight = "220px",
  maxHeight = "350px",
}: CashflowChartProps) => {
  // Get projection data and the setter for highlightedDate from context
  const {
    projectionData,
    scenarioProjectionData,
    showScenarios,
    setShowScenarios,
    setHighlightedDate,
  } = useProjection();

  if (!projectionData) return null;

  // Format data including scenarios if available
  // Use useMemo to prevent unnecessary recalculations
  const chartData = useMemo(() => {
    console.log("Recalculating chart data...");
    return showScenarios && scenarioProjectionData
      ? formatScenarioChartData(scenarioProjectionData)
      : formatChartData(projectionData);
  }, [showScenarios, scenarioProjectionData, projectionData]);

  // Define payload for the recharts legend - memoize to prevent unnecessary re-renders
  const legendPayload = useMemo(
    () => [
      { value: "Balance", type: "square", id: "balance", color: "#3498db" }, // Match Area stroke color
      { value: "Money In", type: "square", id: "inflows", color: "#27ae60" }, // Match Line stroke color
      { value: "Money Out", type: "square", id: "outflows", color: "#e74c3c" }, // Match Line stroke color
      // Add these items when showing scenarios
      ...(showScenarios
        ? [
            {
              value: "Expected Case",
              type: "square",
              id: "expected",
              color: "rgba(52, 152, 219, 0.5)",
            },
            {
              value: "Best Case",
              type: "square",
              id: "best",
              color: "rgba(52, 152, 219, 0.3)",
            },
          ]
        : []),
    ],
    [showScenarios]
  );

  // Handler for chart clicks - now attached to the ComposedChart
  // Memoize to prevent unnecessary re-renders
  const handleChartClick = useCallback(
    (data: any) => {
      // Check if the click event provides the active payload
      if (data && data.activePayload && data.activePayload.length > 0) {
        const clickedDate = data.activePayload[0]?.payload?.date;
        // console.log('Setting highlightedDate from chart click:', clickedDate); // Keep commented
        setHighlightedDate(clickedDate || null);
      } else {
        // Clear highlight if clicking outside a data point or data is unexpected
        // console.log('Clearing highlightedDate (click data invalid or outside)'); // Keep commented
        setHighlightedDate(null);
      }
    },
    [setHighlightedDate]
  );

  return (
    <div
      className="chart-container mb-0 overflow-visible relative w-full flex justify-center"
      style={{
        height: "46vh", // Increased from 40vh (15% taller)
        minHeight: "290px", // Increased from 250px (15% taller)
        maxHeight: "460px", // Increased from 400px (15% taller)
        position: "relative", // Ensure proper stacking context
      }}
    >
      <RechartContainer width="100%" height="100%" style={{ maxWidth: "100%" }}>
        <RechartComposed
          data={chartData}
          margin={{
            // Minimal padding on all sides
            top: 20,
            right: 10,
            left: 10,
            bottom: 5, // Reduced from 20 to 5
          }}
          onClick={handleChartClick} // Attach onClick to the chart itself
        >
          {/* Add the built-in Legend */}
          <RechartLegend
            payload={legendPayload}
            verticalAlign="top"
            align="right"
            wrapperStyle={{
              position: "absolute", // Position absolutely relative to parent
              top: "0px", // Align to the top
              right: "10px", // Mobile-first margin
              paddingTop: "3px", // Add some padding from the top edge
              fontSize: "10px", // Smaller text size for mobile
              lineHeight: "14px", // Adjust line height for spacing
            }}
            iconSize={8} // Smaller icon size for mobile
          />

          {/* Grid with mobile-first styling */}
          <RechartGrid
            strokeDasharray="3 3"
            stroke="rgba(204, 204, 204, 0.3)"
            horizontal={true}
            vertical={false} // Hide vertical grid on mobile for cleaner look
            horizontalPoints={[0, 25, 50, 75, 100]} // Simplified grid for mobile
          />
          <RechartXAxis
            dataKey="date"
            tick={{
              fontSize: 10, // Mobile-first size
              fill: "currentColor",
              opacity: 0.8,
            }}
            interval="preserveEnd" // Mobile-first setting - fewer ticks
            tickFormatter={(value) => {
              // Mobile-first date formatting - shorter format
              return format(new Date(value), "d MMM");
            }}
            axisLine={{ stroke: "rgba(204, 204, 204, 0.5)" }}
            tickLine={{ stroke: "rgba(204, 204, 204, 0.5)" }}
            height={20} // Reduced from 30 to 20
            dy={3} // Reduced from 5 to 3
          />
          <RechartYAxis
            tickFormatter={(value) => {
              // Mobile-first currency format - more compact
              return new Intl.NumberFormat("en-AU", {
                style: "currency",
                currency: "AUD",
                notation: "compact",
                maximumFractionDigits: 0, // No decimals on mobile
              }).format(value);
            }}
            width={45} // Minimal width to save horizontal space
            tick={{
              fontSize: 10, // Mobile-first font size
              fill: "currentColor",
              opacity: 0.8,
            }}
            axisLine={{ stroke: "rgba(204, 204, 204, 0.5)" }}
            tickLine={{ stroke: "rgba(204, 204, 204, 0.5)" }}
            tickCount={4} // Mobile-first tick count - fewer ticks
          />
          <RechartTooltip
            content={<ChartTooltip />}
            cursor={{ strokeWidth: 1 }} // Mobile-first stroke width
            wrapperStyle={{
              outline: "none",
              pointerEvents: "all",
              zIndex: 999999, // Increased z-index to ensure it appears above everything
              overflow: "visible", // Changed to visible to prevent scrolling
              // Wider tooltip to reduce text wrapping
              minWidth: "450px", // Increased width to ensure single row layout works
              maxWidth: "95%", // Responsive width
              maxHeight: "none", // No height restriction to prevent scrolling
              display: "block",
              filter: "drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))", // Add shadow for better visibility
            }}
            allowEscapeViewBox={{ x: false, y: false }} // Keep tooltip within chart boundaries
            position="right" // Position tooltip to the right of the cursor
            isAnimationActive={false}
            viewBox={{ x: 0, y: 0, width: "100%", height: "100%" }} // Define viewBox for proper positioning
          />

          {/* Reference line for zero balance */}
          <RechartReferenceLine
            y={0}
            stroke="rgba(102, 102, 102, 0.5)"
            strokeDasharray="3 3"
            strokeOpacity={0.5}
          />

          {/* Area and Line for Balance */}
          <defs>
            <linearGradient id="balanceGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#3498db" stopOpacity={0.2} />
              <stop offset="95%" stopColor="#3498db" stopOpacity={0} />
            </linearGradient>
          </defs>
          <RechartArea
            type="monotone"
            dataKey="balance"
            name="Balance"
            fill="url(#balanceGradient)"
            stroke="#3498db" // Secondary color
            activeDot={{
              r: 6, // Mobile-first size
              strokeWidth: 0,
              fill: "#3498db",
            }}
            strokeWidth={2} // Mobile-first width
            dot={false}
            isAnimationActive={false} // Disable animations on mobile for better performance
            // Removed onClick handler from Area
          />

          {/* Add scenario areas when showScenarios is true */}
          {showScenarios && (
            <>
              <defs>
                <linearGradient
                  id="expectedCaseGradient"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor="#3498db" stopOpacity={0.4} />
                  <stop offset="95%" stopColor="#3498db" stopOpacity={0.1} />
                </linearGradient>
                <linearGradient
                  id="bestCaseGradient"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor="#3498db" stopOpacity={0.2} />
                  <stop offset="95%" stopColor="#3498db" stopOpacity={0.05} />
                </linearGradient>
              </defs>

              {/* Expected case area */}
              <RechartArea
                type="monotone"
                dataKey="expectedCaseBalance"
                name="Expected Case"
                fill="url(#expectedCaseGradient)"
                stroke="rgba(52, 152, 219, 0.5)"
                strokeWidth={1}
                activeDot={false}
                dot={false}
                isAnimationActive={false} // Disable animations on mobile for better performance
                fillOpacity={0.6}
              />

              {/* Best case area */}
              <RechartArea
                type="monotone"
                dataKey="bestCaseBalance"
                name="Best Case"
                fill="url(#bestCaseGradient)"
                stroke="rgba(52, 152, 219, 0.3)"
                strokeWidth={1}
                activeDot={false}
                dot={false}
                isAnimationActive={false} // Disable animations on mobile for better performance
                fillOpacity={0.3}
              />
            </>
          )}

          {/* Lines for Money In and Money Out */}
          <RechartLineSeries
            type="monotone"
            dataKey="inflows"
            name="Money In"
            stroke="#27ae60" // Success color
            strokeWidth={1.5} // Mobile-first width
            strokeDasharray="3 3" // Mobile-first dash pattern
            dot={(props) => {
              // Only render dots for significant inflows
              const { cx, cy, payload } = props;
              if (payload.hasSignificantInflow) {
                return (
                  <g key={`inflow-dot-${payload.date}`}>
                    <circle
                      cx={cx}
                      cy={cy}
                      r={3} // Mobile-first radius
                      fill="#27ae60"
                    />
                    {/* Minimal indicator for significant inflow */}
                    {labelsVisible && (
                      <g key={`inflow-label-${payload.date}`}>
                        {/* Small marker line */}
                        <line
                          key={`inflow-line-${payload.date}`}
                          x1={cx}
                          y1={cy}
                          x2={cx}
                          y2={
                            payload.inflowLabelPosition === 0
                              ? cy - 8
                              : payload.inflowLabelPosition === 1
                              ? cy - 20
                              : cy - 32
                          }
                          stroke="#27ae60"
                          strokeWidth={1}
                          strokeDasharray="1 1"
                        />
                        {/* Transaction name and amount */}
                        <text
                          key={`inflow-text-${payload.date}`}
                          x={cx}
                          y={
                            payload.inflowLabelPosition === 0
                              ? cy - 10
                              : payload.inflowLabelPosition === 1
                              ? cy - 22
                              : cy - 34
                          }
                          textAnchor="middle"
                          fill="#27ae60"
                          fontSize={8}
                          fontWeight="500"
                        >
                          {`${payload.significantInflowName?.substring(0, 10)}${
                            payload.significantInflowName?.length > 10
                              ? ".."
                              : ""
                          } $${(
                            Math.abs(payload.significantInflowAmount || 0) /
                            1000
                          ).toFixed(0)}k`}
                        </text>
                      </g>
                    )}
                  </g>
                );
              }
              return null;
            }}
            activeDot={{
              r: 4, // Mobile-first radius
              strokeWidth: 0,
              fill: "#27ae60",
            }}
            isAnimationActive={false} // Disable animations on mobile for better performance
          />
          <RechartLineSeries
            type="monotone"
            dataKey="outflows"
            name="Money Out"
            stroke="#e74c3c" // Accent color
            strokeWidth={1.5} // Mobile-first width
            strokeDasharray="3 3" // Mobile-first dash pattern
            dot={(props) => {
              // Only render dots for significant outflows
              const { cx, cy, payload } = props;
              if (payload.hasSignificantOutflow) {
                return (
                  <g key={`outflow-dot-${payload.date}`}>
                    <circle
                      cx={cx}
                      cy={cy}
                      r={3} // Mobile-first radius
                      fill="#e74c3c"
                    />
                    {/* Minimal indicator for significant outflow */}
                    {labelsVisible && (
                      <g key={`outflow-label-${payload.date}`}>
                        {/* Small marker line */}
                        <line
                          key={`outflow-line-${payload.date}`}
                          x1={cx}
                          y1={cy}
                          x2={cx}
                          y2={
                            payload.outflowLabelPosition === 0
                              ? cy + 8
                              : payload.outflowLabelPosition === 1
                              ? cy + 20
                              : cy + 32
                          }
                          stroke="#e74c3c"
                          strokeWidth={1}
                          strokeDasharray="1 1"
                        />
                        {/* Transaction name and amount */}
                        <text
                          key={`outflow-text-${payload.date}`}
                          x={cx}
                          y={
                            payload.outflowLabelPosition === 0
                              ? cy + 16
                              : payload.outflowLabelPosition === 1
                              ? cy + 28
                              : cy + 40
                          }
                          textAnchor="middle"
                          fill="#e74c3c"
                          fontSize={8}
                          fontWeight="500"
                        >
                          {`${payload.significantOutflowName?.substring(0, 8)}${
                            payload.significantOutflowName?.length > 8
                              ? ".."
                              : ""
                          } $${(
                            Math.abs(payload.significantOutflowAmount || 0) /
                            1000
                          ).toFixed(0)}k`}
                        </text>
                      </g>
                    )}
                  </g>
                );
              }
              return null;
            }}
            activeDot={{
              r: 4, // Mobile-first radius
              strokeWidth: 0,
              fill: "#e74c3c",
            }}
            isAnimationActive={false} // Disable animations on mobile for better performance
          />
          {/* Removed Brush component */}
        </RechartComposed>
      </RechartContainer>
    </div>
  );
};

// Create a custom hook to manage label visibility
export const useLabelVisibility = () => {
  const [labelsVisible, setLabelsVisible] = useState(true);

  const toggleLabels = () => {
    setLabelsVisible(!labelsVisible);
  };

  return { labelsVisible, toggleLabels };
};

export default CashflowChart;
