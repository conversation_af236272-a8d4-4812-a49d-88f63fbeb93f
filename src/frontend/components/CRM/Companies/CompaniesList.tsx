import React, { useState } from "react";
import { Company } from "../../../types/crm-types";
import { useQuery } from "react-query";
import { getCompanies } from "../../../api/crm";
import CompanyCard from "./CompanyCard";
import CompanyDetail from "./CompanyDetail";

/**
 * Component for displaying and managing companies
 */
const CompaniesList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);

  // Fetch companies using React Query
  const {
    data: companies = [],
    isLoading,
    error,
  } = useQuery("companies", getCompanies);

  // Filter companies based on search term
  const filteredCompanies = companies.filter((company) => {
    const name = company.name.toLowerCase();
    const industry = (company.industry || "").toLowerCase();
    const website = (company.website || "").toLowerCase();
    const search = searchTerm.toLowerCase();

    // Check in related company names (parent companies)
    let matchesParentCompany = false;
    if (company.parentCompanies && company.parentCompanies.length > 0) {
      matchesParentCompany = company.parentCompanies.some((rel) =>
        (rel.company.name || "").toLowerCase().includes(search)
      );
    }

    // Check in related company names (child companies)
    let matchesChildCompany = false;
    if (company.childCompanies && company.childCompanies.length > 0) {
      matchesChildCompany = company.childCompanies.some((rel) =>
        (rel.company.name || "").toLowerCase().includes(search)
      );
    }

    return (
      name.includes(search) ||
      industry.includes(search) ||
      website.includes(search) ||
      matchesParentCompany ||
      matchesChildCompany
    );
  });

  // Handle selecting a company for detailed view
  const handleSelectCompany = (company: Company) => {
    setSelectedCompany(company);
  };

  // Close the detail view
  const handleCloseDetail = () => {
    setSelectedCompany(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          Companies
        </h1>
      </div>

      {/* Search bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg
            className="h-5 w-5 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <input
          type="text"
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder="Search companies..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Company detail modal */}
      {selectedCompany && (
        <CompanyDetail company={selectedCompany} onClose={handleCloseDetail} />
      )}

      {/* Main content */}
      {isLoading ? (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline">
            {" "}
            Failed to load companies. Please try again later.
          </span>
        </div>
      ) : filteredCompanies.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredCompanies.map((company) => (
            <CompanyCard
              key={company.id}
              company={company}
              onClick={() => handleSelectCompany(company)}
            />
          ))}
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="text-center py-12">
            <svg
              className="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
            <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">
              {searchTerm ? "No companies found" : "No companies yet"}
            </h3>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {searchTerm
                ? `No companies match "${searchTerm}". Try a different search term.`
                : "No companies available."}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompaniesList;
