import React, { useState } from "react";
import { DealCreate, DealStage, DealPriority } from "../../../types/crm-types";
import { useMutation, useQueryClient } from "react-query";
import { createDeal } from "../../../api/crm";
import { getRetryableErrorMessage } from "../../../utils/error-helpers";
import {
  Input,
  Select,
  Textarea,
  FormSection,
  FormGrid,
} from "../../shared/forms";
import { Button } from "../../shared/Button";
import {
  CurrencyDollarIcon,
  CalendarIcon,
  ChartBarIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";

interface DealFormProps {
  onClose: () => void;
}

/**
 * Form component for creating a new deal
 * Now using the modern form system with enhanced UX
 */
const DealForm: React.FC<DealFormProps> = ({ onClose }) => {
  const [formData, setFormData] = useState<DealCreate>({
    name: "",
    stage: "Lead",
    value: undefined,
    currency: "AUD",
    probability: undefined,
    expectedCloseDate: undefined,
    description: "",
    priority: undefined,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const queryClient = useQueryClient();

  // Mutation for creating a deal
  const createDealMutation = useMutation(
    (data: DealCreate) => createDeal(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("deals");
        onClose();
      },
      onError: (error: any) => {
        // Handle API errors
        if (error?.response?.data?.errors) {
          setErrors(error.response.data.errors);
        } else {
          const retryMessage = getRetryableErrorMessage(error, 'create deal');
          setErrors({ general: retryMessage });
        }
      },
    }
  );

  // Validation function
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Deal name is required";
    }

    if (!formData.stage) {
      newErrors.stage = "Stage is required";
    }

    if (formData.value !== undefined && formData.value < 0) {
      newErrors.value = "Value must be positive";
    }

    if (
      formData.probability !== undefined &&
      (formData.probability < 0 || formData.probability > 1)
    ) {
      newErrors.probability = "Probability must be between 0% and 100%";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;

    // Handle number inputs
    if (type === "number") {
      if (name === "probability") {
        // Convert percentage (0-100) to decimal (0-1) for storage
        setFormData({
          ...formData,
          [name]: value === "" ? undefined : Number(value) / 100,
        });
      } else {
        setFormData({
          ...formData,
          [name]: value === "" ? undefined : Number(value),
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value === "" ? undefined : value,
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setErrors({});

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Submit form
    createDealMutation.mutate(formData);
  };

  // Define form options
  const stageOptions = [
    { value: "Lead", label: "Lead" },
    { value: "Qualified", label: "Qualified" },
    { value: "Proposal", label: "Proposal" },
    { value: "Negotiation", label: "Negotiation" },
    { value: "Closed Won", label: "Closed Won" },
    { value: "Closed Lost", label: "Closed Lost" },
  ];

  const currencyOptions = [
    { value: "AUD", label: "AUD" },
    { value: "USD", label: "USD" },
    { value: "EUR", label: "EUR" },
    { value: "GBP", label: "GBP" },
  ];

  const priorityOptions = [
    { value: "", label: "None" },
    { value: "Low", label: "Low" },
    { value: "Medium", label: "Medium" },
    { value: "High", label: "High" },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full overflow-hidden">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Create New Deal
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* General error message */}
          {errors.general && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
              <div className="text-sm text-red-600 dark:text-red-400">
                {errors.general}
              </div>
            </div>
          )}

          <FormSection title="Deal Information">
            <FormGrid cols={1}>
              <Input
                label="Deal Name"
                placeholder="Enter deal name"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                error={errors.name}
                required
                loading={createDealMutation.isLoading}
              />

              <Select
                label="Stage"
                placeholder="Select deal stage"
                options={stageOptions}
                value={formData.stage}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    stage: e.target.value as DealStage,
                  })
                }
                error={errors.stage}
                required
                loading={createDealMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          <FormSection title="Financial Details">
            <FormGrid cols={2}>
              <Input
                label="Value"
                type="number"
                placeholder="0.00"
                icon={<CurrencyDollarIcon className="h-5 w-5" />}
                value={
                  formData.value === undefined ? "" : formData.value.toString()
                }
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    value:
                      e.target.value === ""
                        ? undefined
                        : Number(e.target.value),
                  })
                }
                error={errors.value}
                helpText="Deal value in selected currency"
                loading={createDealMutation.isLoading}
              />

              <Select
                label="Currency"
                options={currencyOptions}
                value={formData.currency || "AUD"}
                onChange={(e) =>
                  setFormData({ ...formData, currency: e.target.value })
                }
                loading={createDealMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          <FormSection title="Deal Metrics">
            <FormGrid cols={2}>
              <Input
                label="Probability (%)"
                type="number"
                min="0"
                max="100"
                placeholder="0-100"
                icon={<ChartBarIcon className="h-5 w-5" />}
                value={
                  formData.probability === undefined
                    ? ""
                    : Math.round(formData.probability * 100).toString()
                }
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    probability:
                      e.target.value === ""
                        ? undefined
                        : Number(e.target.value) / 100,
                  })
                }
                error={errors.probability}
                helpText="Likelihood of closing this deal"
                loading={createDealMutation.isLoading}
              />

              <Select
                label="Priority"
                placeholder="Select priority"
                options={priorityOptions}
                value={formData.priority || ""}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    priority:
                      e.target.value === ""
                        ? undefined
                        : (e.target.value as DealPriority),
                  })
                }
                helpText="Deal priority level"
                loading={createDealMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          <FormSection title="Additional Details">
            <FormGrid cols={1}>
              <Input
                label="Expected Close Date"
                type="date"
                icon={<CalendarIcon className="h-5 w-5" />}
                value={formData.expectedCloseDate || ""}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    expectedCloseDate:
                      e.target.value === "" ? undefined : e.target.value,
                  })
                }
                helpText="When do you expect to close this deal?"
                loading={createDealMutation.isLoading}
              />

              <Textarea
                label="Description"
                placeholder="Enter deal description..."
                icon={<DocumentTextIcon className="h-5 w-5" />}
                value={formData.description || ""}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                rows={3}
                maxLength={500}
                showCharCount={true}
                helpText="Provide details about this deal"
                loading={createDealMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          {/* Form actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="secondary"
              onClick={onClose}
              disabled={createDealMutation.isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={createDealMutation.isLoading}
              loadingText="Creating..."
            >
              Create Deal
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DealForm;
