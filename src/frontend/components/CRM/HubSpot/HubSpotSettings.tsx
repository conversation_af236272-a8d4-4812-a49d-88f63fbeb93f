import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { checkHubSpotStatus, configureHubSpot, removeHubSpotConfiguration } from '../../../api/hubspot';

/**
 * Component for configuring HubSpot integration
 */
const HubSpotSettings: React.FC = () => {
  const [accessToken, setAccessToken] = useState('');
  const [showToken, setShowToken] = useState(false);
  const queryClient = useQueryClient();

  // Fetch HubSpot status
  const { data: hubspotStatus, isLoading: isStatusLoading } = useQuery(
    'hubspotStatus',
    checkHubSpotStatus
  );

  // Mutation for configuring HubSpot
  const configureHubSpotMutation = useMutation(
    (token: string) => configureHubSpot(token),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('hubspotStatus');
        setAccessToken('');
      }
    }
  );

  // Mutation for removing HubSpot configuration
  const removeHubSpotMutation = useMutation(
    () => removeHubSpotConfiguration(),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('hubspotStatus');
      }
    }
  );

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (accessToken.trim()) {
      configureHubSpotMutation.mutate(accessToken);
    }
  };

  // Handle removing HubSpot configuration
  const handleRemoveConfiguration = () => {
    if (window.confirm('Are you sure you want to remove the HubSpot integration? This will delete your access token.')) {
      removeHubSpotMutation.mutate();
    }
  };

  // Toggle token visibility
  const toggleShowToken = () => {
    setShowToken(!showToken);
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
        HubSpot Integration
      </h2>
      
      {isStatusLoading ? (
        <div className="flex justify-center items-center h-24">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : hubspotStatus?.isConfigured ? (
        <div className="space-y-4">
          <div className="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                  HubSpot integration is configured
                </h3>
                <p className="mt-2 text-sm text-green-700 dark:text-green-300">
                  Your HubSpot account is connected. You can now import data from HubSpot.
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end">
            <button
              type="button"
              onClick={handleRemoveConfiguration}
              className="px-4 py-2 border border-red-300 dark:border-red-700 rounded-md shadow-sm text-sm font-medium text-red-700 dark:text-red-200 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              disabled={removeHubSpotMutation.isLoading}
            >
              {removeHubSpotMutation.isLoading ? 'Removing...' : 'Remove Integration'}
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Connect your HubSpot account to import deals, contacts, and companies.
          </p>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="accessToken" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                HubSpot Private App Access Token
              </label>
              <div className="relative">
                <input
                  type={showToken ? 'text' : 'password'}
                  id="accessToken"
                  name="accessToken"
                  value={accessToken}
                  onChange={(e) => setAccessToken(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="pat-na1-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
                  required
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  onClick={toggleShowToken}
                >
                  {showToken ? (
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                      <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                    </svg>
                  )}
                </button>
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Create a Private App in HubSpot and generate an access token with the required scopes.
              </p>
            </div>
            
            <div className="flex justify-end">
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                disabled={configureHubSpotMutation.isLoading || !accessToken.trim()}
              >
                {configureHubSpotMutation.isLoading ? 'Configuring...' : 'Configure HubSpot'}
              </button>
            </div>
          </form>
          
          <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              How to create a HubSpot Private App
            </h3>
            <ol className="list-decimal list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>Go to your HubSpot account settings</li>
              <li>Navigate to Integrations &gt; Private Apps</li>
              <li>Click "Create private app"</li>
              <li>Name your app (e.g., "Onbord Integration")</li>
              <li>Select the following scopes:
                <ul className="list-disc list-inside ml-4 mt-1">
                  <li>crm.objects.contacts.read</li>
                  <li>crm.objects.companies.read</li>
                  <li>crm.objects.deals.read</li>
                </ul>
              </li>
              <li>Create the app and copy the access token</li>
              <li>Paste the token in the field above</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
};

export default HubSpotSettings;
