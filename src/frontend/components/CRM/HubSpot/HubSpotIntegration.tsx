import React, { useState } from 'react';
import HubSpotSettings from './HubSpotSettings';
import HubSpotImport from './HubSpotImport';

/**
 * Main component for HubSpot integration
 */
const HubSpotIntegration: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'settings' | 'import'>('settings');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          HubSpot Integration
        </h1>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'settings'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
            onClick={() => setActiveTab('settings')}
          >
            Settings
          </button>
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'import'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
            onClick={() => setActiveTab('import')}
          >
            Import Data
          </button>
        </nav>
      </div>

      {/* Tab content */}
      <div>
        {activeTab === 'settings' && <HubSpotSettings />}
        {activeTab === 'import' && <HubSpotImport />}
      </div>
    </div>
  );
};

export default HubSpotIntegration;
