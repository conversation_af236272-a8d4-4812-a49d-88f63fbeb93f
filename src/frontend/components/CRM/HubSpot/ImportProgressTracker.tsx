import React, { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';

interface ImportProgress {
  step: 'companies' | 'deals' | 'contacts';
  current: number;
  total: number;
  currentItem?: string;
  errors: Array<{ item: string; error: string }>;
}

interface ImportResult {
  success: boolean;
  count: number;
  errors: Array<{ item: string; error: string }>;
  updates: Array<{ item: string; changes: string[] }>;
  created: Array<{ item: string }>;
  error?: string;
}

interface ImportProgressTrackerProps {
  isImporting: boolean;
  onComplete?: (results: {
    totalCount: number;
    results: {
      companies: ImportResult;
      deals: ImportResult;
      contacts: ImportResult;
    };
  }) => void;
}

const ImportProgressTracker: React.FC<ImportProgressTrackerProps> = ({
  isImporting,
  onComplete
}) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [progress, setProgress] = useState<ImportProgress | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  const [connectionError, setConnectionError] = useState(false);

  useEffect(() => {
    if (!isImporting) {
      // Clean up socket if import is not running
      if (socket) {
        socket.disconnect();
        setSocket(null);
      }
      setIsComplete(true);
      return;
    }

    // Reset states when starting new import
    setIsComplete(false);
    setConnectionError(false);
    setProgress(null);

    // Create socket connection with error handling
    // In development, we need to connect to the backend port
    const socketUrl = window.location.hostname === 'localhost' 
      ? 'http://localhost:3002' 
      : window.location.origin;
    
    console.log('Connecting to Socket.IO at:', socketUrl);
    
    const newSocket = io(socketUrl, {
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 1000,
      timeout: 5000,
      transports: ['polling', 'websocket'], // Start with polling for better compatibility
    });

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('Connected to import progress updates');
      setConnectionError(false);
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setConnectionError(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from import progress updates');
    });

    // Progress update handler
    newSocket.on('hubspot-import-progress', (progressData: ImportProgress) => {
      console.log('Received progress update:', progressData);
      setProgress(progressData);
    });

    setSocket(newSocket);

    // Cleanup
    return () => {
      newSocket.disconnect();
    };
  }, [isImporting]);

  if (!isImporting && !isComplete) {
    return null;
  }

  const getStepName = (step: string) => {
    switch (step) {
      case 'companies': return 'Companies';
      case 'deals': return 'Deals';
      case 'contacts': return 'Contacts';
      default: return step;
    }
  };

  const getProgressPercentage = () => {
    if (!progress || progress.total === 0) return 0;
    return Math.round((progress.current / progress.total) * 100);
  };

  const getOverallProgress = () => {
    if (!progress) return 0;
    
    const stepWeights = { companies: 0.3, deals: 0.5, contacts: 0.2 };
    const currentStepWeight = stepWeights[progress.step];
    const stepProgress = progress.total > 0 ? (progress.current / progress.total) : 0;
    
    let overallProgress = 0;
    if (progress.step === 'companies') {
      overallProgress = stepProgress * stepWeights.companies;
    } else if (progress.step === 'deals') {
      overallProgress = stepWeights.companies + (stepProgress * stepWeights.deals);
    } else if (progress.step === 'contacts') {
      overallProgress = stepWeights.companies + stepWeights.deals + (stepProgress * stepWeights.contacts);
    }
    
    return Math.round(overallProgress * 100);
  };

  return (
    <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6 shadow-sm">
      <div className="space-y-4">
        {/* Connection Error Alert */}
        {connectionError && (
          <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
            <div className="flex items-center">
              <svg className="h-4 w-4 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">
                Unable to connect for real-time updates. Import is still running in the background.
              </p>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {isComplete ? 'Import Complete' : 'Importing HubSpot Data'}
          </h3>
          {!isComplete && (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {connectionError ? 'Processing...' : `${getOverallProgress()}% complete`}
              </span>
            </div>
          )}
        </div>

        {/* Overall Progress Bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${getOverallProgress()}%` }}
          ></div>
        </div>

        {/* Current Step - show even if no real-time updates */}
        {!isComplete && (
          <div className="space-y-3">
            {progress ? (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Processing {getStepName(progress.step)}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {progress.current} of {progress.total}
                  </span>
                </div>
                
                {/* Step Progress Bar */}
                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
                  <div
                    className="bg-green-500 h-1.5 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${getProgressPercentage()}%` }}
                  ></div>
                </div>

                {/* Current Item */}
                {progress.currentItem && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {progress.currentItem}
                  </p>
                )}
              </>
            ) : (
              <div className="text-center py-2">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {connectionError 
                    ? 'Import is processing. This may take up to a minute...'
                    : 'Initializing import...'}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Step Indicators */}
        <div className="flex items-center justify-between">
          {['companies', 'deals', 'contacts'].map((step, index) => {
            const isActive = progress?.step === step;
            const isCompleted = progress && 
              (['companies', 'deals', 'contacts'].indexOf(progress.step) > index ||
               (progress.step === step && progress.current === progress.total));
            
            return (
              <div key={step} className="flex items-center">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium
                  ${isCompleted 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : isActive 
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      : 'bg-gray-100 text-gray-500 dark:bg-gray-600 dark:text-gray-400'
                  }
                `}>
                  {isCompleted ? '✓' : index + 1}
                </div>
                <span className={`
                  ml-2 text-sm
                  ${isActive 
                    ? 'text-blue-600 dark:text-blue-400 font-medium' 
                    : 'text-gray-500 dark:text-gray-400'
                  }
                `}>
                  {getStepName(step)}
                </span>
                {index < 2 && (
                  <div className={`
                    mx-4 h-0.5 w-8
                    ${isCompleted 
                      ? 'bg-green-300 dark:bg-green-600' 
                      : 'bg-gray-200 dark:bg-gray-600'
                    }
                  `}></div>
                )}
              </div>
            );
          })}
        </div>

        {/* Errors */}
        {progress && progress.errors.length > 0 && (
          <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
            <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              Issues encountered ({progress.errors.length})
            </h4>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {progress.errors.slice(0, 5).map((error, index) => (
                <p key={index} className="text-xs text-yellow-700 dark:text-yellow-300">
                  • {error.item}: {error.error}
                </p>
              ))}
              {progress.errors.length > 5 && (
                <p className="text-xs text-yellow-600 dark:text-yellow-400 italic">
                  ... and {progress.errors.length - 5} more
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportProgressTracker;
