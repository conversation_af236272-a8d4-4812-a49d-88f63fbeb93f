/**
 * Utility functions for the Deal Edit page
 */

import { Deal, DealEstimate } from "../../../types/crm-types";

/**
 * Check if a field is controlled by an estimate
 *
 * @param deal The deal object
 * @param fieldName The name of the field to check
 * @returns True if the field is controlled by an estimate
 */
export function isFieldControlledByEstimate(deal: Deal, fieldName: string): boolean {
  // If the deal has no estimates, no fields are controlled by estimates
  if (!deal.estimates || deal.estimates.length === 0) {
    return false;
  }

  // Check if the field is one of the fields that can be controlled by estimates
  const estimateControlledFields = ['startDate', 'endDate', 'invoiceFrequency', 'paymentTerms', 'value'];
  if (!estimateControlledFields.includes(fieldName)) {
    return false;
  }

  // Check if the field's owner is "Estimate"
  // This would require a backend API call to check field ownership
  // For now, we'll assume that if a deal has estimates and the field is in the list above,
  // it's controlled by an estimate
  return true;
}

/**
 * Get the first linked estimate from a deal
 *
 * @param deal The deal object
 * @returns The first linked estimate or undefined if none
 */
export function getFirstLinkedEstimate(deal: Deal): DealEstimate | undefined {
  if (!deal.estimates || deal.estimates.length === 0) {
    return undefined;
  }

  return deal.estimates[0];
}

/**
 * Get the URL for an estimate
 *
 * @param estimate The estimate object
 * @returns The URL to view the estimate
 */
export function getEstimateUrl(estimate: DealEstimate): string {
  if (estimate.type === 'draft') {
    return `/estimates/${estimate.id}`;
  }

  // For Harvest estimates, we don't have a direct URL in the app
  // We could potentially add a link to Harvest in the future
  return '#';
}
