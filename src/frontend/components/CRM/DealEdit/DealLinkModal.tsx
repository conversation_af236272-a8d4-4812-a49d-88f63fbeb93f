import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { getDeals, linkDealEstimate } from "../../../api/crm";
import { Deal } from "../../../types/crm-types";

interface DealLinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  estimateId: string;
  estimateType: "draft" | "harvest";
}

/**
 * Modal for linking an estimate to a deal
 */
const DealLinkModal: React.FC<DealLinkModalProps> = ({
  isOpen,
  onClose,
  estimateId,
  estimateType,
}) => {
  const [selectedDealId, setSelectedDealId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Fetch deals
  const { data: deals = [], isLoading: isDealsLoading } = useQuery(
    "deals",
    getDeals,
    {
      enabled: isOpen, // Only fetch when modal is open
      staleTime: 30000, // 30 seconds
    }
  );

  // Reset selected deal when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedDealId("");
      setSearchTerm("");
      setError(null);
    }
  }, [isOpen]);

  // Mutation for linking an estimate to a deal
  const linkEstimateMutation = useMutation(
    (dealId: string) => linkDealEstimate(dealId, estimateId, estimateType),
    {
      onSuccess: () => {
        // Invalidate relevant queries
        queryClient.invalidateQueries("deals");
        queryClient.invalidateQueries("draftEstimates");
        queryClient.invalidateQueries("harvestEstimates");

        // Close the modal
        onClose();
      },
      onError: (err: any) => {
        setError(err.message || "Failed to link estimate to deal");
      },
    }
  );

  // Handle linking the estimate to the selected deal
  const handleLinkEstimate = () => {
    if (!selectedDealId) {
      setError("Please select a deal");
      return;
    }

    linkEstimateMutation.mutate(selectedDealId);
  };

  // Filter deals based on search term
  const filteredDeals = deals.filter((deal: Deal) => {
    if (!searchTerm) return true;

    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    return (
      deal.name.toLowerCase().includes(lowerCaseSearchTerm) ||
      deal.stage.toLowerCase().includes(lowerCaseSearchTerm) ||
      (deal.company?.name || "").toLowerCase().includes(lowerCaseSearchTerm)
    );
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Link to Deal
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 overflow-auto">
          {error && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 rounded-md">
              {error}
            </div>
          )}

          <div className="mb-4">
            <label
              htmlFor="search-deals"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Search Deals
            </label>
            <input
              type="text"
              id="search-deals"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
              placeholder="Search by name, stage, or company..."
            />
          </div>

          {isDealsLoading ? (
            <div className="flex justify-center items-center py-8">
              <svg
                className="animate-spin h-8 w-8 text-blue-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          ) : filteredDeals.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No deals found matching your search.
            </div>
          ) : (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {filteredDeals.map((deal: Deal) => (
                <div
                  key={deal.id}
                  className={`p-3 border rounded-md cursor-pointer transition-colors ${
                    selectedDealId === deal.id
                      ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20 dark:border-emerald-700"
                      : "border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50"
                  }`}
                  onClick={() => setSelectedDealId(deal.id)}
                >
                  <div className="font-medium text-gray-900 dark:text-white">
                    {deal.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 flex justify-between">
                    <span>{deal.company?.name || "No company"}</span>
                    <span className="capitalize">{deal.stage}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            onClick={handleLinkEstimate}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!selectedDealId || linkEstimateMutation.isLoading}
          >
            {linkEstimateMutation.isLoading ? "Linking..." : "Link to Deal"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DealLinkModal;
