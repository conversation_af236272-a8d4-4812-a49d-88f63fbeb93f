import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { getDealById, updateDeal } from "../../../api/crm";
import { Deal, DealUpdate } from "../../../types/crm-types";
import { useQuery, useMutation, useQueryClient } from "react-query";
import DealInfoSection from "./DealInfoSection";
import DealCustomFieldsSection from "./DealCustomFieldsSection";
import DealHeaderCard from "./DealHeaderCard";
import DealFinancialSection from "./DealFinancialSection";
import DealTimelineSection from "./DealTimelineSection";
import DealContactsCard from "./DealContactsCard";
import DealEstimatesCard from "./DealEstimatesCard";
import DealNotesCard from "./DealNotesCard";

/**
 * Main component for the Deal Edit Page
 * Redesigned to use a card-based layout instead of tabs
 */
const DealEditPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({} as DealUpdate);

  // Fetch deal data
  const {
    data: deal,
    isLoading,
    error,
  } = useQuery<Deal>(["deal", id], () => getDealById(id!), {
    enabled: !!id,
    onSuccess: (data) => {
      // Initialize form data with deal data
      setFormData({
        name: data.name,
        stage: data.stage,
        value: data.value,
        currency: data.currency,
        probability: data.probability,
        expectedCloseDate: data.expectedCloseDate,
        startDate: data.startDate,
        endDate: data.endDate,
        invoiceFrequency: data.invoiceFrequency,
        paymentTerms: data.paymentTerms,
        description: data.description,
        source: data.source,
        priority: data.priority,
        owner: data.owner,
        customFields: data.customFields,
      });
    },
  });

  // Mutation for updating a deal
  const updateDealMutation = useMutation(
    (data: DealUpdate) => updateDeal(id!, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(["deal", id]);
        queryClient.invalidateQueries("deals");
        setIsEditing(false);
      },
    }
  );

  // Handle form field changes
  const handleChange = (field: keyof DealUpdate, value: unknown) => {
    setFormData((prev: DealUpdate) => ({ ...prev, [field]: value }));
  };

  // Handle form submission
  const handleSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    updateDealMutation.mutate(formData);
  };

  // Handle cancel edit
  const handleCancel = () => {
    // Reset form data to original deal data
    if (deal) {
      setFormData({
        name: deal.name,
        stage: deal.stage,
        value: deal.value,
        currency: deal.currency,
        probability: deal.probability,
        expectedCloseDate: deal.expectedCloseDate,
        startDate: deal.startDate,
        endDate: deal.endDate,
        invoiceFrequency: deal.invoiceFrequency,
        paymentTerms: deal.paymentTerms,
        description: deal.description,
        source: deal.source,
        priority: deal.priority,
        owner: deal.owner,
        customFields: deal.customFields,
      });
    }
    setIsEditing(false);
  };

  // Handle back button click
  const handleBack = () => {
    navigate("/crm/deals");
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !deal) {
    return (
      <div
        className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
        role="alert"
      >
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline">
          {" "}
          Failed to load deal. Please try again later.
        </span>
        <button
          onClick={handleBack}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Back to Deals
        </button>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen p-4 md:p-6">
      {/* Deal Header Card */}
      <DealHeaderCard
        deal={deal}
        isEditing={isEditing}
        formData={formData}
        onChange={handleChange}
        onEdit={() => setIsEditing(true)}
        onSave={handleSubmit}
        onCancel={handleCancel}
        onBack={handleBack}
        isLoading={updateDealMutation.isLoading}
      />

      {/* Main content area with all sections */}
      <div className="space-y-6">
        {/* Deal Information Section */}
        <DealInfoSection
          deal={deal}
          isEditing={isEditing}
          formData={formData}
          onChange={handleChange}
        />

        {/* Financial Details Section */}
        <DealFinancialSection
          deal={deal}
          isEditing={isEditing}
          formData={formData}
          onChange={handleChange}
        />

        {/* Timeline Information Section */}
        <DealTimelineSection
          deal={deal}
          isEditing={isEditing}
          formData={formData}
          onChange={handleChange}
        />

        {/* Contacts Section */}
        <DealContactsCard dealId={deal.id} contacts={deal.contacts || []} />

        {/* Estimates Section */}
        <DealEstimatesCard dealId={deal.id} estimates={deal.estimates || []} />

        {/* Notes Section */}
        <DealNotesCard dealId={deal.id} notes={deal.notes || []} />

        {/* Custom Fields Section */}
        <DealCustomFieldsSection
          deal={deal}
          isEditing={isEditing}
          formData={formData}
          onChange={handleChange}
        />
      </div>
    </div>
  );
};

export default DealEditPage;
