import React, { useState } from "react";
import { DealEstimate } from "../../../types/crm-types";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { linkDealEstimate, unlinkDealEstimate } from "../../../api/crm";
import { getDraftEstimates } from "../../../api/estimates";
import { getEstimates } from "../../../api/harvest";
import { format, parseISO } from "date-fns";
import EstimateLinkModal from "./EstimateLinkModal";

interface DealEstimatesSectionProps {
  dealId: string;
  estimates: DealEstimate[];
}

/**
 * Component for managing estimates linked to a deal
 */
const DealEstimatesSection: React.FC<DealEstimatesSectionProps> = ({
  dealId,
  estimates,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [estimateType, setEstimateType] = useState<"draft" | "harvest">(
    "draft"
  );
  const queryClient = useQueryClient();

  // Fetch draft estimates
  const { data: draftEstimates = [], isLoading: isDraftLoading } = useQuery(
    "draftEstimates",
    getDraftEstimates
  );

  // Fetch Harvest estimates
  const { data: harvestEstimates = [], isLoading: isHarvestLoading } = useQuery(
    "harvestEstimates",
    () => getEstimates()
  );

  // Mutation for linking an estimate to a deal
  const linkEstimateMutation = useMutation(
    (params: { estimateId: string; estimateType: "draft" | "harvest" }) =>
      linkDealEstimate(dealId, params.estimateId, params.estimateType),
    {
      onSuccess: () => {
        // Invalidate multiple queries to ensure data consistency
        queryClient.invalidateQueries(["deal", dealId]);
        queryClient.invalidateQueries("deals");
        setIsModalOpen(false);
      },
    }
  );

  // Mutation for unlinking an estimate from a deal
  const unlinkEstimateMutation = useMutation(
    (params: { estimateId: string; estimateType: "draft" | "harvest" }) =>
      unlinkDealEstimate(dealId, params.estimateId, params.estimateType),
    {
      onSuccess: () => {
        // Invalidate multiple queries to ensure data consistency
        queryClient.invalidateQueries(["deal", dealId]);
        queryClient.invalidateQueries("deals");
      },
    }
  );

  // Handle linking an estimate
  const handleLinkEstimate = (
    estimateId: string,
    type: "draft" | "harvest"
  ) => {
    linkEstimateMutation.mutate({ estimateId, estimateType: type });
  };

  // Handle unlinking an estimate
  const handleUnlinkEstimate = (
    estimateId: string,
    type: "draft" | "harvest"
  ) => {
    if (
      window.confirm(
        "Are you sure you want to unlink this estimate from the deal?"
      )
    ) {
      unlinkEstimateMutation.mutate({ estimateId, estimateType: type });
    }
  };

  // Open modal for linking a draft estimate
  const openDraftEstimateModal = () => {
    setEstimateType("draft");
    setIsModalOpen(true);
  };

  // Open modal for linking a Harvest estimate
  const openHarvestEstimateModal = () => {
    setEstimateType("harvest");
    setIsModalOpen(true);
  };

  // Format date to Australian format (DD/MM/YYYY)
  const formatDate = (dateString: string): string => {
    try {
      return format(parseISO(dateString), "dd/MM/yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Get estimate details
  const getEstimateDetails = (
    estimateId: string,
    type: "draft" | "harvest"
  ) => {
    if (type === "draft") {
      const estimate = draftEstimates.find((e) => e.uuid === estimateId);
      if (estimate) {
        return {
          name: estimate.projectName || "Unnamed Project",
          client: estimate.clientName,
          date: formatDate(estimate.updatedAt),
          status: estimate.status,
        };
      }
    } else {
      const estimate = harvestEstimates.find(
        (e) => e.id.toString() === estimateId
      );
      if (estimate) {
        return {
          name: estimate.subject || "Unnamed Estimate",
          client: estimate.client?.name || "Unknown Client",
          date: estimate.issue_date
            ? formatDate(estimate.issue_date)
            : "No date",
          status: estimate.state,
        };
      }
    }
    return {
      name: "Unknown Estimate",
      client: "Unknown Client",
      date: "Unknown Date",
      status: "unknown",
    };
  };

  // Filter out estimates that are already linked
  const getAvailableDraftEstimates = () => {
    const linkedDraftIds = estimates
      .filter((e) => e.type === "draft")
      .map((e) => e.id);
    return draftEstimates.filter((e) => !linkedDraftIds.includes(e.uuid));
  };

  const getAvailableHarvestEstimates = () => {
    const linkedHarvestIds = estimates
      .filter((e) => e.type === "harvest")
      .map((e) => e.id);
    return harvestEstimates.filter(
      (e) => !linkedHarvestIds.includes(e.id.toString())
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
          Linked Estimates
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          Estimates associated with this deal.
        </p>
      </div>

      <div className="px-4 py-5 sm:p-6">
        {/* Action buttons */}
        <div className="mb-6 flex flex-col sm:flex-row gap-3">
          <button
            type="button"
            onClick={openDraftEstimateModal}
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Link Draft Estimate
          </button>
          <button
            type="button"
            onClick={openHarvestEstimateModal}
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
            Link Harvest Estimate
          </button>
        </div>

        {/* Linked estimates list */}
        {estimates.length === 0 ? (
          <div className="text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-md p-6 text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-10 w-10 mx-auto mb-3 text-gray-400 dark:text-gray-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <p>No estimates linked to this deal yet.</p>
            <p className="mt-2 text-xs text-gray-400 dark:text-gray-500">
              Use the buttons above to link a draft or Harvest estimate.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Draft Estimates */}
            <div>
              <div className="flex items-center mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-blue-500 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Draft Estimates
                </h4>
              </div>

              {estimates.filter((estimate) => estimate.type === "draft")
                .length === 0 ? (
                <div className="py-4 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-md p-3 text-center">
                  No draft estimates linked.
                </div>
              ) : (
                <div className="grid gap-4 sm:grid-cols-1 lg:grid-cols-2">
                  {estimates
                    .filter((estimate) => estimate.type === "draft")
                    .map((estimate) => {
                      const details = getEstimateDetails(estimate.id, "draft");
                      return (
                        <div
                          key={estimate.id}
                          className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200"
                        >
                          <div className="border-b border-blue-100 dark:border-blue-800 bg-blue-100/50 dark:bg-blue-800/30 px-4 py-2 flex justify-between items-center">
                            <h5 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {details.name}
                            </h5>
                            <span className="text-xs px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200">
                              {details.status}
                            </span>
                          </div>
                          <div className="p-4">
                            <div className="grid grid-cols-2 gap-2 text-xs mb-3">
                              <div>
                                <p className="text-gray-500 dark:text-gray-400">
                                  Client
                                </p>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  {details.client}
                                </p>
                              </div>
                              <div>
                                <p className="text-gray-500 dark:text-gray-400">
                                  Updated
                                </p>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  {details.date}
                                </p>
                              </div>
                              <div>
                                <p className="text-gray-500 dark:text-gray-400">
                                  Linked
                                </p>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  {estimate.linkedAt
                                    ? formatDate(estimate.linkedAt)
                                    : "Unknown"}
                                </p>
                              </div>
                            </div>
                            <div className="flex space-x-2 mt-3">
                              <a
                                href={`/estimates/${estimate.id}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center justify-center flex-1 px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-900 dark:text-blue-200 dark:hover:bg-blue-800 transition-colors duration-200"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4 mr-1"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                  />
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                  />
                                </svg>
                                View
                              </a>
                              <button
                                type="button"
                                onClick={() =>
                                  handleUnlinkEstimate(estimate.id, "draft")
                                }
                                className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4 mr-1"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                                  />
                                </svg>
                                Unlink
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              )}
            </div>

            {/* Harvest Estimates */}
            <div>
              <div className="flex items-center mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-green-500 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Harvest Estimates
                </h4>
              </div>

              {estimates.filter((estimate) => estimate.type === "harvest")
                .length === 0 ? (
                <div className="py-4 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-md p-3 text-center">
                  No Harvest estimates linked.
                </div>
              ) : (
                <div className="grid gap-4 sm:grid-cols-1 lg:grid-cols-2">
                  {estimates
                    .filter((estimate) => estimate.type === "harvest")
                    .map((estimate) => {
                      const details = getEstimateDetails(
                        estimate.id,
                        "harvest"
                      );
                      return (
                        <div
                          key={estimate.id}
                          className="bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200"
                        >
                          <div className="border-b border-green-100 dark:border-green-800 bg-green-100/50 dark:bg-green-800/30 px-4 py-2 flex justify-between items-center">
                            <h5 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {details.name}
                            </h5>
                            <span className="text-xs px-2 py-1 rounded-full bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200">
                              {details.status}
                            </span>
                          </div>
                          <div className="p-4">
                            <div className="grid grid-cols-2 gap-2 text-xs mb-3">
                              <div>
                                <p className="text-gray-500 dark:text-gray-400">
                                  Client
                                </p>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  {details.client}
                                </p>
                              </div>
                              <div>
                                <p className="text-gray-500 dark:text-gray-400">
                                  Date
                                </p>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  {details.date}
                                </p>
                              </div>
                              <div>
                                <p className="text-gray-500 dark:text-gray-400">
                                  Linked
                                </p>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  {estimate.linkedAt
                                    ? formatDate(estimate.linkedAt)
                                    : "Unknown"}
                                </p>
                              </div>
                            </div>
                            <div className="flex justify-end mt-3">
                              <button
                                type="button"
                                onClick={() =>
                                  handleUnlinkEstimate(estimate.id, "harvest")
                                }
                                className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4 mr-1"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                                  />
                                </svg>
                                Unlink
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Estimate Link Modal */}
      <EstimateLinkModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        estimateType={estimateType}
        draftEstimates={getAvailableDraftEstimates()}
        harvestEstimates={getAvailableHarvestEstimates()}
        onLinkEstimate={handleLinkEstimate}
        isLoading={linkEstimateMutation.isLoading}
      />
    </div>
  );
};

export default DealEstimatesSection;
