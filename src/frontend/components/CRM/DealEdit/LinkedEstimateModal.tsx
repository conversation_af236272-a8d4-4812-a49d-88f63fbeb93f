import React, { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "react-query";
import { unlinkDealEstimate } from "../../../api/crm";
import { formatCurrency } from "../../Estimate/utils";
import { format, parseISO } from "date-fns";
import { TrashIcon, ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import { getDraftEstimate } from "../../../api/estimates";

interface LinkedEstimateModalProps {
  isOpen: boolean;
  onClose: () => void;
  deal: any;
  estimate: any;
  estimateType: "draft" | "harvest";
}

/**
 * Modal for viewing and managing a linked estimate
 */
const LinkedEstimateModal: React.FC<LinkedEstimateModalProps> = ({
  isOpen,
  onClose,
  deal,
  estimate,
  estimateType,
}) => {
  const [estimateDetails, setEstimateDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Fetch full estimate details when modal opens
  useEffect(() => {
    if (isOpen && estimate) {
      fetchEstimateDetails();
    }
  }, [isOpen, estimate]);

  // Fetch estimate details
  const fetchEstimateDetails = async () => {
    setLoading(true);
    setError(null);
    
    try {
      if (estimateType === "draft") {
        const draftDetails = await getDraftEstimate(estimate.id);
        setEstimateDetails(draftDetails);
      } else {
        // For Harvest estimates, we already have most of the details needed
        setEstimateDetails(estimate);
      }
    } catch (err: any) {
      console.error("Error fetching estimate details:", err);
      setError(err.message || "Failed to fetch estimate details");
    } finally {
      setLoading(false);
    }
  };

  // Mutation for unlinking the estimate from the deal
  const unlinkMutation = useMutation(
    () => {
      if (!deal || !estimate) throw new Error("Missing deal or estimate information");
      return unlinkDealEstimate(deal.id, estimate.id, estimateType);
    },
    {
      onSuccess: () => {
        // Invalidate relevant queries
        queryClient.invalidateQueries("deals");
        queryClient.invalidateQueries(["deal", deal.id]);
        queryClient.invalidateQueries("draftEstimates");
        queryClient.invalidateQueries("harvestEstimates");
        
        // Close the modal
        onClose();
      },
      onError: (err: any) => {
        setError(err.message || "Failed to unlink estimate from deal");
      },
    }
  );

  // Handle unlinking the estimate from the deal
  const handleUnlink = () => {
    if (window.confirm("Are you sure you want to unlink this estimate from the deal?")) {
      unlinkMutation.mutate();
    }
  };

  // Handle viewing the full estimate
  const handleViewEstimate = () => {
    let url;
    if (estimateType === "draft") {
      url = `/estimates/${estimate.id}`;
    } else {
      // For Harvest estimates
      url = `https://onbord.harvestapp.com/estimates/${estimate.id}`;
    }
    window.open(url, "_blank");
  };

  if (!isOpen) return null;

  // Format date safely
  const formatDateSafely = (dateString: string | undefined | null) => {
    if (!dateString) return "Not set";
    try {
      if (typeof dateString === 'string' && dateString.includes('T')) {
        return format(parseISO(dateString), "MMM d, yyyy");
      }
      return format(new Date(dateString), "MMM d, yyyy");
    } catch (e) {
      return "Invalid date";
    }
  };

  // Calculate estimate total
  const calculateTotal = () => {
    if (estimateType === "draft" && estimateDetails?.allocations) {
      let total = 0;
      estimateDetails.allocations.forEach((allocation: any) => {
        const totalDays = allocation.timeAllocations.reduce(
          (sum: number, ta: any) => sum + ta.days,
          0
        );
        total += allocation.rateProposedDaily * totalDays;
      });
      return total;
    } else if (estimateType === "harvest") {
      return estimateDetails?.amount || 0;
    }
    return 0;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {estimateType === "draft" ? "Draft Estimate" : "Harvest Estimate"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 overflow-auto">
          {error && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 rounded-md">
              {error}
            </div>
          )}

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <svg
                className="animate-spin h-8 w-8 text-blue-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          ) : estimateDetails ? (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {estimateType === "draft" 
                      ? estimateDetails.projectName || "Unnamed Project" 
                      : estimateDetails.subject || estimateDetails.number || "Unnamed Estimate"}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {estimateType === "draft" 
                      ? estimateDetails.clientName 
                      : estimateDetails.client?.name || "No Client"}
                  </p>
                </div>
                <span className={`text-xs px-2 py-0.5 rounded-full capitalize ${
                  estimateType === "draft"
                    ? "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200"
                    : getStatusColorClass(estimateDetails.state)
                }`}>
                  {estimateType === "draft" 
                    ? estimateDetails.status 
                    : estimateDetails.state}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Total
                  </p>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {formatCurrency(calculateTotal())}
                  </p>
                </div>
                
                {estimateType === "draft" && (
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Time Allocation
                    </p>
                    <p className="text-sm text-gray-900 dark:text-white">
                      {calculateTotalDays(estimateDetails)} days
                    </p>
                  </div>
                )}
                
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {estimateType === "draft" ? "Date Range" : "Issue Date"}
                  </p>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {estimateType === "draft"
                      ? `${formatDateSafely(estimateDetails.startDate)} - ${formatDateSafely(estimateDetails.endDate)}`
                      : formatDateSafely(estimateDetails.issue_date)}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Last Updated
                  </p>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {formatDateSafely(estimateDetails.updatedAt || estimateDetails.updated_at)}
                  </p>
                </div>
              </div>

              {estimateType === "draft" && estimateDetails.allocations && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                    Staff Allocation
                  </p>
                  <div className="space-y-1">
                    {estimateDetails.allocations.map((allocation: any, index: number) => (
                      <div key={index} className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                        <span>{allocation.firstName} {allocation.lastName}</span>
                        <span>{getAllocationDays(allocation)} days</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-between items-center mt-4 pt-3 border-t border-blue-200 dark:border-blue-800">
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Linked to deal: {deal.name}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={handleUnlink}
                    className="inline-flex items-center text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                    title="Unlink estimate"
                  >
                    <TrashIcon className="w-3.5 h-3.5 mr-1" />
                    Unlink
                  </button>
                  <button
                    onClick={handleViewEstimate}
                    className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                  >
                    <ArrowTopRightOnSquareIcon className="w-3.5 h-3.5 mr-1" />
                    View Estimate
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No estimate information found.
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

// Helper function to calculate total days for an allocation
const getAllocationDays = (allocation: any) => {
  if (!allocation.timeAllocations) return 0;
  return allocation.timeAllocations.reduce(
    (sum: number, ta: any) => sum + ta.days,
    0
  );
};

// Helper function to calculate total days for an estimate
const calculateTotalDays = (estimate: any) => {
  if (!estimate.allocations) return 0;
  let totalDays = 0;
  estimate.allocations.forEach((allocation: any) => {
    totalDays += getAllocationDays(allocation);
  });
  return totalDays;
};

// Helper function to get status color class
const getStatusColorClass = (status: string) => {
  switch (status) {
    case "draft":
      return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    case "sent":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
    case "accepted":
      return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
    case "declined":
      return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
  }
};

export default LinkedEstimateModal;