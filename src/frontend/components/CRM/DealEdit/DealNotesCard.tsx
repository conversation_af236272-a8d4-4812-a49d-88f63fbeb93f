import React from "react";
import { Note } from "../../../types/crm-types";
import { Card } from "../../shared/Card";
import DealNotesSection from "./DealNotesSection";

interface DealNotesCardProps {
  dealId: string;
  notes: Note[];
}

/**
 * Card wrapper for the DealNotesSection component
 */
const DealNotesCard: React.FC<DealNotesCardProps> = ({ dealId, notes }) => {
  return (
    <Card className="overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
          Notes
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          Notes and comments about this deal.
        </p>
      </div>
      <div className="px-4 py-5 sm:p-6">
        <DealNotesSection dealId={dealId} notes={notes} />
      </div>
    </Card>
  );
};

export default DealNotesCard;
