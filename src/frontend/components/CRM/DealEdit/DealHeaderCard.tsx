import React from "react";
import { Deal, DealUpdate } from "../../../types/crm-types";
import { Card } from "../../shared/Card";
import DealHeader from "./DealHeader";

interface DealHeaderCardProps {
  deal: Deal;
  isEditing: boolean;
  formData: DealUpdate;
  onChange: (field: keyof DealUpdate, value: any) => void;
  onEdit: () => void;
  onSave: (e: React.FormEvent) => void;
  onCancel: () => void;
  onBack: () => void;
  isLoading: boolean;
}

/**
 * Card wrapper for the DealHeader component
 */
const DealHeaderCard: React.FC<DealHeaderCardProps> = (props) => {
  return (
    <Card className="mb-6">
      <DealHeader {...props} />
    </Card>
  );
};

export default DealHeaderCard;
