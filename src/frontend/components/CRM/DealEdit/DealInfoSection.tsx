import React from "react";
import { Deal, DealUpdate, DealPriority } from "../../../types/crm-types";
import { format, parseISO } from "date-fns";
import { LinkIcon } from "@heroicons/react/24/outline";
import { isFieldControlledByEstimate } from "./utils";

// Define invoice frequency options
const INVOICE_FREQUENCY_OPTIONS = [
  { value: "weekly", label: "Weekly" },
  { value: "biweekly", label: "Every 2 Weeks" },
  { value: "monthly", label: "Monthly" },
  { value: "custom", label: "Custom" },
];

// Define payment terms options
const PAYMENT_TERMS_OPTIONS = [
  { value: 7, label: "Net 7" },
  { value: 14, label: "Net 14" },
  { value: 20, label: "Net 20" },
  { value: 30, label: "Net 30" },
];

interface DealInfoSectionProps {
  deal: Deal;
  isEditing: boolean;
  formData: DealUpdate;
  onChange: (field: keyof DealUpdate, value: any) => void;
}

/**
 * Component for displaying and editing basic deal information
 */
const DealInfoSection: React.FC<DealInfoSectionProps> = ({
  deal,
  isEditing,
  formData,
  onChange,
}) => {
  // Format date to Australian format (DD/MM/YYYY)
  const formatDate = (dateString?: string): string => {
    if (!dateString) return "No date set";
    try {
      return format(parseISO(dateString), "dd/MM/yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Deal priorities for dropdown
  const DEAL_PRIORITIES: DealPriority[] = ["Low", "Medium", "High"];

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
          Deal Information
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          Basic details about the deal.
        </p>
      </div>
      <div className="px-4 py-5 sm:p-6">
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          {/* Expected Close Date */}
          <div className="sm:col-span-3">
            <label
              htmlFor="expectedCloseDate"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Expected Close Date
            </label>
            <div className="mt-1">
              {isEditing ? (
                <input
                  type="date"
                  id="expectedCloseDate"
                  value={
                    formData.expectedCloseDate
                      ? formData.expectedCloseDate.split("T")[0]
                      : ""
                  }
                  onChange={(e) =>
                    onChange("expectedCloseDate", e.target.value)
                  }
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              ) : (
                <div className="text-sm text-gray-900 dark:text-gray-200">
                  {formatDate(deal.expectedCloseDate)}
                </div>
              )}
            </div>
          </div>

          {/* Project Start Date */}
          <div className="sm:col-span-3">
            <label
              htmlFor="startDate"
              className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center"
            >
              Project Start Date
              {isFieldControlledByEstimate(deal, "startDate") && (
                <span
                  className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  title="This field is controlled by the linked estimate"
                >
                  <LinkIcon className="w-3 h-3 mr-1" />
                  Estimate
                </span>
              )}
            </label>
            <div className="mt-1">
              {isEditing ? (
                <div className="relative">
                  <input
                    type="date"
                    id="startDate"
                    value={
                      formData.startDate ? formData.startDate.split("T")[0] : ""
                    }
                    onChange={(e) => onChange("startDate", e.target.value)}
                    className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      isFieldControlledByEstimate(deal, "startDate")
                        ? "border-blue-300 dark:border-blue-700"
                        : ""
                    }`}
                  />
                  {isFieldControlledByEstimate(deal, "startDate") && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span
                        className="text-blue-500 dark:text-blue-400"
                        title="This field is controlled by the linked estimate"
                      >
                        <LinkIcon className="w-4 h-4" />
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-gray-900 dark:text-gray-200 flex items-center">
                  {formatDate(deal.startDate)}
                  {isFieldControlledByEstimate(deal, "startDate") && (
                    <span
                      className="ml-2 text-blue-500 dark:text-blue-400"
                      title="This field is controlled by the linked estimate"
                    >
                      <LinkIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
              )}
              {isFieldControlledByEstimate(deal, "startDate") && isEditing && (
                <p className="mt-1 text-xs text-blue-500 dark:text-blue-400">
                  This field is controlled by the linked estimate. Changes may
                  be overwritten.
                </p>
              )}
            </div>
          </div>

          {/* Project End Date */}
          <div className="sm:col-span-3">
            <label
              htmlFor="endDate"
              className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center"
            >
              Project End Date
              {isFieldControlledByEstimate(deal, "endDate") && (
                <span
                  className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  title="This field is controlled by the linked estimate"
                >
                  <LinkIcon className="w-3 h-3 mr-1" />
                  Estimate
                </span>
              )}
            </label>
            <div className="mt-1">
              {isEditing ? (
                <div className="relative">
                  <input
                    type="date"
                    id="endDate"
                    value={
                      formData.endDate ? formData.endDate.split("T")[0] : ""
                    }
                    onChange={(e) => onChange("endDate", e.target.value)}
                    className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      isFieldControlledByEstimate(deal, "endDate")
                        ? "border-blue-300 dark:border-blue-700"
                        : ""
                    }`}
                  />
                  {isFieldControlledByEstimate(deal, "endDate") && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span
                        className="text-blue-500 dark:text-blue-400"
                        title="This field is controlled by the linked estimate"
                      >
                        <LinkIcon className="w-4 h-4" />
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-gray-900 dark:text-gray-200 flex items-center">
                  {formatDate(deal.endDate)}
                  {isFieldControlledByEstimate(deal, "endDate") && (
                    <span
                      className="ml-2 text-blue-500 dark:text-blue-400"
                      title="This field is controlled by the linked estimate"
                    >
                      <LinkIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
              )}
              {isFieldControlledByEstimate(deal, "endDate") && isEditing && (
                <p className="mt-1 text-xs text-blue-500 dark:text-blue-400">
                  This field is controlled by the linked estimate. Changes may
                  be overwritten.
                </p>
              )}
            </div>
          </div>

          {/* Priority */}
          <div className="sm:col-span-3">
            <label
              htmlFor="priority"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Priority
            </label>
            <div className="mt-1">
              {isEditing ? (
                <select
                  id="priority"
                  value={formData.priority || ""}
                  onChange={(e) => onChange("priority", e.target.value)}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="">Select Priority</option>
                  {DEAL_PRIORITIES.map((priority) => (
                    <option key={priority} value={priority}>
                      {priority}
                    </option>
                  ))}
                </select>
              ) : (
                <div className="text-sm text-gray-900 dark:text-gray-200">
                  {deal.priority || "Not set"}
                </div>
              )}
            </div>
          </div>

          {/* Source */}
          <div className="sm:col-span-3">
            <label
              htmlFor="source"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Source
            </label>
            <div className="mt-1">
              {isEditing ? (
                <input
                  type="text"
                  id="source"
                  value={formData.source || ""}
                  onChange={(e) => onChange("source", e.target.value)}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="Where did this deal come from?"
                />
              ) : (
                <div className="text-sm text-gray-900 dark:text-gray-200">
                  {deal.source || "Not specified"}
                </div>
              )}
            </div>
          </div>

          {/* Owner */}
          <div className="sm:col-span-3">
            <label
              htmlFor="owner"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Owner
            </label>
            <div className="mt-1">
              {isEditing ? (
                <input
                  type="text"
                  id="owner"
                  value={formData.owner || ""}
                  onChange={(e) => onChange("owner", e.target.value)}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="Who is responsible for this deal?"
                />
              ) : (
                <div className="text-sm text-gray-900 dark:text-gray-200">
                  {deal.owner || "Not assigned"}
                </div>
              )}
            </div>
          </div>

          {/* Invoice Frequency */}
          <div className="sm:col-span-3">
            <label
              htmlFor="invoiceFrequency"
              className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center"
            >
              Invoice Frequency
              {isFieldControlledByEstimate(deal, "invoiceFrequency") && (
                <span
                  className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  title="This field is controlled by the linked estimate"
                >
                  <LinkIcon className="w-3 h-3 mr-1" />
                  Estimate
                </span>
              )}
            </label>
            <div className="mt-1">
              {isEditing ? (
                <div className="relative">
                  {isFieldControlledByEstimate(deal, "invoiceFrequency") ? (
                    <div className="flex items-center">
                      <div className="flex-1 text-sm text-gray-900 dark:text-gray-200 py-2 px-3 border border-blue-300 dark:border-blue-700 rounded-md bg-gray-50 dark:bg-gray-800">
                        {deal.invoiceFrequency
                          ? INVOICE_FREQUENCY_OPTIONS.find(
                              (opt) => opt.value === deal.invoiceFrequency
                            )?.label || deal.invoiceFrequency
                          : "Not set"}
                      </div>
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span
                          className="text-blue-500 dark:text-blue-400"
                          title="This field is controlled by the linked estimate"
                        >
                          <LinkIcon className="w-4 h-4" />
                        </span>
                      </div>
                    </div>
                  ) : (
                    <select
                      id="invoiceFrequency"
                      value={formData.invoiceFrequency || ""}
                      onChange={(e) =>
                        onChange("invoiceFrequency", e.target.value)
                      }
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    >
                      <option value="">Select Frequency</option>
                      {INVOICE_FREQUENCY_OPTIONS.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  )}
                </div>
              ) : (
                <div className="text-sm text-gray-900 dark:text-gray-200 flex items-center">
                  {deal.invoiceFrequency
                    ? INVOICE_FREQUENCY_OPTIONS.find(
                        (opt) => opt.value === deal.invoiceFrequency
                      )?.label || deal.invoiceFrequency
                    : "Not set"}
                  {isFieldControlledByEstimate(deal, "invoiceFrequency") && (
                    <span
                      className="ml-2 text-blue-500 dark:text-blue-400"
                      title="This field is controlled by the linked estimate"
                    >
                      <LinkIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
              )}
              {isFieldControlledByEstimate(deal, "invoiceFrequency") &&
                isEditing && (
                  <p className="mt-1 text-xs text-blue-500 dark:text-blue-400">
                    This field is controlled by the linked estimate. To change
                    this value, please update the estimate.
                  </p>
                )}
            </div>
          </div>

          {/* Payment Terms */}
          <div className="sm:col-span-3">
            <label
              htmlFor="paymentTerms"
              className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center"
            >
              Payment Terms
              {isFieldControlledByEstimate(deal, "paymentTerms") && (
                <span
                  className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  title="This field is controlled by the linked estimate"
                >
                  <LinkIcon className="w-3 h-3 mr-1" />
                  Estimate
                </span>
              )}
            </label>
            <div className="mt-1">
              {isEditing ? (
                <div className="relative">
                  {isFieldControlledByEstimate(deal, "paymentTerms") ? (
                    <div className="flex items-center">
                      <div className="flex-1 text-sm text-gray-900 dark:text-gray-200 py-2 px-3 border border-blue-300 dark:border-blue-700 rounded-md bg-gray-50 dark:bg-gray-800">
                        {deal.paymentTerms
                          ? PAYMENT_TERMS_OPTIONS.find(
                              (opt) => opt.value === deal.paymentTerms
                            )?.label || `Net ${deal.paymentTerms}`
                          : "Not set"}
                      </div>
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span
                          className="text-blue-500 dark:text-blue-400"
                          title="This field is controlled by the linked estimate"
                        >
                          <LinkIcon className="w-4 h-4" />
                        </span>
                      </div>
                    </div>
                  ) : (
                    <select
                      id="paymentTerms"
                      value={formData.paymentTerms?.toString() || ""}
                      onChange={(e) =>
                        onChange("paymentTerms", parseInt(e.target.value))
                      }
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    >
                      <option value="">Select Payment Terms</option>
                      {PAYMENT_TERMS_OPTIONS.map((option) => (
                        <option
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </option>
                      ))}
                    </select>
                  )}
                </div>
              ) : (
                <div className="text-sm text-gray-900 dark:text-gray-200 flex items-center">
                  {deal.paymentTerms
                    ? PAYMENT_TERMS_OPTIONS.find(
                        (opt) => opt.value === deal.paymentTerms
                      )?.label || `Net ${deal.paymentTerms}`
                    : "Not set"}
                  {isFieldControlledByEstimate(deal, "paymentTerms") && (
                    <span
                      className="ml-2 text-blue-500 dark:text-blue-400"
                      title="This field is controlled by the linked estimate"
                    >
                      <LinkIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
              )}
              {isFieldControlledByEstimate(deal, "paymentTerms") &&
                isEditing && (
                  <p className="mt-1 text-xs text-blue-500 dark:text-blue-400">
                    This field is controlled by the linked estimate. To change
                    this value, please update the estimate.
                  </p>
                )}
            </div>
          </div>

          {/* Description */}
          <div className="sm:col-span-6">
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Description
            </label>
            <div className="mt-1">
              {isEditing ? (
                <textarea
                  id="description"
                  rows={4}
                  value={formData.description || ""}
                  onChange={(e) => onChange("description", e.target.value)}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="Add a description of the deal..."
                />
              ) : (
                <div className="text-sm text-gray-900 dark:text-gray-200 whitespace-pre-wrap">
                  {deal.description || "No description provided"}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DealInfoSection;
