import React, { useState, useEffect } from "react";
import { useQuery } from "react-query";
import { getDeals } from "../../../api/crm";
import { Deal, DealStage } from "../../../types/crm-types";
import { format, parseISO } from "date-fns";
import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/react/24/outline";

// Define deal stages in pipeline order
const DEAL_STAGES: DealStage[] = [
  "Identified",
  "Qualified",
  "Solution proposal",
  "Solution presentation",
  "Objection handling",
  "Finalising terms",
  "Closed won",
  "Closed lost",
  "Abandoned",
];

interface DealSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectDeal: (deal: Deal) => void;
}

/**
 * Modal for selecting a deal to populate estimate form fields
 */
const DealSelectionModal: React.FC<DealSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectDeal,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedDealId, setSelectedDealId] = useState<string>("");

  // Fetch deals
  const { data: deals = [], isLoading: isDealsLoading } = useQuery(
    "deals",
    getDeals,
    {
      enabled: isOpen, // Only fetch when modal is open
      staleTime: 30000, // 30 seconds
    }
  );

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm("");
      setSelectedDealId("");
    }
  }, [isOpen]);

  // Filter and sort deals based on search term and pipeline order
  const filteredDeals = deals
    .filter((deal) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        deal.name.toLowerCase().includes(searchLower) ||
        (deal.company?.name &&
          deal.company.name.toLowerCase().includes(searchLower)) ||
        deal.stage.toLowerCase().includes(searchLower)
      );
    })
    .sort((a, b) => {
      // Sort by pipeline stage order first
      const aStageIndex = DEAL_STAGES.indexOf(a.stage);
      const bStageIndex = DEAL_STAGES.indexOf(b.stage);

      if (aStageIndex !== bStageIndex) {
        return aStageIndex - bStageIndex;
      }

      // If same stage, sort by deal name alphabetically
      return a.name.localeCompare(b.name);
    });

  // Handle deal selection
  const handleSelectDeal = () => {
    const selectedDeal = deals.find((deal) => deal.id === selectedDealId);
    if (selectedDeal) {
      onSelectDeal(selectedDeal);
      onClose();
    }
  };

  // Format currency
  const formatCurrency = (amount?: number) => {
    if (!amount) return "No value";
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: "AUD",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "No date";
    try {
      return format(parseISO(dateString), "dd/MM/yyyy");
    } catch {
      return "Invalid date";
    }
  };

  // Get stage color
  const getStageColor = (stage: string) => {
    switch (stage) {
      case "Closed won":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "Closed lost":
      case "Abandoned":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
      case "Finalising terms":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Select Deal
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search deals by name, company, or stage..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {isDealsLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500"></div>
            </div>
          ) : filteredDeals.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              {searchTerm
                ? "No deals found matching your search."
                : "No deals available."}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredDeals.map((deal) => (
                <div
                  key={deal.id}
                  onClick={() => setSelectedDealId(deal.id)}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedDealId === deal.id
                      ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20 dark:border-emerald-400"
                      : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      {deal.name}
                    </h3>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${getStageColor(
                        deal.stage
                      )}`}
                    >
                      {deal.stage}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <div>
                      <span className="font-medium">Company:</span>{" "}
                      {deal.company?.name || "No company"}
                    </div>
                    <div>
                      <span className="font-medium">Value:</span>{" "}
                      {formatCurrency(deal.value)}
                    </div>
                    <div>
                      <span className="font-medium">Expected Close:</span>{" "}
                      {formatDate(deal.expectedCloseDate)}
                    </div>
                    <div>
                      <span className="font-medium">Probability:</span>{" "}
                      {deal.probability || 0}%
                    </div>
                  </div>

                  {deal.description && (
                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                      {deal.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            onClick={handleSelectDeal}
            disabled={!selectedDealId}
            className="px-4 py-2 bg-emerald-600 text-white rounded-md text-sm font-medium hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Select Deal
          </button>
        </div>
      </div>
    </div>
  );
};

export default DealSelectionModal;
