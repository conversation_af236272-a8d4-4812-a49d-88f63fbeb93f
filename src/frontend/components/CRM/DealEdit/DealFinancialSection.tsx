import React, { useState, useEffect } from "react";
import { Deal, DealUpdate, DealEstimate } from "../../../types/crm-types";
import { Card } from "../../shared/Card";
import {
  LinkIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { isFieldControlledByEstimate } from "./utils";
import { useQuery } from "react-query";
import { getDraftEstimate } from "../../../api/estimates";
import { getEstimate } from "../../../api/harvest";

// Define invoice frequency options
const INVOICE_FREQUENCY_OPTIONS = [
  { value: "weekly", label: "Weekly" },
  { value: "biweekly", label: "Every 2 Weeks" },
  { value: "monthly", label: "Monthly" },
  { value: "custom", label: "Custom" },
];

// Define payment terms options
const PAYMENT_TERMS_OPTIONS = [
  { value: 7, label: "Net 7" },
  { value: 14, label: "Net 14" },
  { value: 20, label: "Net 20" },
  { value: 30, label: "Net 30" },
];

interface DealFinancialSectionProps {
  deal: Deal;
  isEditing: boolean;
  formData: DealUpdate;
  onChange: (field: keyof DealUpdate, value: any) => void;
}

/**
 * Component for displaying and editing deal financial information
 */
const DealFinancialSection: React.FC<DealFinancialSectionProps> = ({
  deal,
  isEditing,
  formData,
  onChange,
}) => {
  // Get linked estimates
  const linkedEstimates = deal.estimates || [];
  const linkedDraftEstimate = linkedEstimates.find((e) => e.type === "draft");
  const linkedHarvestEstimate = linkedEstimates.find(
    (e) => e.type === "harvest"
  );

  // Fetch draft estimate details if available
  const { data: draftEstimateDetails, isLoading: isDraftLoading } = useQuery(
    ["draftEstimate", linkedDraftEstimate?.id],
    () => getDraftEstimate(linkedDraftEstimate!.id),
    {
      enabled: !!linkedDraftEstimate,
      staleTime: 60000, // 1 minute
    }
  );

  // Fetch Harvest estimate details if available
  const { data: harvestEstimateDetails, isLoading: isHarvestLoading } =
    useQuery(
      ["harvestEstimate", linkedHarvestEstimate?.id],
      () => getEstimate(parseInt(linkedHarvestEstimate!.id)),
      {
        enabled: !!linkedHarvestEstimate,
        staleTime: 60000, // 1 minute
      }
    );

  // Format currency
  const formatCurrency = (value?: number, currency = "AUD") => {
    if (value === undefined || isNaN(value)) return "—";
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value?: number) => {
    if (value === undefined || isNaN(value)) return "—";
    return `${value.toFixed(1)}%`;
  };

  // Get margin health class
  const getMarginHealthClass = (marginPercentage?: number) => {
    if (marginPercentage === undefined)
      return "text-gray-500 dark:text-gray-400";
    if (marginPercentage >= 30) return "text-green-600 dark:text-green-400";
    if (marginPercentage >= 20) return "text-amber-500 dark:text-amber-400";
    return "text-red-600 dark:text-red-400";
  };

  // Calculate financial metrics from draft estimate - using the same approach as the estimates table
  const calculateDraftEstimateFinancials = (draftEstimate: any) => {
    if (!draftEstimate || !draftEstimate.allocations) return null;

    // Calculate totals from allocations
    let totalDays = 0;
    let totalCost = 0;
    let totalRevenue = 0;
    const staffInitials: string[] = [];

    // Use the allocations to calculate totals - same approach as EstimatesList.tsx
    draftEstimate.allocations.forEach((allocation: any) => {
      // Add staff initials for reference
      const initial = `${allocation.firstName.charAt(0)}${
        allocation.lastName ? allocation.lastName.charAt(0) : ""
      }`;
      if (!staffInitials.includes(initial)) {
        staffInitials.push(initial);
      }

      // Calculate days for this allocation
      const allocationDays =
        allocation.timeAllocations?.reduce(
          (sum: number, ta: any) => sum + (ta.days || 0),
          0
        ) || 0;

      totalDays += allocationDays;
      totalCost += allocation.onbordCostRateDaily * allocationDays;
      totalRevenue += allocation.rateProposedDaily * allocationDays;
    });

    // Calculate margin
    const marginAmount = totalRevenue - totalCost;
    const marginPercentage =
      totalRevenue > 0 ? (marginAmount / totalRevenue) * 100 : 0;

    // Calculate average daily rate
    const averageDailyRate = totalDays > 0 ? totalRevenue / totalDays : 0;

    console.log("Calculated financials from draft estimate:", {
      totalRevenue,
      totalCost,
      marginAmount,
      marginPercentage,
      totalDays,
      averageDailyRate,
      staffInitials,
    });

    return {
      totalRevenue,
      totalCost,
      marginAmount,
      marginPercentage,
      totalDays,
      averageDailyRate,
      staffInitials,
      currency: deal.currency || "AUD",
      source: "draft",
    };
  };

  // Extract financial metrics from estimate
  const estimateFinancials = React.useMemo(() => {
    const dealCurrency = deal.currency || "AUD";

    if (draftEstimateDetails) {
      console.log("Draft estimate details:", draftEstimateDetails);

      // If the API returns totalFees directly, use it
      if (typeof draftEstimateDetails.totalFees === "number") {
        console.log(
          "Using pre-calculated totalFees:",
          draftEstimateDetails.totalFees
        );

        // If we have totalFees but not other metrics, calculate them
        if (
          draftEstimateDetails.allocations &&
          (!draftEstimateDetails.totalCost ||
            !draftEstimateDetails.marginAmount)
        ) {
          console.log("Calculating missing metrics from allocations");
          return calculateDraftEstimateFinancials(draftEstimateDetails);
        }

        return {
          totalRevenue: draftEstimateDetails.totalFees,
          totalCost: draftEstimateDetails.totalCost,
          marginAmount: draftEstimateDetails.marginAmount,
          marginPercentage: draftEstimateDetails.marginPercentage,
          totalDays: draftEstimateDetails.totalDays,
          averageDailyRate: draftEstimateDetails.averageDailyRate,
          currency: dealCurrency,
          source: "draft",
        };
      }

      // If the API doesn't return calculated fields, calculate them ourselves
      console.log("Calculating all metrics from allocations");
      const calculatedFinancials =
        calculateDraftEstimateFinancials(draftEstimateDetails);
      if (calculatedFinancials) {
        return calculatedFinancials;
      }
    }

    if (harvestEstimateDetails) {
      // Extract data from Harvest estimate
      const amount = harvestEstimateDetails.amount;
      const currency = harvestEstimateDetails.currency || dealCurrency;

      return {
        totalRevenue: amount,
        // We don't have these values for Harvest estimates
        totalCost: undefined,
        marginAmount: undefined,
        marginPercentage: undefined,
        totalDays: undefined,
        currency: currency,
        source: "harvest",
      };
    }

    return null;
  }, [draftEstimateDetails, harvestEstimateDetails, deal.currency]);

  // Loading state
  const isLoading =
    (linkedDraftEstimate && isDraftLoading) ||
    (linkedHarvestEstimate && isHarvestLoading);

  return (
    <Card className="overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
          Financial Details
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          Deal value and payment information.
        </p>
      </div>

      <div className="px-4 py-5 sm:p-6">
        {/* Financial Summary from Estimate */}
        {(linkedDraftEstimate || linkedHarvestEstimate) && (
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <DocumentTextIcon className="w-5 h-5 text-blue-500 mr-2" />
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Estimate Financial Summary
              </h4>
            </div>

            {isLoading ? (
              <div className="animate-pulse flex space-x-4 h-20 bg-gray-100 dark:bg-gray-700 rounded-md">
                <div className="flex-1 py-1"></div>
              </div>
            ) : estimateFinancials ? (
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {/* Total Revenue */}
                  <div>
                    <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                      <CurrencyDollarIcon className="w-3 h-3 mr-1" />
                      Total Revenue
                    </div>
                    <div className="text-base font-bold text-gray-900 dark:text-white">
                      {formatCurrency(
                        estimateFinancials.totalRevenue,
                        estimateFinancials.currency
                      )}
                    </div>
                  </div>

                  {/* Gross Margin - Only show if available */}
                  {estimateFinancials.marginPercentage !== undefined && (
                    <div>
                      <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                        Gross Margin
                      </div>
                      <div className="flex flex-col">
                        <span
                          className={`text-base font-bold ${getMarginHealthClass(
                            estimateFinancials.marginPercentage
                          )}`}
                        >
                          {formatPercentage(
                            estimateFinancials.marginPercentage
                          )}
                        </span>
                        <span className="text-xs text-gray-600 dark:text-gray-400">
                          {formatCurrency(
                            estimateFinancials.marginAmount,
                            estimateFinancials.currency
                          )}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Total Cost - Only show if available */}
                  {estimateFinancials.totalCost !== undefined && (
                    <div>
                      <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                        Total Cost
                      </div>
                      <div className="text-base font-bold text-gray-700 dark:text-gray-300">
                        {formatCurrency(
                          estimateFinancials.totalCost,
                          estimateFinancials.currency
                        )}
                      </div>
                    </div>
                  )}

                  {/* Total Days - Only show if available */}
                  {estimateFinancials.totalDays !== undefined && (
                    <div>
                      <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                        <ClockIcon className="w-3 h-3 mr-1" />
                        Total Days
                      </div>
                      <div className="text-base font-bold text-gray-700 dark:text-gray-300">
                        {estimateFinancials.totalDays.toFixed(1)}
                      </div>
                      {estimateFinancials.staffInitials &&
                        estimateFinancials.staffInitials.length > 0 && (
                          <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            Staff: {estimateFinancials.staffInitials.join(", ")}
                          </div>
                        )}
                    </div>
                  )}

                  {/* Average Daily Rate - Only show if available */}
                  {estimateFinancials.averageDailyRate !== undefined && (
                    <div>
                      <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                        <ClockIcon className="w-3 h-3 mr-1" />
                        Avg. Daily Rate
                      </div>
                      <div className="text-base font-bold text-gray-700 dark:text-gray-300">
                        {formatCurrency(
                          estimateFinancials.averageDailyRate,
                          estimateFinancials.currency
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Source indicator */}
                <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                  <span className="mr-1">Source:</span>
                  {estimateFinancials.source === "draft" ? (
                    <span className="text-blue-600 dark:text-blue-400 font-medium">
                      Draft Estimate
                    </span>
                  ) : estimateFinancials.source === "harvest" ? (
                    <span className="text-green-600 dark:text-green-400 font-medium">
                      Harvest Estimate
                    </span>
                  ) : (
                    <span className="text-gray-600 dark:text-gray-400 font-medium">
                      Deal Value
                    </span>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-sm text-gray-500 dark:text-gray-400 italic">
                No financial data available. Link an estimate to see financial
                metrics.
              </div>
            )}
          </div>
        )}

        {/* Invoice and Payment Settings */}
        <div className="mt-4">
          <div className="flex items-center mb-3">
            <CalendarIcon className="w-5 h-5 text-blue-500 mr-2" />
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Invoice and Payment Settings
            </h4>
          </div>

          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            {/* Invoice Frequency */}
            <div className="sm:col-span-3">
              <label
                htmlFor="invoiceFrequency"
                className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center"
              >
                Invoice Frequency
                {isFieldControlledByEstimate(deal, "invoiceFrequency") && (
                  <span
                    className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                    title="This field is controlled by the linked estimate"
                  >
                    <LinkIcon className="w-3 h-3 mr-1" />
                    Estimate
                  </span>
                )}
              </label>
              <div className="mt-1">
                {isEditing ? (
                  <div className="relative">
                    {isFieldControlledByEstimate(deal, "invoiceFrequency") ? (
                      <div className="flex items-center">
                        <div className="flex-1 text-sm text-gray-900 dark:text-gray-200 py-2 px-3 border border-blue-300 dark:border-blue-700 rounded-md bg-gray-50 dark:bg-gray-800">
                          {deal.invoiceFrequency
                            ? INVOICE_FREQUENCY_OPTIONS.find(
                                (opt) => opt.value === deal.invoiceFrequency
                              )?.label || deal.invoiceFrequency
                            : "Not set"}
                        </div>
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <span
                            className="text-blue-500 dark:text-blue-400"
                            title="This field is controlled by the linked estimate"
                          >
                            <LinkIcon className="w-4 h-4" />
                          </span>
                        </div>
                      </div>
                    ) : (
                      <select
                        id="invoiceFrequency"
                        value={formData.invoiceFrequency || ""}
                        onChange={(e) =>
                          onChange("invoiceFrequency", e.target.value)
                        }
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      >
                        <option value="">Select Frequency</option>
                        {INVOICE_FREQUENCY_OPTIONS.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                ) : (
                  <div className="text-sm text-gray-900 dark:text-gray-200 flex items-center">
                    {deal.invoiceFrequency
                      ? INVOICE_FREQUENCY_OPTIONS.find(
                          (opt) => opt.value === deal.invoiceFrequency
                        )?.label || deal.invoiceFrequency
                      : "Not set"}
                    {isFieldControlledByEstimate(deal, "invoiceFrequency") && (
                      <span
                        className="ml-2 text-blue-500 dark:text-blue-400"
                        title="This field is controlled by the linked estimate"
                      >
                        <LinkIcon className="w-4 h-4" />
                      </span>
                    )}
                  </div>
                )}
                {isFieldControlledByEstimate(deal, "invoiceFrequency") &&
                  isEditing && (
                    <p className="mt-1 text-xs text-blue-500 dark:text-blue-400">
                      This field is controlled by the linked estimate. To change
                      this value, please update the estimate.
                    </p>
                  )}
              </div>
            </div>

            {/* Payment Terms */}
            <div className="sm:col-span-3">
              <label
                htmlFor="paymentTerms"
                className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center"
              >
                Payment Terms
                {isFieldControlledByEstimate(deal, "paymentTerms") && (
                  <span
                    className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                    title="This field is controlled by the linked estimate"
                  >
                    <LinkIcon className="w-3 h-3 mr-1" />
                    Estimate
                  </span>
                )}
              </label>
              <div className="mt-1">
                {isEditing ? (
                  <div className="relative">
                    {isFieldControlledByEstimate(deal, "paymentTerms") ? (
                      <div className="flex items-center">
                        <div className="flex-1 text-sm text-gray-900 dark:text-gray-200 py-2 px-3 border border-blue-300 dark:border-blue-700 rounded-md bg-gray-50 dark:bg-gray-800">
                          {deal.paymentTerms
                            ? PAYMENT_TERMS_OPTIONS.find(
                                (opt) => opt.value === deal.paymentTerms
                              )?.label || `Net ${deal.paymentTerms}`
                            : "Not set"}
                        </div>
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <span
                            className="text-blue-500 dark:text-blue-400"
                            title="This field is controlled by the linked estimate"
                          >
                            <LinkIcon className="w-4 h-4" />
                          </span>
                        </div>
                      </div>
                    ) : (
                      <select
                        id="paymentTerms"
                        value={formData.paymentTerms?.toString() || ""}
                        onChange={(e) =>
                          onChange("paymentTerms", parseInt(e.target.value))
                        }
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      >
                        <option value="">Select Payment Terms</option>
                        {PAYMENT_TERMS_OPTIONS.map((option) => (
                          <option
                            key={option.value}
                            value={option.value.toString()}
                          >
                            {option.label}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                ) : (
                  <div className="text-sm text-gray-900 dark:text-gray-200 flex items-center">
                    {deal.paymentTerms
                      ? PAYMENT_TERMS_OPTIONS.find(
                          (opt) => opt.value === deal.paymentTerms
                        )?.label || `Net ${deal.paymentTerms}`
                      : "Not set"}
                    {isFieldControlledByEstimate(deal, "paymentTerms") && (
                      <span
                        className="ml-2 text-blue-500 dark:text-blue-400"
                        title="This field is controlled by the linked estimate"
                      >
                        <LinkIcon className="w-4 h-4" />
                      </span>
                    )}
                  </div>
                )}
                {isFieldControlledByEstimate(deal, "paymentTerms") &&
                  isEditing && (
                    <p className="mt-1 text-xs text-blue-500 dark:text-blue-400">
                      This field is controlled by the linked estimate. To change
                      this value, please update the estimate.
                    </p>
                  )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default DealFinancialSection;
