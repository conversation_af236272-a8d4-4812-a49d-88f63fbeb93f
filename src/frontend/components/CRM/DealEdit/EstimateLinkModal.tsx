import React, { useState, useEffect } from "react";
import { format, parseISO } from "date-fns";
import { DraftEstimateSummary } from "../../../../types/api";
import { ExclamationCircleIcon } from "@heroicons/react/24/outline";

interface EstimateLinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  estimateType: "draft" | "harvest";
  draftEstimates: DraftEstimateSummary[];
  harvestEstimates: any[]; // Using any for Harvest estimates since we don't have a specific type
  onLinkEstimate: (estimateId: string, type: "draft" | "harvest") => void;
  isLoading: boolean;
  error?: string; // Add error prop
}

/**
 * Modal for linking estimates to a deal
 */
const EstimateLinkModal: React.FC<EstimateLinkModalProps> = ({
  isOpen,
  onClose,
  estimateType,
  draftEstimates,
  harvestEstimates,
  onLinkEstimate,
  isLoading,
  error,
}) => {
  const [selectedEstimateId, setSelectedEstimateId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");

  // Reset selected estimate when modal opens or estimate type changes
  useEffect(() => {
    setSelectedEstimateId("");
    setSearchTerm("");
  }, [isOpen, estimateType]);

  // Format date to Australian format (DD/MM/YYYY)
  const formatDate = (dateString: string): string => {
    try {
      return format(parseISO(dateString), "dd/MM/yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Filter draft estimates based on search term
  const filteredDraftEstimates = draftEstimates.filter(
    (estimate) =>
      estimate.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (estimate.projectName &&
        estimate.projectName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Filter Harvest estimates based on search term
  const filteredHarvestEstimates = harvestEstimates.filter(
    (estimate) =>
      (estimate.client?.name &&
        estimate.client.name
          .toLowerCase()
          .includes(searchTerm.toLowerCase())) ||
      (estimate.subject &&
        estimate.subject.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handle linking the selected estimate
  const handleLinkEstimate = () => {
    if (selectedEstimateId) {
      onLinkEstimate(selectedEstimateId, estimateType);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen p-4">
        {/* Background overlay */}
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75 dark:bg-gray-900 dark:opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div className="relative bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all w-full max-w-lg mx-auto">
          <div className="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                  Link {estimateType === "draft" ? "Draft" : "Harvest"} Estimate
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Linking an estimate will automatically update the deal's value
                  and name if they are not already set.
                </p>

                {/* Error message */}
                {error && (
                  <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                    <div className="flex">
                      <ExclamationCircleIcon className="h-5 w-5 text-red-500 dark:text-red-400 mr-2 flex-shrink-0" />
                      <p className="text-sm text-red-600 dark:text-red-400">
                        {error}
                      </p>
                    </div>
                  </div>
                )}
                <div className="mt-4">
                  <div className="mb-4">
                    <label
                      htmlFor="estimateSearch"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Search Estimates
                    </label>
                    <input
                      type="text"
                      id="estimateSearch"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="mt-1 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="Search by client or project name"
                    />
                  </div>

                  <div className="mb-4">
                    <label
                      htmlFor="estimateSelect"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Select Estimate
                    </label>
                    <select
                      id="estimateSelect"
                      value={selectedEstimateId}
                      onChange={(e) => setSelectedEstimateId(e.target.value)}
                      className="mt-1 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    >
                      <option value="">Select an estimate</option>
                      {estimateType === "draft"
                        ? filteredDraftEstimates.map((estimate) => (
                            <option key={estimate.uuid} value={estimate.uuid}>
                              {estimate.projectName || "Unnamed Project"} -{" "}
                              {estimate.clientName} (
                              {formatDate(estimate.updatedAt)})
                            </option>
                          ))
                        : filteredHarvestEstimates.map((estimate) => (
                            <option
                              key={estimate.id}
                              value={estimate.id.toString()}
                            >
                              {estimate.subject || "Unnamed Estimate"} -{" "}
                              {estimate.client?.name || "Unknown Client"} (
                              {estimate.issue_date
                                ? formatDate(estimate.issue_date)
                                : "No date"}
                              )
                            </option>
                          ))}
                    </select>
                  </div>

                  {/* Display selected estimate details */}
                  {selectedEstimateId && (
                    <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Selected Estimate Details
                      </h4>
                      {estimateType === "draft"
                        ? (() => {
                            const estimate = draftEstimates.find(
                              (e) => e.uuid === selectedEstimateId
                            );
                            return estimate ? (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                <p>
                                  <span className="font-medium">Project:</span>{" "}
                                  {estimate.projectName || "Unnamed Project"}
                                </p>
                                <p>
                                  <span className="font-medium">Client:</span>{" "}
                                  {estimate.clientName}
                                </p>
                                <p>
                                  <span className="font-medium">Updated:</span>{" "}
                                  {formatDate(estimate.updatedAt)}
                                </p>
                                <p>
                                  <span className="font-medium">Status:</span>{" "}
                                  {estimate.status}
                                </p>
                              </div>
                            ) : null;
                          })()
                        : (() => {
                            const estimate = harvestEstimates.find(
                              (e) => e.id.toString() === selectedEstimateId
                            );
                            return estimate ? (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                <p>
                                  <span className="font-medium">Subject:</span>{" "}
                                  {estimate.subject || "Unnamed Estimate"}
                                </p>
                                <p>
                                  <span className="font-medium">Client:</span>{" "}
                                  {estimate.client?.name || "Unknown Client"}
                                </p>
                                <p>
                                  <span className="font-medium">Amount:</span>{" "}
                                  {new Intl.NumberFormat("en-AU", {
                                    style: "currency",
                                    currency: "AUD",
                                    maximumFractionDigits: 0,
                                  }).format(estimate.amount)}
                                </p>
                                <p>
                                  <span className="font-medium">Status:</span>{" "}
                                  {estimate.state}
                                </p>
                              </div>
                            ) : null;
                          })()}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={handleLinkEstimate}
              disabled={!selectedEstimateId || isLoading}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed dark:bg-blue-500 dark:hover:bg-blue-600"
            >
              {isLoading ? "Linking..." : "Link Estimate"}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-200 dark:border-gray-500 dark:hover:bg-gray-500"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EstimateLinkModal;
