import React, { useState } from "react";
import { Deal, DealUpdate } from "../../../types/crm-types";
import {
  updateDeal,
  deleteDeal,
  linkDealEstimate,
  unlinkDealEstimate,
  getDealById,
} from "../../../api/crm";
import { useMutation, useQueryClient } from "react-query";
import { Link } from "react-router-dom";
import NotesTimeline from "../Notes/NotesTimeline";
import {
  DocumentTextIcon,
  LinkIcon,
  TrashIcon,
  ArrowTopRightOnSquareIcon,
  ExclamationCircleIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import EstimateLinkModal from "../DealEdit/EstimateLinkModal";
import { getDraftEstimates, getDraftEstimate } from "../../../api/estimates";
import { getEstimates } from "../../../api/harvest";
import { useQuery } from "react-query";

interface DealDetailProps {
  dealId: string;
  initialDealData: Deal;
  onClose: () => void;
}

/**
 * Component for displaying detailed information about a deal
 */
const DealDetail: React.FC<DealDetailProps> = ({
  dealId,
  initialDealData,
  onClose,
}) => {
  // Fetch the latest deal data
  const { data: deal = initialDealData } = useQuery(
    ["deal", dealId],
    () => getDealById(dealId),
    {
      initialData: initialDealData,
      refetchOnWindowFocus: false,
    }
  );
  const [isEditing, setIsEditing] = useState(false);
  const [isEstimateLinkModalOpen, setIsEstimateLinkModalOpen] = useState(false);
  const [estimateType, setEstimateType] = useState<"draft" | "harvest">(
    "draft"
  );
  const [formData, setFormData] = useState<DealUpdate>({
    name: deal.name,
    stage: deal.stage,
    value: deal.value,
    currency: deal.currency,
    probability: deal.probability,
    expectedCloseDate: deal.expectedCloseDate,
    description: deal.description,
    priority: deal.priority,
  });
  const queryClient = useQueryClient();

  // Fetch draft estimates
  const { data: draftEstimates = [] } = useQuery(
    "draftEstimates",
    getDraftEstimates,
    {
      staleTime: 0, // Always refetch when requested
      refetchOnWindowFocus: false,
    }
  );

  // Get the linked draft estimate ID if any
  const linkedDraftEstimateId = deal.estimates?.find(
    (e: { type: string }) => e.type === "draft"
  )?.id;

  // Fetch full draft estimate details if there's a linked draft estimate
  const {
    data: fullDraftEstimateDetails,
    isLoading: isLoadingDraftDetails,
    isError: isDraftError,
  } = useQuery(
    ["draftEstimate", linkedDraftEstimateId],
    () => {
      console.log(
        "Fetching draft estimate details for ID:",
        linkedDraftEstimateId
      );
      return linkedDraftEstimateId
        ? getDraftEstimate(linkedDraftEstimateId)
        : null;
    },
    {
      enabled: !!linkedDraftEstimateId,
      staleTime: 0, // Always refetch when requested
      cacheTime: 0, // Don't cache the result
      refetchOnWindowFocus: true, // Refetch when window gets focus
      onSuccess: (data) => {
        console.log("Fetched draft estimate details:", data);
        console.log("Total fees value:", data?.totalFees);
      },
      onError: (error) => {
        console.error("Error fetching draft estimate details:", error);
      },
    }
  );

  // Fetch Harvest estimates
  const { data: harvestEstimates = [] } = useQuery(
    "harvestEstimates",
    () => getEstimates(),
    {
      staleTime: 0, // Always refetch when requested
      refetchOnWindowFocus: false,
    }
  );

  // Mutation for updating a deal
  const updateDealMutation = useMutation(
    (data: DealUpdate) => updateDeal(deal.id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("deals");
        setIsEditing(false);
      },
    }
  );

  // Mutation for deleting a deal
  const deleteDealMutation = useMutation(() => deleteDeal(deal.id), {
    onSuccess: () => {
      queryClient.invalidateQueries("deals");
      onClose();
    },
  });

  // Mutation for linking an estimate to a deal
  const linkEstimateMutation = useMutation(
    (params: { estimateId: string; estimateType: "draft" | "harvest" }) =>
      linkDealEstimate(dealId, params.estimateId, params.estimateType),
    {
      onSuccess: async (result, variables) => {
        if (result.success) {
          // Show success notification
          setNotification({
            type: "success",
            message: "Estimate linked successfully",
          });

          // Invalidate all relevant queries
          await Promise.all([
            // Invalidate deals list
            queryClient.invalidateQueries("deals"),

            // Invalidate the specific deal
            queryClient.invalidateQueries(["deal", dealId]),

            // Invalidate draft estimates
            queryClient.invalidateQueries("draftEstimates"),

            // Invalidate the specific draft estimate
            queryClient.invalidateQueries([
              "draftEstimate",
              variables.estimateId,
            ]),

            // Invalidate Harvest estimates
            queryClient.invalidateQueries("harvestEstimates"),
          ]);

          // Force refetch of all relevant data
          await Promise.all([
            queryClient.refetchQueries("deals"),
            queryClient.refetchQueries(["deal", dealId]),
            queryClient.refetchQueries("draftEstimates"),
            queryClient.refetchQueries(["draftEstimate", variables.estimateId]),
            queryClient.refetchQueries("harvestEstimates"),
          ]);

          // Force immediate refetch of the draft estimate with fresh data
          if (variables.estimateType === "draft") {
            console.log(
              "Forcing immediate refetch of draft estimate:",
              variables.estimateId
            );
            await queryClient.refetchQueries(
              ["draftEstimate", variables.estimateId],
              {
                active: true,
                exact: true,
                stale: false,
              }
            );
          }

          // Close the modal
          setIsEstimateLinkModalOpen(false);

          // Clear notification after 3 seconds
          setTimeout(() => {
            setNotification(null);
          }, 3000);
        } else {
          // Set error message for the modal
          setLinkError(result.error || "Failed to link estimate");

          // Show error notification
          setNotification({
            type: "error",
            message: result.error || "Failed to link estimate",
          });

          // Clear notification after 3 seconds
          setTimeout(() => {
            setNotification(null);
          }, 3000);
        }
      },
      onError: (error: Error) => {
        // Set error message for the modal
        setLinkError(error.message || "Failed to link estimate");

        // Show error notification
        setNotification({
          type: "error",
          message: error.message || "Failed to link estimate",
        });

        // Clear notification after 3 seconds
        setTimeout(() => {
          setNotification(null);
        }, 3000);
      },
    }
  );

  // Mutation for unlinking an estimate from a deal
  const unlinkEstimateMutation = useMutation(
    (params: { estimateId: string; estimateType: "draft" | "harvest" }) =>
      unlinkDealEstimate(dealId, params.estimateId, params.estimateType),
    {
      onSuccess: async (_, variables) => {
        // Show success notification
        setNotification({
          type: "success",
          message: "Estimate unlinked successfully",
        });

        // Invalidate all relevant queries
        await Promise.all([
          // Invalidate deals list
          queryClient.invalidateQueries("deals"),

          // Invalidate the specific deal
          queryClient.invalidateQueries(["deal", dealId]),

          // Invalidate draft estimates
          queryClient.invalidateQueries("draftEstimates"),

          // Invalidate the specific draft estimate
          queryClient.invalidateQueries([
            "draftEstimate",
            variables.estimateId,
          ]),

          // Invalidate Harvest estimates
          queryClient.invalidateQueries("harvestEstimates"),
        ]);

        // Force refetch of all relevant data
        await Promise.all([
          queryClient.refetchQueries("deals"),
          queryClient.refetchQueries(["deal", dealId]),
          queryClient.refetchQueries("draftEstimates"),
          queryClient.refetchQueries(["draftEstimate", variables.estimateId]),
          queryClient.refetchQueries("harvestEstimates"),
        ]);

        // Force immediate refetch of the draft estimate with fresh data
        if (variables.estimateType === "draft") {
          console.log(
            "Forcing immediate refetch of draft estimate:",
            variables.estimateId
          );
          await queryClient.refetchQueries(
            ["draftEstimate", variables.estimateId],
            {
              active: true,
              exact: true,
              stale: false,
            }
          );
        }

        // Clear notification after 3 seconds
        setTimeout(() => {
          setNotification(null);
        }, 3000);
      },
      onError: (error: Error) => {
        // Show error notification
        setNotification({
          type: "error",
          message: error.message || "Failed to unlink estimate",
        });

        // Clear notification after 3 seconds
        setTimeout(() => {
          setNotification(null);
        }, 3000);
      },
    }
  );

  // State for notifications
  const [linkError, setLinkError] = useState<string | undefined>(undefined);
  const [notification, setNotification] = useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);

  // Handle linking an estimate
  const handleLinkEstimate = (
    estimateId: string,
    type: "draft" | "harvest"
  ) => {
    // Clear any previous errors
    setLinkError(undefined);

    // Attempt to link the estimate
    linkEstimateMutation.mutate({ estimateId, estimateType: type });
  };

  // Handle unlinking an estimate
  const handleUnlinkEstimate = (
    estimateId: string,
    type: "draft" | "harvest"
  ) => {
    if (
      window.confirm(
        "Are you sure you want to unlink this estimate from the deal?"
      )
    ) {
      unlinkEstimateMutation.mutate({ estimateId, estimateType: type });
    }
  };

  // Open modal for linking a draft estimate
  const openDraftEstimateModal = () => {
    setEstimateType("draft");
    setIsEstimateLinkModalOpen(true);
  };

  // Open modal for linking a Harvest estimate
  const openHarvestEstimateModal = () => {
    setEstimateType("harvest");
    setIsEstimateLinkModalOpen(true);
  };

  // Get available draft estimates (excluding already linked ones)
  const getAvailableDraftEstimates = () => {
    const linkedDraftIds =
      deal.estimates
        ?.filter((e: { type: string }) => e.type === "draft")
        .map((e: { id: string }) => e.id) || [];

    return draftEstimates.filter(
      (e: { uuid: string }) => !linkedDraftIds.includes(e.uuid)
    );
  };

  // Get available Harvest estimates (excluding already linked ones)
  const getAvailableHarvestEstimates = () => {
    const linkedHarvestIds =
      deal.estimates
        ?.filter((e: { type: string }) => e.type === "harvest")
        .map((e: { id: string }) => e.id) || [];

    return harvestEstimates.filter(
      (e: { id: number | string }) =>
        !linkedHarvestIds.includes(e.id.toString())
    );
  };

  // Handle form input changes
  const handleChange = (e: any) => {
    const { name, value, type } = e.target;

    // Handle number inputs
    if (type === "number") {
      if (name === "probability") {
        // Convert percentage (0-100) to decimal (0-1) for storage
        setFormData({
          ...formData,
          [name]: value === "" ? undefined : Number(value) / 100,
        });
      } else {
        setFormData({
          ...formData,
          [name]: value === "" ? undefined : Number(value),
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e: any) => {
    e.preventDefault();
    updateDealMutation.mutate(formData);
  };

  // Handle deal deletion with confirmation
  const handleDelete = () => {
    if (
      window.confirm(
        "Are you sure you want to delete this deal? This action cannot be undone."
      )
    ) {
      deleteDealMutation.mutate();
    }
  };

  // Format currency value
  const formatCurrency = (value?: number, currency?: string): string => {
    if (value === undefined || value === null) {
      console.log("formatCurrency received undefined or null value");
      return "N/A";
    }

    try {
      return new Intl.NumberFormat("en-AU", {
        style: "currency",
        currency: currency || "AUD",
        maximumFractionDigits: 0,
      }).format(value);
    } catch (error) {
      console.error("Error formatting currency value:", error, "Value:", value);
      return "Error";
    }
  };

  // Format date to local format
  const formatDate = (dateString?: string): string => {
    if (!dateString) return "No date set";

    return new Date(dateString).toLocaleDateString("en-AU", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-2">
      {/* Notification */}
      {notification && (
        <div
          className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-md shadow-lg ${
            notification.type === "success"
              ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-800"
              : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 border border-red-200 dark:border-red-800"
          }`}
        >
          <div className="flex items-center">
            {notification.type === "success" ? (
              <CheckCircleIcon className="w-5 h-5 mr-2" />
            ) : (
              <ExclamationCircleIcon className="w-5 h-5 mr-2" />
            )}
            <span>{notification.message}</span>
          </div>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? "Edit Deal" : "Deal Details"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {isEditing ? (
            // Edit form
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Deal Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name || ""}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Stage
                  </label>
                  <select
                    name="stage"
                    value={formData.stage || ""}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="Lead">Lead</option>
                    <option value="Qualified">Qualified</option>
                    <option value="Proposal">Proposal</option>
                    <option value="Negotiation">Negotiation</option>
                    <option value="Closed Won">Closed Won</option>
                    <option value="Closed Lost">Closed Lost</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Priority
                  </label>
                  <select
                    name="priority"
                    value={formData.priority || ""}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">None</option>
                    <option value="Low">Low</option>
                    <option value="Medium">Medium</option>
                    <option value="High">High</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Value
                  </label>
                  <input
                    type="number"
                    name="value"
                    value={formData.value === undefined ? "" : formData.value}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Currency
                  </label>
                  <select
                    name="currency"
                    value={formData.currency || "AUD"}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="AUD">AUD</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Probability (%)
                  </label>
                  <input
                    type="number"
                    name="probability"
                    min="0"
                    max="100"
                    value={
                      formData.probability === undefined
                        ? ""
                        : Math.round(formData.probability * 100)
                    }
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Expected Close Date
                  </label>
                  <input
                    type="date"
                    name="expectedCloseDate"
                    value={formData.expectedCloseDate || ""}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description || ""}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="flex justify-between pt-4">
                <button
                  type="button"
                  onClick={() => setIsEditing(false)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={handleDelete}
                    className="px-4 py-2 border border-red-300 dark:border-red-700 rounded-md shadow-sm text-sm font-medium text-red-700 dark:text-red-200 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Delete
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    disabled={updateDealMutation.isLoading}
                  >
                    {updateDealMutation.isLoading
                      ? "Saving..."
                      : "Save Changes"}
                  </button>
                </div>
              </div>
            </form>
          ) : (
            // View mode
            <div className="space-y-6">
              {/* Deal info */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {deal.name}
                  </h3>
                  {deal.company && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {deal.company.name}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Stage
                    </p>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {deal.stage}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Priority
                    </p>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {deal.priority || "None"}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Value
                    </p>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {deal.value
                        ? formatCurrency(deal.value, deal.currency)
                        : "Not set"}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Probability
                    </p>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {deal.probability !== undefined
                        ? `${Math.round(deal.probability * 100)}%`
                        : "Not set"}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Expected Close Date
                    </p>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {deal.expectedCloseDate
                        ? formatDate(deal.expectedCloseDate)
                        : "Not set"}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Created
                    </p>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {formatDate(deal.createdAt)}
                    </p>
                  </div>
                </div>

                {deal.description && (
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Description
                    </p>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {deal.description}
                    </p>
                  </div>
                )}
              </div>

              {/* Linked Estimates section */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <h4 className="text-base font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <DocumentTextIcon className="w-5 h-5 mr-1.5 text-emerald-500" />
                  Linked Estimates
                </h4>

                {/* Upstream Estimate Section */}
                <div className="mb-4">
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Upstream Estimate
                  </h5>

                  {deal.estimates?.some(
                    (e: { type: string }) => e.type === "draft"
                  ) ? (
                    // Show linked Upstream estimate
                    (() => {
                      const estimate = deal.estimates?.find(
                        (e: { type: string }) => e.type === "draft"
                      );
                      if (!estimate) return null;

                      // Find the estimate summary in the list
                      const estimateSummary = draftEstimates.find(
                        (e: { uuid: string }) => e.uuid === estimate.id
                      );

                      return (
                        <div className="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 rounded-md p-3">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {estimateSummary?.projectName ||
                                  fullDraftEstimateDetails?.projectName ||
                                  "Unnamed Project"}
                              </div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">
                                {estimateSummary?.clientName ||
                                  fullDraftEstimateDetails?.clientName ||
                                  "Unknown Client"}
                              </div>
                            </div>
                            <span className="text-xs bg-emerald-100 text-emerald-800 dark:bg-emerald-800 dark:text-emerald-200 px-2 py-0.5 rounded-full">
                              {estimate.id.substring(0, 8)}
                            </span>
                          </div>

                          <div className="grid grid-cols-2 gap-2 mb-3 text-xs">
                            <div>
                              <span className="text-gray-500 dark:text-gray-400">
                                Value:
                              </span>{" "}
                              <span className="font-medium text-gray-900 dark:text-white">
                                {isLoadingDraftDetails ? (
                                  "Loading..."
                                ) : isDraftError ? (
                                  "Error loading"
                                ) : !fullDraftEstimateDetails ? (
                                  "No data"
                                ) : fullDraftEstimateDetails.totalFees ===
                                  undefined ? (
                                  "Value not calculated"
                                ) : (
                                  <>
                                    {formatCurrency(
                                      fullDraftEstimateDetails.totalFees,
                                      "AUD"
                                    )}
                                    {/* Debug info */}
                                    <span className="hidden">
                                      {JSON.stringify({
                                        id: linkedDraftEstimateId,
                                        hasData: !!fullDraftEstimateDetails,
                                        totalFees:
                                          fullDraftEstimateDetails.totalFees,
                                      })}
                                    </span>
                                  </>
                                )}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-500 dark:text-gray-400">
                                Timeline:
                              </span>{" "}
                              <span className="font-medium text-gray-900 dark:text-white">
                                {formatDate(
                                  estimateSummary?.startDate ||
                                    fullDraftEstimateDetails?.startDate
                                )}{" "}
                                -{" "}
                                {formatDate(
                                  estimateSummary?.endDate ||
                                    fullDraftEstimateDetails?.endDate
                                )}
                              </span>
                            </div>
                          </div>

                          <div className="flex justify-between items-center">
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Linked on {formatDate(estimate.linkedAt)}
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() =>
                                  handleUnlinkEstimate(estimate.id, "draft")
                                }
                                className="inline-flex items-center text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                                title="Unlink estimate"
                              >
                                <TrashIcon className="w-3.5 h-3.5 mr-1" />
                                Unlink
                              </button>
                              <a
                                href={`/estimates/${estimate.id}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center text-xs text-emerald-600 dark:text-emerald-400 hover:text-emerald-800 dark:hover:text-emerald-300"
                              >
                                <ArrowTopRightOnSquareIcon className="w-3.5 h-3.5 mr-1" />
                                View
                              </a>
                            </div>
                          </div>
                        </div>
                      );
                    })()
                  ) : (
                    // Show buttons if no Upstream estimate is linked
                    <div className="flex space-x-2">
                      <button
                        onClick={openDraftEstimateModal}
                        className="flex-1 flex items-center justify-center px-4 py-2 border border-dashed border-emerald-300 dark:border-emerald-700 rounded-md text-sm text-emerald-600 dark:text-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20"
                      >
                        <LinkIcon className="w-4 h-4 mr-1.5" />
                        Link Upstream Estimate
                      </button>
                      <Link
                        to="/estimates/new"
                        className="flex-1 flex items-center justify-center px-4 py-2 border border-dashed border-emerald-300 dark:border-emerald-700 rounded-md text-sm text-emerald-600 dark:text-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-4 h-4 mr-1.5"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M12 4.5v15m7.5-7.5h-15"
                          />
                        </svg>
                        Create Estimate
                      </Link>
                    </div>
                  )}
                </div>

                {/* Harvest Estimate Section */}
                <div>
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Harvest Estimate
                  </h5>

                  {deal.estimates?.some(
                    (e: { type: string }) => e.type === "harvest"
                  ) ? (
                    // Show linked Harvest estimate
                    (() => {
                      const estimate = deal.estimates?.find(
                        (e: { type: string }) => e.type === "harvest"
                      );
                      if (!estimate) return null;

                      // Find the full estimate details
                      const estimateDetails = harvestEstimates.find(
                        (e: { id: number | string }) =>
                          e.id.toString() === estimate.id
                      );

                      return (
                        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {estimateDetails?.subject || "Unnamed Estimate"}
                              </div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">
                                {estimateDetails?.client?.name ||
                                  "Unknown Client"}
                              </div>
                            </div>
                            <span className="text-xs bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200 px-2 py-0.5 rounded-full">
                              #{estimate.id}
                            </span>
                          </div>

                          {estimateDetails && (
                            <div className="grid grid-cols-2 gap-2 mb-3 text-xs">
                              <div>
                                <span className="text-gray-500 dark:text-gray-400">
                                  Value:
                                </span>{" "}
                                <span className="font-medium text-gray-900 dark:text-white">
                                  {formatCurrency(
                                    parseFloat(estimateDetails.amount || "0"),
                                    "AUD"
                                  )}
                                </span>
                              </div>
                              <div>
                                <span className="text-gray-500 dark:text-gray-400">
                                  Status:
                                </span>{" "}
                                <span className="font-medium text-gray-900 dark:text-white capitalize">
                                  {estimateDetails.state || "unknown"}
                                </span>
                              </div>
                            </div>
                          )}

                          <div className="flex justify-between items-center">
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Linked on {formatDate(estimate.linkedAt)}
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() =>
                                  handleUnlinkEstimate(estimate.id, "harvest")
                                }
                                className="inline-flex items-center text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                                title="Unlink estimate"
                              >
                                <TrashIcon className="w-3.5 h-3.5 mr-1" />
                                Unlink
                              </button>
                              <a
                                href={`https://onbord.harvestapp.com/estimates/${estimate.id}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                              >
                                <ArrowTopRightOnSquareIcon className="w-3.5 h-3.5 mr-1" />
                                View in Harvest
                              </a>
                            </div>
                          </div>
                        </div>
                      );
                    })()
                  ) : (
                    // Show link button if no Harvest estimate is linked
                    <button
                      onClick={openHarvestEstimateModal}
                      className="w-full flex items-center justify-center px-4 py-2 border border-dashed border-green-300 dark:border-green-700 rounded-md text-sm text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20"
                    >
                      <LinkIcon className="w-4 h-4 mr-1.5" />
                      Link Harvest Estimate
                    </button>
                  )}
                </div>
              </div>

              {/* Notes section */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <h4 className="text-base font-medium text-gray-900 dark:text-white mb-4">
                  Notes
                </h4>
                <NotesTimeline dealId={deal.id} notes={deal.notes || []} />
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {!isEditing && (
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-between space-x-2">
            {/* Close Button */}
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
            >
              Close
            </button>

            {/* Edit Buttons */}
            <div className="flex space-x-2">
              <a
                href={`/crm/deals/${deal.id}`}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 inline-flex items-center justify-center"
              >
                Advanced Edit
              </a>
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Quick Edit
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Estimate Link Modal */}
      <EstimateLinkModal
        isOpen={isEstimateLinkModalOpen}
        onClose={() => {
          setIsEstimateLinkModalOpen(false);
          setLinkError(undefined); // Clear error when closing modal
        }}
        estimateType={estimateType}
        draftEstimates={getAvailableDraftEstimates()}
        harvestEstimates={getAvailableHarvestEstimates()}
        onLinkEstimate={handleLinkEstimate}
        isLoading={linkEstimateMutation.isLoading}
        error={linkError}
      />
    </div>
  );
};

export default DealDetail;
