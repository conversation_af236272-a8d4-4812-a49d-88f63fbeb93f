/**
 * Activity Feed Page
 *
 * This component displays the main activity feed dashboard with filters,
 * timeline, and real-time updates.
 */

import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "react-query";
import { getActivityFeed, getActivityStats } from "../../api/activity";
import {
  ActivityFilters as ActivityFiltersType,
  ActivityFeedResponse,
  ActivityStats,
  Activity,
} from "../../types/activity-types";
import ActivityTimeline from "./ActivityTimeline";
import ActivityFiltersComponent from "./ActivityFilters";
import ActivityStatsCard from "./ActivityStatsCard";
import { useEvents } from "../../contexts/EventContext";

/**
 * Activity Feed Page component
 */
const ActivityFeedPage = () => {
  const [filters, setFilters] = useState<ActivityFiltersType>({
    limit: 20,
    offset: 0,
  });
  const [allActivities, setAllActivities] = useState<Activity[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const queryClient = useQueryClient();
  const events = useEvents();

  // Query for activity feed
  const {
    data: activityFeedData,
    isLoading: isLoadingFeed,
    error: feedError,
    refetch: refetchFeed,
  } = useQuery<ActivityFeedResponse>(
    ["activity-feed", filters],
    () => getActivityFeed(filters),
    {
      keepPreviousData: true,
      staleTime: 30000, // 30 seconds
      refetchOnMount: true, // Ensure fresh data on component mount
      refetchOnWindowFocus: true, // Refresh when window regains focus
    }
  );

  // Query for activity stats
  const {
    data: statsData,
    isLoading: isLoadingStats,
    error: statsError,
  } = useQuery<ActivityStats>(["activity-stats"], getActivityStats, {
    staleTime: 60000, // 1 minute
    refetchOnMount: true, // Ensure fresh stats on component mount
    refetchOnWindowFocus: true, // Refresh when window regains focus
  });

  // Update activities and hasMore when data changes
  useEffect(() => {
    if (activityFeedData) {
      if (filters.offset === 0) {
        // Reset to new data (filters changed or refresh)
        setAllActivities(activityFeedData.activities || []);
      } else {
        // Append new data (load more)
        setAllActivities((prev: Activity[]) => {
          const existingIds = new Set(prev.map((a: Activity) => a.id));
          const newActivities = (activityFeedData.activities || []).filter((a: Activity) => !existingIds.has(a.id));
          return [...prev, ...newActivities];
        });
      }
      setHasMore(activityFeedData.hasMore || false);
    }
  }, [activityFeedData, filters.offset]);

  // Set up real-time updates and refresh on page load
  useEffect(() => {
    // Force refresh when component mounts
    queryClient.invalidateQueries(["activity-feed"]);
    queryClient.invalidateQueries(["activity-stats"]);

    // Listen for activity events
    const handleActivityCreated = () => {
      // Reset pagination to get fresh data including new activities
      setFilters((prev: ActivityFiltersType) => ({ ...prev, offset: 0 })); // Reset pagination
      queryClient.invalidateQueries(["activity-feed"]);
      queryClient.invalidateQueries(["activity-stats"]);
    };

    const handleActivityUpdated = () => {
      // Reset pagination to get fresh data
      setFilters((prev: ActivityFiltersType) => ({ ...prev, offset: 0 })); // Reset pagination
      queryClient.invalidateQueries(["activity-feed"]);
    };

    const handleActivityDeleted = () => {
      // Reset pagination to get fresh data
      setFilters((prev: ActivityFiltersType) => ({ ...prev, offset: 0 })); // Reset pagination
      queryClient.invalidateQueries(["activity-feed"]);
      queryClient.invalidateQueries(["activity-stats"]);
    };

    // Subscribe to events
    events.subscribe("activity:created", handleActivityCreated);
    events.subscribe("activity:updated", handleActivityUpdated);
    events.subscribe("activity:deleted", handleActivityDeleted);

    // Cleanup
    return () => {
      events.unsubscribe("activity:created", handleActivityCreated);
      events.unsubscribe("activity:updated", handleActivityUpdated);
      events.unsubscribe("activity:deleted", handleActivityDeleted);
    };
  }, [events, queryClient]);

  // Handle filter changes
  const handleFiltersChange = (newFilters: Partial<ActivityFiltersType>) => {
    setAllActivities([]); // Clear accumulated activities
    setFilters((prev: ActivityFiltersType) => ({
      ...prev,
      ...newFilters,
      offset: 0, // Reset offset when filters change
    }));
  };

  // Handle load more
  const handleLoadMore = () => {
    if (!hasMore || isLoadingFeed) return;

    setFilters((prev: ActivityFiltersType) => ({
      ...prev,
      offset: (prev.offset || 0) + (prev.limit || 20),
    }));
  };

  // Handle refresh
  const handleRefresh = () => {
    // Reset pagination without clearing activities immediately
    setFilters((prev: ActivityFiltersType) => ({ ...prev, offset: 0 })); // Reset pagination
    // Invalidate queries to force refresh
    queryClient.invalidateQueries(["activity-feed"]);
    queryClient.invalidateQueries(["activity-stats"]);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setAllActivities([]); // Clear accumulated activities
    setFilters({
      limit: 20,
      offset: 0,
    });
  };

  const activities = allActivities;
  const totalCount = activityFeedData?.total || 0;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Activity Feed
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Track all system activities, user actions, and integration
                events
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleRefresh}
                disabled={isLoadingFeed}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <svg
                  className={`-ml-1 mr-2 h-4 w-4 ${
                    isLoadingFeed ? "animate-spin" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        {statsData && !isLoadingStats && (
          <div className="mb-8">
            <ActivityStatsCard stats={statsData} />
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  Filters
                </h2>
                <button
                  onClick={handleClearFilters}
                  className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  Clear all
                </button>
              </div>
              <ActivityFiltersComponent
                filters={filters}
                onFiltersChange={handleFiltersChange}
              />
            </div>
          </div>

          {/* Activity Timeline */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              {/* Timeline Header */}
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                    Recent Activity
                  </h2>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {totalCount > 0 && (
                      <span>
                        Showing {activities.length} of {totalCount} activities
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Timeline Content */}
              <div className="p-6">
                {feedError ? (
                  <div className="text-center py-8">
                    <div className="text-red-500 dark:text-red-400 mb-2">
                      Error loading activities
                    </div>
                    <button
                      onClick={handleRefresh}
                      className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                    >
                      Try again
                    </button>
                  </div>
                ) : (
                  <ActivityTimeline
                    activities={activities}
                    isLoading={isLoadingFeed}
                    hasMore={hasMore}
                    onLoadMore={handleLoadMore}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityFeedPage;
