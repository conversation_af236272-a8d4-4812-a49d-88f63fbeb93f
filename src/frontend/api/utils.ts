// The old eventBus is no longer used - we'll use console logging directly

// Helper function to safely stringify objects with possible circular references
const safeStringify = (obj: any): string => {
  if (obj === null || obj === undefined) {
    return String(obj);
  }

  if (typeof obj !== 'object') {
    return String(obj);
  }

  // Handle special objects
  if (obj instanceof Error) {
    return `Error: ${obj.message}${obj.stack ? `\n${obj.stack}` : ''}`;
  }

  try {
    // Use a set to track objects we've seen
    const seen = new WeakSet();

    // Custom replacer function to handle circular references
    const replacer = (key: string, value: any) => {
      // Skip non-object values
      if (typeof value !== 'object' || value === null) {
        return value;
      }

      // Handle DOM nodes
      if (value instanceof Node) {
        return `[DOM Node: ${value.nodeName}]`;
      }

      // Check for circular reference
      if (seen.has(value)) {
        return '[Circular Reference]';
      }

      // Add this object to the set of seen objects
      seen.add(value);

      return value;
    };

    return JSON.stringify(obj, replacer, 2);
  } catch (err) {
    return `[Object - Failed to stringify: ${err instanceof Error ? err.message : String(err)}]`;
  }
};

/**
 * Fetch data from API with error handling, automatic retry for 401s, and logging
 * @param url API URL
 * @param options Fetch options
 * @returns Response data
 */
export const fetchFromApi = async (url: string, options: RequestInit = {}): Promise<any> => {
  // Generate request ID for correlation
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

  try {
    // Add cache-busting parameter to avoid cached responses
    const noCacheUrl = url.includes('?')
      ? `${url}&_t=${Date.now()}`
      : `${url}?_t=${Date.now()}`;

    // Log the request
    console.log(`API Request: ${options.method || 'GET'} ${url}`, {
      requestId,
      url,
      method: options.method || 'GET',
    });

    const startTime = Date.now();
    const response = await fetch(noCacheUrl, {
      ...options,
      credentials: 'include', // Include cookies for auth
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Cache-Control': 'no-cache, no-store',
        'Pragma': 'no-cache',
        'X-Request-ID': requestId,
        ...(options.headers || {})
      }
    });

    const duration = Date.now() - startTime;

    // Clone response before reading it (because reading the body consumes it)
    const responseClone = response.clone();

    if (!response.ok) {
      // Try to parse the error message from the response
      try {
        const errorData = await response.json();

        // Log the error response
        console.error(`API Error Response: ${response.status} ${response.statusText} for ${options.method || 'GET'} ${url} (${duration}ms)`, {
          requestId,
          status: response.status,
          statusText: response.statusText,
          url,
          method: options.method || 'GET',
          data: errorData,
          duration
        });

        // Enhanced handling for 401 errors, with special logic for preview deployments
        const isPreviewDeployment = window.location.hostname.includes('preview');
        if (response.status === 401) {
          console.log('Got 401 error:', errorData);

          // Check if we're in a preview deployment
          if (isPreviewDeployment) {
            console.log('In preview deployment, checking fallback auth options');

            // Perform advanced cookie diagnostics
            const allCookies = document.cookie.split(';').map(c => c.trim());
            const authCookies = allCookies.filter(c =>
              c.startsWith('preview_auth=') ||
              c.startsWith('preview_auth_alt=') ||
              c.startsWith('preview_auth_api=') ||
              c.startsWith('auth_test=')
            );

            console.log(`Found ${authCookies.length} auth-related cookies:`,
              authCookies.map(c => c.split('=')[0])
            );

            // First check if we have a redirectUrl provided in the error response
            if (errorData?.redirectUrl) {
              console.log('Using redirect URL from API:', errorData.redirectUrl);

              // Add cache-busting timestamp to prevent browser cache issues
              const redirectWithTimestamp = errorData.redirectUrl.includes('?')
                ? `${errorData.redirectUrl}&_t=${Date.now()}`
                : `${errorData.redirectUrl}?_t=${Date.now()}`;

              // Only redirect if we're not already in a redirect loop
              if (!window.location.href.includes('first_load=true')) {
                window.location.href = redirectWithTimestamp;
                return null; // Prevent further processing
              } else {
                console.log('Detected potential redirect loop, using direct auth endpoint');
                window.location.href = `/api/xero/auth?_t=${Date.now()}`;
                return null;
              }
            }
            // If no redirectUrl but we have auth cookies, try refreshing the page
            else if (authCookies.length > 0 && !window.location.href.includes('refresh=true')) {
              console.log('Auth cookies exist but not recognized by API, refreshing page');

              // Add a special parameter to detect refresh loops
              const currentUrl = new URL(window.location.href);
              currentUrl.searchParams.set('refresh', 'true');
              currentUrl.searchParams.set('_t', Date.now().toString());

              window.location.href = currentUrl.toString();
              return null;
            }
            // Last resort: direct auth endpoint
            else {
              console.log('Using direct auth endpoint as last resort');
              window.location.href = `/api/xero/auth?_t=${Date.now()}`;
              return null;
            }
          }
          // For non-preview deployments, use simpler handling
          else if (errorData?.redirectUrl) {
            console.log('Non-preview deployment, using provided redirect URL');
            window.location.href = errorData.redirectUrl;
            return null;
          }
        }

        // Create ApiError with proper status for better error handling
        const apiError = new Error(errorData.message || `Error: ${response.status} ${response.statusText}`);
        (apiError as any).status = response.status;
        (apiError as any).isRetryable = response.status >= 500 || response.status === 429;
        throw apiError;
      } catch (e) {
        // Log parse error
        console.error(`API Error: Failed to parse error response for ${options.method || 'GET'} ${url}`, {
          requestId,
          error: e instanceof Error ? e.message : String(e),
          status: response.status,
          statusText: response.statusText
        });

        const apiError = new Error(`Error: ${response.status} ${response.statusText}`);
        (apiError as any).status = response.status;
        (apiError as any).isRetryable = response.status >= 500 || response.status === 429;
        throw apiError;
      }
    }

    // For successful responses
    try {
      const responseData = await responseClone.json();

      // Log successful response (with limited data to avoid huge logs)
      // Safely get the size by using our safe stringify function
      const serializedSize = (() => {
        try {
          return safeStringify(responseData).length;
        } catch (e) {
          return -1; // Indicate we couldn't determine size
        }
      })();

      const responseInfo = {
        requestId,
        status: response.status,
        statusText: response.statusText,
        url,
        method: options.method || 'GET',
        dataSize: serializedSize,
        duration,
      };

      console.log(`API Response: ${response.status} ${response.statusText} for ${options.method || 'GET'} ${url} (${duration}ms)`, responseInfo);

      return responseData;
    } catch (e) {
      // Log JSON parse error but continue with original response
      console.warn(`API Warning: Could not parse JSON response for logging purposes`, {
        requestId,
        error: e instanceof Error ? e.message : String(e)
      });

      // Still try to parse the original response
      return await response.json();
    }
  } catch (error: any) {
    // Log network or other fetch errors
    console.error(`API Fetch Error: ${error.message || 'Unknown error'} for ${options.method || 'GET'} ${url}`, {
      requestId,
      url,
      method: options.method || 'GET',
      error: error.message || 'Unknown error',
      stack: error.stack
    });

    // Check if we're in a preview deployment for extra debug info
    if (window.location.hostname.includes('preview')) {
      console.log('Preview deployment detected in API error handler');
      console.log('Current cookies:', document.cookie);
    }

    // Enhanced error message for network/timeout errors
    const networkError = new Error(error.message || 'Failed to fetch from API');
    (networkError as any).isRetryable = true;
    (networkError as any).isNetworkError = true;
    throw networkError;
  }
};