import { fetchFromApi } from './utils';
import type { Company } from '../types/crm-types';

/**
 * Get all companies with radar information
 */
export const getRadarCompanies = async (): Promise<Company[]> => {
  // In the unified data model, we use the company table with radar_state field
  const response = await fetchFromApi<{ success: boolean; data: Company[] }>('/api/leads/companies');
  return response.data;
};

/**
 * Update a company's radar information
 */
export const updateRadarCompany = async (
  id: string,
  data: { radarState?: string; radarPriority?: string }
): Promise<Company> => {
  // In the unified data model, we update the radar_state and radar_priority fields in the company table
  const response = await fetchFromApi<{ success: boolean; data: Company }>(
    `/api/leads/companies/${id}/radar`,
    {
      method: 'PUT',
      body: JSON.stringify(data)
    }
  );
  return response.data;
};

/**
 * Get companies from Harvest API
 */
export const getHarvestCompanies = async (): Promise<any[]> => {
  try {
    console.log('Fetching companies from Harvest API');
    const response = await fetchFromApi<{ success: boolean; data: any[] }>('/api/harvest/clients');
    console.log(`Received ${response.data.length} companies from Harvest API`);

    // Log the first few companies for debugging
    if (response.data.length > 0) {
      console.log('Sample companies:');
      response.data.slice(0, 3).forEach((company: any) => {
        console.log(`- ID: ${company.id} (${typeof company.id}), Name: ${company.name}`);
      });
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching companies from Harvest API:', error);
    throw error;
  }
};

/**
 * Add a company to the radar
 */
export const addCompanyToRadar = async (
  companyId: string,
  radarState: string,
  priority?: string
): Promise<Company> => {
  try {
    console.log(`Adding company to radar: ID=${companyId}, State=${radarState}, Priority=${priority || 'none'}`);

    // In the unified data model, we update the radar_state and priority fields in the company table
    const response = await fetchFromApi<{ success: boolean; data: Company }>(
      '/api/leads/companies/add-to-radar',
      {
        method: 'POST',
        body: JSON.stringify({
          companyId,
          radarState,
          priority
        })
      }
    );

    console.log('Successfully added company to radar:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error adding company to radar:', error);
    throw error;
  }
};

/**
 * Remove a company from the radar
 */
export const removeCompanyFromRadar = async (companyId: string): Promise<void> => {
  // In the unified data model, we set the radar_state to null in the company table
  await fetchFromApi<{ success: boolean; message: string }>(
    `/api/leads/companies/${companyId}/remove-from-radar`,
    {
      method: 'DELETE'
    }
  );
};
