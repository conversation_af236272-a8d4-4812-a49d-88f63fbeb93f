import { fetchFromApi } from './utils';
import {
  Deal,
  Contact,
  Company,
  Note,
  DealCreate,
  DealUpdate,
  ContactCreate,
  ContactUpdate,
  CompanyCreate,
  CompanyUpdate,
  NoteCreate,
  DealEstimate
} from '../types/crm-types';

/**
 * Get all deals
 */
export const getDeals = async (): Promise<Deal[]> => {
  const response = await fetchFromApi<{ success: boolean; data: Deal[] }>('/api/crm/deals');
  return response.data;
};

/**
 * Get a deal by ID
 */
export const getDealById = async (id: string): Promise<Deal> => {
  const response = await fetchFromApi<{ success: boolean; data: Deal }>(`/api/crm/deals/${id}`);
  return response.data;
};

/**
 * Create a new deal
 */
export const createDeal = async (dealData: DealCreate): Promise<Deal> => {
  const response = await fetchFromApi<{ success: boolean; data: Deal }>('/api/crm/deals', {
    method: 'POST',
    body: JSON.stringify(dealData),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return response.data;
};

/**
 * Update a deal
 */
export const updateDeal = async (id: string, dealData: DealUpdate): Promise<Deal> => {
  const response = await fetchFromApi<{ success: boolean; data: Deal }>(`/api/crm/deals/${id}`, {
    method: 'PUT',
    body: JSON.stringify(dealData),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return response.data;
};

/**
 * Delete a deal
 */
export const deleteDeal = async (id: string): Promise<void> => {
  await fetchFromApi<{ success: boolean; message: string }>(`/api/crm/deals/${id}`, {
    method: 'DELETE'
  });
};

/**
 * Get all contacts
 */
export const getContacts = async (): Promise<Contact[]> => {
  const response = await fetchFromApi<{ success: boolean; data: Contact[] }>('/api/crm/contacts');
  return response.data;
};

/**
 * Get a contact by ID
 */
export const getContactById = async (id: string): Promise<Contact> => {
  const response = await fetchFromApi<{ success: boolean; data: Contact }>(`/api/crm/contacts/${id}`);
  return response.data;
};

/**
 * Create a new contact
 */
export const createContact = async (contactData: ContactCreate): Promise<Contact> => {
  const response = await fetchFromApi<{ success: boolean; data: Contact }>('/api/crm/contacts', {
    method: 'POST',
    body: JSON.stringify(contactData),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return response.data;
};

/**
 * Update a contact
 */
export const updateContact = async (id: string, contactData: ContactUpdate): Promise<Contact> => {
  const response = await fetchFromApi<{ success: boolean; data: Contact }>(`/api/crm/contacts/${id}`, {
    method: 'PUT',
    body: JSON.stringify(contactData),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return response.data;
};

/**
 * Delete a contact
 */
export const deleteContact = async (id: string): Promise<void> => {
  await fetchFromApi<{ success: boolean; message: string }>(`/api/crm/contacts/${id}`, {
    method: 'DELETE'
  });
};

/**
 * Get all companies
 */
export const getCompanies = async (): Promise<Company[]> => {
  const response = await fetchFromApi<{ success: boolean; data: Company[] }>('/api/crm/companies');
  return response.data;
};

/**
 * Get a company by ID
 */
export const getCompanyById = async (id: string): Promise<Company> => {
  const response = await fetchFromApi<{ success: boolean; data: Company }>(`/api/crm/companies/${id}`);
  return response.data;
};

/**
 * Create a new company
 */
export const createCompany = async (companyData: CompanyCreate): Promise<Company> => {
  const response = await fetchFromApi<{ success: boolean; data: Company }>('/api/crm/companies', {
    method: 'POST',
    body: JSON.stringify(companyData),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return response.data;
};

/**
 * Update a company
 */
export const updateCompany = async (id: string, companyData: CompanyUpdate): Promise<Company> => {
  const response = await fetchFromApi<{ success: boolean; data: Company }>(`/api/crm/companies/${id}`, {
    method: 'PUT',
    body: JSON.stringify(companyData),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return response.data;
};

/**
 * Delete a company
 */
export const deleteCompany = async (id: string): Promise<void> => {
  await fetchFromApi<{ success: boolean; message: string }>(`/api/crm/companies/${id}`, {
    method: 'DELETE'
  });
};

/**
 * Associate a contact with a deal
 */
export const associateContactWithDeal = async (dealId: string, contactId: string, role?: string): Promise<void> => {
  await fetchFromApi<{ success: boolean; message: string }>(`/api/crm/deals/${dealId}/contacts/${contactId}`, {
    method: 'POST',
    body: JSON.stringify({ role }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

/**
 * Disassociate a contact from a deal
 */
export const disassociateContactFromDeal = async (dealId: string, contactId: string): Promise<void> => {
  await fetchFromApi<{ success: boolean; message: string }>(`/api/crm/deals/${dealId}/contacts/${contactId}`, {
    method: 'DELETE'
  });
};

/**
 * Add a note to a deal
 */
export const addNoteToDeal = async (noteData: NoteCreate): Promise<Note> => {
  const response = await fetchFromApi<{ success: boolean; data: Note }>(`/api/crm/deals/${noteData.dealId}/notes`, {
    method: 'POST',
    body: JSON.stringify({
      content: noteData.content,
      createdBy: noteData.createdBy
    }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return response.data;
};

/**
 * Delete a note
 */
export const deleteNote = async (id: string): Promise<void> => {
  await fetchFromApi<{ success: boolean; message: string }>(`/api/crm/notes/${id}`, {
    method: 'DELETE'
  });
};

/**
 * Get estimates linked to a deal
 */
export const getDealEstimates = async (dealId: string): Promise<DealEstimate[]> => {
  const response = await fetchFromApi<{ success: boolean; data: DealEstimate[] }>(`/api/crm/deals/${dealId}/estimates`);
  return response.data;
};

/**
 * Link an estimate to a deal
 * @returns An object with success status and optional error message
 */
export const linkDealEstimate = async (
  dealId: string,
  estimateId: string,
  estimateType: 'draft' | 'harvest',
  linkedBy?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const response = await fetchFromApi<{ success: boolean; error?: string; message?: string }>(
      `/api/crm/deals/${dealId}/estimates`,
      {
        method: 'POST',
        body: JSON.stringify({ estimateId, estimateType, linkedBy }),
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    return { success: true };
  } catch (error: any) {
    // Extract error message from the API response if available
    const errorMessage = error.response?.data?.error || 'Failed to link estimate to deal';
    return { success: false, error: errorMessage };
  }
};

/**
 * Unlink an estimate from a deal
 * @returns An object with success status and optional error message
 */
export const unlinkDealEstimate = async (
  dealId: string,
  estimateId: string,
  estimateType: 'draft' | 'harvest'
): Promise<{ success: boolean; error?: string }> => {
  try {
    await fetchFromApi<{ success: boolean }>(
      `/api/crm/deals/${dealId}/estimates/${estimateId}?estimateType=${estimateType}`,
      { method: 'DELETE' }
    );
    return { success: true };
  } catch (error: any) {
    // Extract error message from the API response if available
    const errorMessage = error.response?.data?.error || 'Failed to unlink estimate from deal';
    return { success: false, error: errorMessage };
  }
};

/**
 * Get deals linked to an estimate
 * @returns An array of deals linked to the specified estimate
 */
export const getLinkedDealsForEstimate = async (
  estimateId: string,
  estimateType: 'draft' | 'harvest'
): Promise<Deal[]> => {
  try {
    const response = await fetchFromApi<{ success: boolean; data: Deal[] }>(
      `/api/crm/estimates/${estimateId}/deals?estimateType=${estimateType}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to get linked deals for estimate:', error);
    return [];
  }
};

/**
 * Get all companies with their linking status to external systems
 */
export const getCompaniesWithLinkingStatus = async (): Promise<Array<Company & {
  linkingStatus: 'both' | 'hubspot_only' | 'harvest_only' | 'none'
}>> => {
  const response = await fetchFromApi<{
    success: boolean;
    data: Array<Company & {
      linkingStatus: 'both' | 'hubspot_only' | 'harvest_only' | 'none'
    }>;
  }>('/api/crm/companies/linking-status');
  return response.data;
};

/**
 * Get companies that need linking between systems (legacy)
 */
export const getUnlinkedCompanies = async (): Promise<{
  hubspotOnly: Company[];
  harvestOnly: Company[];
  bothMissing: Company[];
}> => {
  const response = await fetchFromApi<{
    success: boolean;
    data: {
      hubspotOnly: Company[];
      harvestOnly: Company[];
      bothMissing: Company[];
    };
  }>('/api/crm/companies/unlinked');
  return response.data;
};

/**
 * Link a company to HubSpot
 */
export const linkCompanyToHubSpot = async (
  companyId: string,
  hubspotId: string
): Promise<Company> => {
  const response = await fetchFromApi<{ success: boolean; data: Company }>(
    `/api/crm/companies/${companyId}/link-hubspot`,
    {
      method: 'POST',
      body: JSON.stringify({ hubspotId }),
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
  return response.data;
};

/**
 * Link a company to Harvest
 */
export const linkCompanyToHarvest = async (
  companyId: string,
  harvestId: number
): Promise<Company> => {
  const response = await fetchFromApi<{ success: boolean; data: Company }>(
    `/api/crm/companies/${companyId}/link-harvest`,
    {
      method: 'POST',
      body: JSON.stringify({ harvestId }),
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
  return response.data;
};

/**
 * Unlink a company from HubSpot
 */
export const unlinkCompanyFromHubSpot = async (companyId: string): Promise<Company> => {
  const response = await fetchFromApi<{ success: boolean; data: Company }>(
    `/api/crm/companies/${companyId}/link-hubspot`,
    {
      method: 'DELETE'
    }
  );
  return response.data;
};

/**
 * Unlink a company from Harvest
 */
export const unlinkCompanyFromHarvest = async (companyId: string): Promise<Company> => {
  const response = await fetchFromApi<{ success: boolean; data: Company }>(
    `/api/crm/companies/${companyId}/link-harvest`,
    {
      method: 'DELETE'
    }
  );
  return response.data;
};

/**
 * Merge two companies
 * @param sourceCompanyId Company to merge from (will be deleted)
 * @param targetCompanyId Company to merge to (will be updated)
 * @returns The merged company
 */
export const mergeCompanies = async (sourceCompanyId: string, targetCompanyId: string): Promise<Company> => {
  const response = await fetchFromApi<{ success: boolean; data: Company }>(
    `/api/crm/companies/${sourceCompanyId}/merge/${targetCompanyId}`,
    {
      method: 'POST'
    }
  );
  return response.data;
};


