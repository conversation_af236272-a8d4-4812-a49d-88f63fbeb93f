import { fetchFrom<PERSON>pi } from './utils';
import { HubSpotImport } from '../types/crm-types';

/**
 * Check if HubSpot integration is configured
 */
export const checkHubSpotStatus = async (): Promise<{ isConfigured: boolean }> => {
  const response = await fetchFromApi<{ success: boolean; data: { isConfigured: boolean } }>('/api/hubspot/status');
  return response.data;
};

/**
 * Configure HubSpot integration with access token
 */
export const configureHubSpot = async (accessToken: string): Promise<void> => {
  await fetchFromApi<{ success: boolean }>('/api/hubspot/configure', {
    method: 'POST',
    body: JSON.stringify({ accessToken }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

/**
 * Remove HubSpot integration configuration
 */
export const removeHubSpotConfiguration = async (): Promise<void> => {
  await fetchFromApi<{ success: boolean }>('/api/hubspot/configure', {
    method: 'DELETE'
  });
};

/**
 * Import deals from HubSpot
 */
export const importDealsFromHubSpot = async (): Promise<{ count: number }> => {
  const response = await fetchFromApi<{ success: boolean; data: { count: number } }>('/api/hubspot/import/deals', {
    method: 'POST'
  });
  return response.data;
};

/**
 * Import contacts from HubSpot
 */
export const importContactsFromHubSpot = async (): Promise<{ count: number }> => {
  const response = await fetchFromApi<{ success: boolean; data: { count: number } }>('/api/hubspot/import/contacts', {
    method: 'POST'
  });
  return response.data;
};

/**
 * Import companies from HubSpot
 */
export const importCompaniesFromHubSpot = async (): Promise<{ count: number }> => {
  const response = await fetchFromApi<{ success: boolean; data: { count: number } }>('/api/hubspot/import/companies', {
    method: 'POST'
  });
  return response.data;
};

/**
 * Import all data from HubSpot (companies, deals, contacts)
 */
export const importAllFromHubSpot = async (): Promise<{
  totalCount: number;
  results: {
    companies: { success: boolean; count: number; error?: string };
    deals: { success: boolean; count: number; error?: string };
    contacts: { success: boolean; count: number; error?: string };
  };
}> => {
  const response = await fetchFromApi<{
    success: boolean;
    data: {
      totalCount: number;
      results: {
        companies: { success: boolean; count: number; error?: string };
        deals: { success: boolean; count: number; error?: string };
        contacts: { success: boolean; count: number; error?: string };
      };
    };
  }>('/api/hubspot/import/all', {
    method: 'POST'
  });
  return response.data;
};

/**
 * Get import history
 */
export const getHubSpotImportHistory = async (): Promise<HubSpotImport[]> => {
  const response = await fetchFromApi<{ success: boolean; data: HubSpotImport[] }>('/api/hubspot/import/history');
  return response.data;
};

/**
 * Get all companies from HubSpot for linking purposes
 */
export const getHubSpotCompaniesForLinking = async (): Promise<Array<{
  id: string;
  name: string;
  industry?: string;
  website?: string;
}>> => {
  const response = await fetchFromApi<{
    success: boolean;
    data: Array<{
      id: string;
      name: string;
      industry?: string;
      website?: string;
    }>;
  }>('/api/hubspot/companies');
  return response.data;
};

/**
 * Reset all CRM data (delete all deals, contacts, companies, notes, and associations)
 */
export const resetCRMData = async (): Promise<{
  dealsDeleted: number;
  contactsDeleted: number;
  companiesDeleted: number;
  notesDeleted: number;
}> => {
  const response = await fetchFromApi<{
    success: boolean;
    data: {
      dealsDeleted: number;
      contactsDeleted: number;
      companiesDeleted: number;
      notesDeleted: number;
    };
  }>('/api/hubspot/reset-crm-data', {
    method: 'DELETE'
  });
  return response.data;
};
