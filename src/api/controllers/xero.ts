import { Request, Response, NextFunction } from 'express';
import {
  getXeroService,
  BillService,
  PayrollService,
  SuperannuationService,
  ActivityStatementService
} from '../../services/xero';
import { getHarvestService } from '../../services/harvest';
import { jwtDecode } from 'jwt-decode';
import { ExpensesRepository } from '../repositories/expenses-repository';
import { CustomExpense } from '../../types';
import { XERO_SOURCES } from '../../constants/xero-backend';
import activityLogger from '../../utils/activity-logger';

// Extend the Express Request type to include session
declare module 'express-session' {
  interface SessionData {
    tokenSet?: any;
    decodedIdToken?: any;
    decodedAccessToken?: any;
    allTenants?: any[];
    activeTenant?: any;
    userInfo?: {
      name?: string;
      email?: string;
      sub?: string;
    };
    // Property for preview mode tracking
    previewMode?: boolean;
  }
}

export class XeroController {
  /**
   * Initiate the OAuth flow
   */
  public initiateAuth = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();
      const consentUrl = await xeroService.buildConsentUrl();
      res.redirect(consentUrl);
    } catch (error) {
      console.error('Error initiating auth:', error);
      res.status(500).json({
        error: 'Failed to initiate authorization',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Handle the OAuth callback
   */
  public handleCallback = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Force HTTPS protocol for production environments
      const protocol = process.env.NODE_ENV === 'production' ? 'https' : (req.protocol || 'http');
      const host = req.get('host') || '';
      const fullUrl = `${protocol}://${host}${req.originalUrl}`;

      console.log('Full callback URL being used:', fullUrl);
      console.log('Request headers:', {
        host: req.get('host'),
        forwardedHost: req.get('x-forwarded-host'),
        forwardedProto: req.get('x-forwarded-proto'),
        origin: req.get('origin')
      });

      const tokenSet = await xeroService.handleCallback(fullUrl);
      console.log('Token set received:', {
        hasIdToken: !!tokenSet.id_token,
        hasAccessToken: !!tokenSet.access_token,
        hasRefreshToken: !!tokenSet.refresh_token,
        expiresAt: tokenSet.expires_at
      });

      // Store token info in session
      if (req.session) {
        req.session.tokenSet = tokenSet;

        if (tokenSet.id_token) {
          const decodedIdToken = jwtDecode(tokenSet.id_token) as any;
          req.session.decodedIdToken = decodedIdToken;

          // Store user info for easy access
          req.session.userInfo = {
            name: decodedIdToken.name as string,
            email: decodedIdToken.email as string,
            sub: decodedIdToken.sub as string
          };
        }

        if (tokenSet.access_token) {
          req.session.decodedAccessToken = jwtDecode(tokenSet.access_token);
        }

        // Update tenants information
        await xeroService.getClient().updateTenants();
        const tenants = xeroService.getTenants();
        console.log(`Found ${tenants.length} tenants`);

        if (tenants.length > 0) {
          req.session.allTenants = tenants;
          req.session.activeTenant = tenants[0];
          console.log('Active tenant set:', tenants[0].tenantName);
        } else {
          console.warn('No tenants found after authentication');
        }
      }

      // Determine the frontend URL for redirection
      const isPreviewDeployment = process.env.RENDER_EXTERNAL_HOSTNAME?.includes('preview');
      let frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

      // CRITICAL: Use the same host that the request came from to maintain cookie context
      const requestHost = req.get('host');
      if (requestHost) {
        console.log(`Using request host for redirect: ${requestHost}`);
        frontendUrl = `${req.protocol}://${requestHost}`;
      } else if (isPreviewDeployment) {
        console.log('Preview deployment detected, using RENDER_EXTERNAL_HOSTNAME for redirect');
        frontendUrl = `https://${process.env.RENDER_EXTERNAL_HOSTNAME}`;
      }

      // Set cookies using the same domain as the request to ensure they're sent back
      const cookieOptions = {
        secure: process.env.NODE_ENV === 'production',
        httpOnly: false,
        sameSite: isPreviewDeployment ? 'none' as const : 'lax' as const,
        path: '/',
        maxAge: 24 * 60 * 60 * 1000, // 1 day
        // Don't set domain explicitly - let the browser use the current domain
        // This ensures cookies work across both custom domains and render.com domains
      };

      // Log cookie settings for debugging
      console.log('Setting cookies with options:', {
        secure: cookieOptions.secure,
        sameSite: cookieOptions.sameSite,
        domain: '(default - current domain)',
        host: req.get('host')
      });

      // Set a cookie that can be used to test if cookies are working
      res.cookie('auth_test', 'true', cookieOptions);

      // Force the session to save before redirecting to ensure cookie is set
      console.log('[DEBUG XERO CALLBACK] Attempting req.session.save()...');
      req.session.save((err) => {
        if (err) {
          console.error('[DEBUG XERO CALLBACK] Error saving session before redirect:', err);
        } else {
          console.log('[DEBUG XERO CALLBACK] req.session.save() completed successfully.');
        }

        // Removed preview-specific fallback cookie setting logic

        // Add success parameter to redirect URL
        const redirectUrl = new URL(`${frontendUrl}/`);
        redirectUrl.searchParams.append('auth_success', 'true');
        redirectUrl.searchParams.append('sid', req.sessionID);
        redirectUrl.searchParams.append('ts', Date.now().toString());

        console.log(`Redirecting to frontend URL: ${redirectUrl.toString()}`);
        res.redirect(redirectUrl.toString());
      });
    } catch (error) {
      console.error('Error handling callback:', error);
      res.status(500).json({
        error: 'Failed to complete authorization',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Check auth status
   */
  public checkAuthStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      console.log('Check auth status request received, session ID:', req.sessionID);
      console.log('Request cookies:', req.headers.cookie);
      console.log('Request host:', req.get('host'));
      console.log('Request origin:', req.get('origin'));
      console.log('Request referer:', req.get('referer'));

      // For preview deployments, add additional diagnostics
      const isPreviewDeployment = process.env.RENDER_EXTERNAL_HOSTNAME?.includes('preview') || false;
      if (isPreviewDeployment) {
        console.log('Preview deployment detected in auth status check');
        console.log('RENDER_EXTERNAL_HOSTNAME:', process.env.RENDER_EXTERNAL_HOSTNAME);
        console.log('X-Forwarded-Host:', req.get('x-forwarded-host'));
        console.log('X-Forwarded-Proto:', req.get('x-forwarded-proto'));
      }

      // Check if we're in preview and it's the first load - for preview deployments,
      // don't redirect directly but return a redirect URL for the client to use
      const isFirstLoad = req.query.first_load === 'true';
      const hasAuthorizationCode = req.query.code && req.query.state;

      if (isPreviewDeployment && (isFirstLoad || hasAuthorizationCode)) {
        console.log('Preview first load or authorization code detected, sending redirect URL');

        // Instead of directly redirecting, return a URL for the client to use
        const xeroService = getXeroService();
        const consentUrl = await xeroService.buildConsentUrl();

        // Return a JSON response with the redirect URL
        res.json({
          authenticated: false,
          redirectUrl: consentUrl,
          reason: 'Preview deployment first load',
          sessionId: req.sessionID,
          preview: true
        });
        return;
      }

      // Verify we have a valid session with required data
      if (!req.session || !req.session.tokenSet) {
        console.log('No valid session found, returning not authenticated');
        res.json({
          authenticated: false,
          reason: 'No valid session found',
          sessionId: req.sessionID,
          hasCookie: !!req.headers.cookie,
          preview: isPreviewDeployment
        });
        return;
      }

      console.log('Session data:', {
        hasTokenSet: !!req.session.tokenSet,
        hasDecodedIdToken: !!req.session.decodedIdToken,
        hasUserInfo: !!req.session.userInfo,
        hasTenants: !!(req.session.allTenants && req.session.allTenants.length > 0)
      });

      const xeroService = getXeroService();
      const isAuthenticated = await xeroService.isAuthenticated();

      console.log('isAuthenticated result:', isAuthenticated);
      console.log('Tenants available:', xeroService.getTenants().length);

      // If not authenticated in preview, force new login
      if (isPreviewDeployment && !isAuthenticated) {
        console.log('Preview deployment detected but not authenticated, redirecting to auth');
        return this.initiateAuth(req, res);
      }

      const response = {
        authenticated: isAuthenticated,
        tenants: isAuthenticated ? xeroService.getTenants() : [],
        activeTenant: isAuthenticated ? xeroService.getTenants()[0] : null,
        user: req.session?.userInfo || null,
        sessionId: req.sessionID, // Include session ID for debugging
        preview: isPreviewDeployment,
        host: req.get('host'),
        previewContext: isPreviewDeployment,
        renderHost: process.env.RENDER_EXTERNAL_HOSTNAME,
        // Additional data for debugging
        requestData: {
          method: req.method,
          protocol: req.protocol,
          secure: req.secure,
          hasSessionCookie: req.headers.cookie?.includes('connect.sid') || false,
          hasPreviewAuthCookie: req.headers.cookie?.includes('preview_auth') || false,
          origin: req.get('origin'),
          referer: req.get('referer')
        },
        timestamp: new Date().toISOString()
      };

      console.log('Sending auth status response:', {
        authenticated: response.authenticated,
        hasTenants: response.tenants.length > 0,
        hasActiveTenant: !!response.activeTenant,
        hasUser: !!response.user,
        preview: isPreviewDeployment
      });

      // Removed preview-specific fallback cookie setting logic

      // Explicitly save session before responding
      if (req.session.save) {
        req.session.save(err => {
          if (err) {
            console.error('Error saving session in auth-status:', err);
          }
          res.json(response);
        });
      } else {
        res.json(response);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      res.status(500).json({
        error: 'Failed to check authentication status',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get user information
   */
  public getUserInfo = async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.session?.decodedIdToken) {
        res.status(401).json({ error: 'Not authenticated' });
        return;
      }

      res.json(req.session.decodedIdToken);
    } catch (error) {
      console.error('Error getting user info:', error);
      res.status(500).json({
        error: 'Failed to get user information',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Logout from Xero
   */
  public logout = async (req: Request, res: Response): Promise<void> => {
    try {
      if (req.session) {
        // Clear session data
        delete req.session.tokenSet;
        delete req.session.decodedIdToken;
        delete req.session.decodedAccessToken;
        delete req.session.allTenants;
        delete req.session.activeTenant;
        delete req.session.userInfo;
      }

      res.json({ success: true, message: 'Logged out successfully' });
    } catch (error) {
      console.error('Error logging out:', error);
      res.status(500).json({
        error: 'Failed to logout',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get Xero organization info
   */
  public getOrganization = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const xeroService = getXeroService();
      const tenantId = xeroService.getActiveTenantId();

      if (!tenantId) {
        res.status(401).json({ error: 'No active Xero organization found. Please authenticate first.' });
        return;
      }

      const response = await xeroService.getClient().accountingApi.getOrganisations(tenantId);
      res.json(response.body);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get cash flow forecast
   */
  public getCashFlowForecast = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const xeroService = getXeroService();
      const tenantId = xeroService.getActiveTenantId();

      if (!tenantId) {
        res.status(401).json({ error: 'No active Xero organization found. Please authenticate first.' });
        return;
      }

      // Parse date parameters
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date();
      let endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date(startDate);

      // Default to 6 months if no end date provided
      if (startDate.getTime() === endDate.getTime()) {
        endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + 6);
      }

      // Format dates for API
      const formattedStartDate = startDate.toISOString().split('T')[0];
      const formattedEndDate = endDate.toISOString().split('T')[0];

      // Create an array of API calls to make in parallel
      const apiCalls = [
        // 1. Get bank summary report - shows current cash positions
        xeroService.getClient().accountingApi.getReportBankSummary(
          tenantId,
          formattedStartDate,
          formattedEndDate
        ),

        // 2. Get balance sheet - shows assets and liabilities
        xeroService.getClient().accountingApi.getReportBalanceSheet(
          tenantId,
          formattedEndDate
        ),

        // 3. Get profit and loss - shows income and expenses
        xeroService.getClient().accountingApi.getReportProfitAndLoss(
          tenantId,
          formattedStartDate,
          formattedEndDate
        ),

        // 4. Get outstanding invoices (money owed to us)
        xeroService.getClient().accountingApi.getInvoices(
          tenantId,
          undefined,
          'Status=="AUTHORISED"', // Only get approved invoices not yet paid
          'Date'
        ),

        // 5. Get bills (money we owe)
        xeroService.getClient().accountingApi.getInvoices(
          tenantId,
          undefined,
          'Type=="ACCPAY" AND Status=="AUTHORISED"', // Only get approved bills not yet paid
          'Date'
        )
      ];

      // Execute all API calls in parallel for better performance
      const [
        bankSummary,
        balanceSheet,
        profitAndLoss,
        receivableInvoices,
        payableInvoices
      ] = await Promise.all(apiCalls);

      // Combine the data into a cash flow forecast
      const cashFlowForecast = {
        startDate,
        endDate,
        // Current cash position
        bankSummary: bankSummary.body,
        balanceSheet: balanceSheet.body,
        // Historical patterns
        profitAndLoss: profitAndLoss.body,
        // Outstanding receivables (money coming in)
        receivableInvoices: receivableInvoices.body,
        // Upcoming payables (money going out)
        payableInvoices: payableInvoices.body
      };

      res.json(cashFlowForecast);
    } catch (error) {
      console.error('Error fetching cash flow forecast:', error);
      next(error);
    }
  };

  /**
   * Get bills from Xero
   * @param req Request
   * @param res Response
   */
  public getBills = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication first
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Get days parameter from query (defaults to 30)
      const days = parseInt(req.query.days as string) || 30;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create bill service instance
      const billService = new BillService(client);

      // Fetch bills from Xero
      const bills = await billService.getBillsForDisplay(tenantId, sinceDate);

      res.json({
        success: true,
        data: bills
      });
    } catch (error) {
      console.error('Error getting bills from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get bills from Xero'
      });
    }
  };

  /**
   * Convert a Xero bill to a custom expense
   * @param req Request
   * @param res Response
   */
  public convertBillToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { bill, expenseType } = req.body;

      if (!bill) {
        res.status(400).json({
          success: false,
          message: 'Missing bill data'
        });
        return;
      }

      if (!expenseType) {
        res.status(400).json({
          success: false,
          message: 'Missing expense type'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert bill to expense format
      // Ensure date is a Date object, not a string
      const billDate = bill.dueDate ? new Date(bill.dueDate) : new Date(bill.date);

      const expense: Omit<CustomExpense, 'id'> = {
        // Include reference in name to ensure we can match it later
        name: bill.reference
          ? `${bill.reference} - ${bill.vendor}`
          : `${bill.vendor} - ${bill.invoiceNumber}`,
        type: expenseType, // Use the provided expense type
        amount: bill.amount,
        date: billDate, // Already a Date object
        frequency: 'one-off', // Bills are one-off by default
        source: XERO_SOURCES.BILL, // Mark as coming from Xero
        description: `Bill from ${bill.vendor} ${bill.reference ? `(${bill.reference})` : ''}`,
        metadata: {
          isFromXero: true
        }
      };

      // Normalize the date - prevent timezone issues by using date only
      const normalizedDate = new Date(expense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      expense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(expense);

      // Log Xero sync activity
      try {
        await activityLogger.logXeroSyncCompleted('bills', 1);
      } catch (activityError) {
        console.error('Error logging Xero bill sync activity:', activityError);
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error converting bill to expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to convert bill to expense'
      });
    }
  };

  /**
   * Get payroll expenses from Xero
   * @param req Request
   * @param res Response
   */
  public getPayrollExpenses = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Fixed value of 90 days for payroll lookback
      const days = 90;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create payroll service instance
      const payrollService = new PayrollService(client);

      try {
        // Fetch payroll expenses from Xero - with NO fallback to sample data
        const expenses = await payrollService.getPayrollExpensesForDisplay(tenantId, sinceDate);

        res.json({
          success: true,
          data: expenses
        });
      } catch (payrollError) {
        // Handle specific payroll service errors with appropriate status codes
        console.error('Error in Xero payroll service:', payrollError);

        if (payrollError instanceof Error) {
          // Differentiate between different error types
          if (payrollError.message.includes('Missing Xero Payroll API scopes')) {
            // Missing scopes - return 403 Forbidden with clear message
            res.status(403).json({
              success: false,
              message: payrollError.message,
              code: 'MISSING_SCOPES',
              details: 'Your Xero connection is missing the required payroll scopes. Please reconnect with the appropriate permissions.'
            });
            return;
          } else if (payrollError.message.includes('No payroll data available')) {
            // No data - return 404 Not Found
            res.status(404).json({
              success: false,
              message: payrollError.message,
              code: 'NO_DATA',
              details: 'No payroll data was found in your Xero account. Please make sure you have payroll setup in Xero.'
            });
            return;
          } else if (payrollError.message.includes('Payroll API not available')) {
            // API not available - return 501 Not Implemented
            res.status(501).json({
              success: false,
              message: payrollError.message,
              code: 'API_UNAVAILABLE',
              details: 'The Xero Payroll API is not available for your account type or region.'
            });
            return;
          }
        }

        // Generic payroll service error - return 500 Internal Server Error
        res.status(500).json({
          success: false,
          message: payrollError instanceof Error ? payrollError.message : 'Unknown payroll service error',
          code: 'PAYROLL_ERROR'
        });
      }
    } catch (error) {
      console.error('Error getting payroll expenses from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get payroll expenses from Xero',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get superannuation expenses from Xero
   * @param req Request
   * @param res Response
   */
  public getSuperannuationExpenses = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Get days parameter from query (defaults to 90)
      const days = parseInt(req.query.days as string) || 90;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create superannuation service instance
      const superannuationService = new SuperannuationService(client);

      try {
        // Fetch superannuation expenses from Xero - with NO fallback to sample data
        const expenses = await superannuationService.getSuperannuationExpensesForDisplay(tenantId, sinceDate);

        res.json({
          success: true,
          data: expenses
        });
      } catch (superError) {
        // Handle specific service errors with appropriate status codes
        console.error('Error in Xero superannuation service:', superError);

        if (superError instanceof Error) {
          // Differentiate between different error types
          if (superError.message.includes('Missing Xero Payroll API scopes')) {
            res.status(403).json({
              success: false,
              message: superError.message,
              code: 'MISSING_SCOPES',
              details: 'Your Xero connection is missing the required payroll scopes. Please reconnect with the appropriate permissions.'
            });
            return;
          } else if (superError.message.includes('No superannuation data available')) {
            res.status(404).json({
              success: false,
              message: superError.message,
              code: 'NO_DATA',
              details: 'No superannuation data was found in your Xero account.'
            });
            return;
          } else if (superError.message.includes('Payroll API not initialized')) {
            res.status(501).json({
              success: false,
              message: superError.message,
              code: 'API_UNAVAILABLE',
              details: 'The Xero Payroll API is not available for your account type or region.'
            });
            return;
          }
        }

        // Generic service error
        res.status(500).json({
          success: false,
          message: superError instanceof Error ? superError.message : 'Unknown superannuation service error',
          code: 'SUPER_ERROR'
        });
      }
    } catch (error) {
      console.error('Error getting superannuation expenses from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get superannuation expenses from Xero',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get activity statements from Xero
   * @param req Request
   * @param res Response
   */
  public getActivityStatements = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Get days parameter from query (defaults to 180 for activity statements since they're less frequent)
      const days = parseInt(req.query.days as string) || 180;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create activity statement service instance
      const activityStatementService = new ActivityStatementService(client);

      try {
        // Fetch activity statements from Xero - with NO fallback to sample data
        const statements = await activityStatementService.getActivityStatementsForDisplay(tenantId, sinceDate);

        res.json({
          success: true,
          data: statements
        });
      } catch (activityError) {
        // Handle specific service errors with appropriate status codes
        console.error('Error in Xero activity statements service:', activityError);

        if (activityError instanceof Error) {
          // Differentiate between different error types
          if (activityError.message.includes('Missing accounting.reports scope')) {
            res.status(403).json({
              success: false,
              message: activityError.message,
              code: 'MISSING_SCOPES',
              details: 'Your Xero connection is missing the required accounting.reports scope. Please reconnect with the appropriate permissions.'
            });
            return;
          } else if (activityError.message.includes('No report data available')) {
            res.status(404).json({
              success: false,
              message: activityError.message,
              code: 'NO_DATA',
              details: 'No activity statement data was found in your Xero account.'
            });
            return;
          } else if (activityError.message.includes('Could not locate tax information')) {
            res.status(404).json({
              success: false,
              message: activityError.message,
              code: 'NO_TAX_DATA',
              details: 'No tax information was found in your Xero account. Please ensure you have configured tax settings in Xero.'
            });
            return;
          }
        }

        // Generic service error
        res.status(500).json({
          success: false,
          message: activityError instanceof Error ? activityError.message : 'Unknown activity statement service error',
          code: 'TAX_ERROR'
        });
      }
    } catch (error) {
      console.error('Error getting activity statements from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get activity statements from Xero',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get payroll expense breakdown from Xero
   * @param req Request
   * @param res Response
   */
  public getPayrollExpenseBreakdown = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Fixed value of 90 days for lookback
      const days = 90;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create payroll service instance
      const payrollService = new PayrollService(client);

      try {
        // Fetch payroll expense breakdown
        const expenseBreakdown = await payrollService.getPayrollExpenseBreakdown(tenantId, sinceDate);

        res.json({
          success: true,
          data: expenseBreakdown
        });
      } catch (payrollError) {
        // Handle specific payroll service errors with appropriate status codes
        console.error('Error in Xero payroll expense breakdown service:', payrollError);

        if (payrollError instanceof Error) {
          // Differentiate between different error types
          if (payrollError.message.includes('Missing Xero Payroll API scopes')) {
            // Missing scopes - return 403 Forbidden with clear message
            res.status(403).json({
              success: false,
              message: payrollError.message,
              code: 'MISSING_SCOPES',
              details: 'Your Xero connection is missing the required payroll scopes. Please reconnect with the appropriate permissions.'
            });
            return;
          } else if (payrollError.message.includes('No payroll data available')) {
            // No data - return 404 Not Found
            res.status(404).json({
              success: false,
              message: payrollError.message,
              code: 'NO_DATA',
              details: 'No payroll data was found in your Xero account. Please make sure you have payroll setup in Xero.'
            });
            return;
          } else if (payrollError.message.includes('Payroll API not available')) {
            // API not available - return 501 Not Implemented
            res.status(501).json({
              success: false,
              message: payrollError.message,
              code: 'API_UNAVAILABLE',
              details: 'The Xero Payroll API is not available for your account type or region.'
            });
            return;
          }
        }

        // Generic payroll service error - return 500 Internal Server Error
        res.status(500).json({
          success: false,
          message: payrollError instanceof Error ? payrollError.message : 'Unknown payroll service error',
          code: 'PAYROLL_ERROR'
        });
      }
    } catch (error) {
      console.error('Error getting payroll expense breakdown from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get payroll expense breakdown from Xero',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Convert a payroll expense to a custom expense
   * @param req Request
   * @param res Response
   */
  public convertPayrollToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { expense } = req.body;

      if (!expense) {
        res.status(400).json({
          success: false,
          message: 'Missing expense data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert to expense format with appropriate type and name
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: expense.description || `Payroll: ${new Date(expense.paymentDate).toLocaleDateString()}`,
        type: 'Monthly Payroll', // Use predefined type for payroll
        amount: expense.amount,
        date: new Date(expense.paymentDate), // Use payment date
        frequency: expense.frequency as CustomExpense['frequency'], // Map to expected frequency
        source: XERO_SOURCES.PAYROLL, // Mark as coming from Xero
        description: `Payroll expense from Xero for period ending ${new Date(expense.periodEndDate).toLocaleDateString()}`,
        metadata: {
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(customExpense);

      // Log Xero sync activity
      try {
        console.log('Attempting to log Xero payroll sync activity...');
        const activity = await activityLogger.logXeroSyncCompleted('payroll', 1);
        console.log('Successfully logged Xero payroll sync activity:', activity);
      } catch (activityError) {
        console.error('Error logging Xero payroll sync activity:', activityError);
        console.error('Activity error details:', {
          message: activityError?.message,
          stack: activityError?.stack,
          name: activityError?.name
        });
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error converting payroll to expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to convert payroll to expense'
      });
    }
  };

  /**
   * Sync an expense type from the expense breakdown
   * @param req Request
   * @param res Response
   */
  public syncExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { expense } = req.body;

      if (!expense) {
        res.status(400).json({
          success: false,
          message: 'Missing expense data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert to expense format with appropriate type and name
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: expense.name,
        type: expense.type,
        amount: expense.amount,
        date: new Date(expense.date),
        frequency: expense.frequency || 'monthly',
        source: expense.source || 'xero',
        metadata: {
          ...expense.metadata,
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(customExpense);

      // Log Xero sync activity
      try {
        console.log('Attempting to log Xero expense sync activity...');
        await activityLogger.logXeroSyncCompleted('expenses', 1);
        console.log('Successfully logged Xero expense sync activity');
      } catch (activityError) {
        console.error('Error logging Xero expense sync activity:', activityError);
        console.error('Activity error stack:', activityError.stack);
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error syncing expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to sync expense'
      });
    }
  };

  /**
   * Convert a superannuation expense to a custom expense
   * @param req Request
   * @param res Response
   */
  public convertSuperannuationToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { expense } = req.body;

      if (!expense) {
        res.status(400).json({
          success: false,
          message: 'Missing expense data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert to expense format with appropriate type
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: `Superannuation: ${expense.provider || 'Payment'}`,
        type: 'Superannuation', // Use predefined type for superannuation
        amount: expense.amount,
        date: new Date(expense.paymentDate),
        frequency: expense.frequency as CustomExpense['frequency'],
        source: XERO_SOURCES.SUPERANNUATION, // Mark as coming from Xero
        description: `Superannuation payment from Xero for ${expense.provider || 'employees'}`,
        metadata: {
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(customExpense);

      // Log Xero sync activity
      try {
        console.log('Attempting to log Xero superannuation sync activity...');
        const activity = await activityLogger.logXeroSyncCompleted('superannuation', 1);
        console.log('Successfully logged Xero superannuation sync activity:', activity);
      } catch (activityError) {
        console.error('Error logging Xero superannuation sync activity:', activityError);
        console.error('Activity error details:', {
          message: activityError?.message,
          stack: activityError?.stack,
          name: activityError?.name
        });
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error converting superannuation to expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to convert superannuation to expense'
      });
    }
  };

  /**
   * Convert an activity statement to a custom expense
   * @param req Request
   * @param res Response
   */
  public convertActivityStatementToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { statement } = req.body;

      if (!statement) {
        res.status(400).json({
          success: false,
          message: 'Missing statement data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert to expense format with appropriate tax type
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: `${statement.type}: ${statement.taxPeriod}`,
        type: 'Taxes', // Use predefined type for tax payments
        amount: statement.amount,
        date: new Date(statement.dueDate),
        frequency: statement.frequency as CustomExpense['frequency'],
        source: XERO_SOURCES.TAX_STATEMENT, // Mark as coming from Xero
        description: `${statement.type} tax payment from Xero for period ${statement.taxPeriod}`,
        metadata: {
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(customExpense);

      // Log Xero sync activity
      try {
        console.log('Attempting to log Xero tax statement sync activity...');
        await activityLogger.logXeroSyncCompleted('tax-statements', 1);
        console.log('Successfully logged Xero tax statement sync activity');
      } catch (activityError) {
        console.error('Error logging Xero tax statement sync activity:', activityError);
        console.error('Activity error stack:', activityError.stack);
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error converting activity statement to expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to convert activity statement to expense'
      });
    }
  };

  /**
   * Get GST data from Xero Profit and Loss Report
   * @param req Request
   * @param res Response
   */
  public getGSTData = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();
      const harvestService = getHarvestService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Get the current quarter date range
      const { fromDate, toDate, daysElapsed, daysInQuarter } = this.getCurrentQuarterDateRange();
      console.log(`Getting Profit and Loss report for period: ${fromDate} to ${toDate}`);

      // Get the Profit and Loss report for the current quarter
      console.log('Requesting Profit and Loss report from Xero');
      const profitAndLossResponse = await client.accountingApi.getReportProfitAndLoss(
        tenantId,
        fromDate,
        toDate,
        undefined, // periods
        undefined, // timeframe
        undefined, // trackingCategoryID
        undefined, // trackingCategoryID2
        undefined, // trackingOptionID
        undefined, // trackingOptionID2
        undefined, // standardLayout
        undefined  // paymentsOnly
      );
      console.log('Received Profit and Loss report from Xero');

      // For debugging, log a sample of the response
      console.log('Profit and Loss response received');
      try {
        // Log the structure as a stringified object to see all properties
        console.log('Profit and Loss response structure:', JSON.stringify(profitAndLossResponse.body, null, 2).substring(0, 500) + '...');
      } catch (logError) {
        console.error('Error logging profit and loss structure:', logError);
      }

      // Extract total sales from the Profit and Loss report and calculate GST
      // THIS IS THE CRITICAL PART - we keep the original accrued GST calculation unchanged
      const gstData = this.calculateGSTFromProfitAndLoss(profitAndLossResponse.body);

      // Calculate the next payment due date
      const nextPaymentDate = this.calculateGSTDueDate();

      // Get projected revenue for the remainder of the quarter
      const today = new Date();
      const endOfQuarter = new Date(toDate);

      // Get project settings for revenue projections
      const projectSettings = await this.getProjectSettings();

      // Get projected invoices for the remainder of the quarter
      // The generateProjectedIncome function already filters out real invoices
      // It only returns projected invoices that don't have corresponding real invoices in Harvest
      // This ensures we're only looking at truly future invoices not already counted in Xero
      const projectedInvoices = await harvestService.projectBudgets.generateProjectedIncome(
        today,
        endOfQuarter,
        projectSettings
      );

      // Calculate projected revenue (sum of all positive amounts)
      const projectedRevenue = projectedInvoices
        .filter(invoice => invoice.amount > 0)
        .reduce((sum, invoice) => sum + invoice.amount, 0);

      // Calculate projected GST (10% of projected revenue from future invoices)
      // Since generateProjectedIncome already filters out real invoices,
      // we can be confident this only includes invoices not yet in Xero
      const projectedGST = projectedRevenue * 0.1;

      // Total projected GST is current accrued GST plus projected GST
      // The accrued amount from Xero (for existing invoices) + GST from projected future invoices
      const totalProjectedGST = gstData.amount + projectedGST;

      res.json({
        success: true,
        data: {
          ...gstData,  // Original accrued amount is preserved here
          dueDate: nextPaymentDate,
          projectedGST: projectedGST,
          totalProjectedGST: totalProjectedGST,
          // Include additional data for frontend display
          daysElapsed,
          daysInQuarter,
          projectedRevenue,
          projectedInvoiceCount: projectedInvoices.filter(invoice => invoice.amount > 0).length
        }
      });
    } catch (error) {
      console.error('Error getting GST data from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get GST data from Xero',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get the date range for the current quarter
   * @returns Object with fromDate, toDate, daysElapsed, and daysInQuarter
   */
  private getCurrentQuarterDateRange = (): {
    fromDate: string,
    toDate: string,
    daysElapsed: number,
    daysInQuarter: number
  } => {
    const today = new Date();
    const currentMonth = today.getMonth(); // 0-indexed (0 = January)
    const currentYear = today.getFullYear();

    let fromMonth: number;
    let toMonth: number;
    let fromYear: number = currentYear;
    let toYear: number = currentYear;

    // Determine the quarter date range based on the current month
    if (currentMonth >= 0 && currentMonth <= 2) {
      // Q3 of financial year (Jan-Mar)
      fromMonth = 0; // January
      toMonth = 2; // March
    } else if (currentMonth >= 3 && currentMonth <= 5) {
      // Q4 of financial year (Apr-Jun)
      fromMonth = 3; // April
      toMonth = 5; // June
    } else if (currentMonth >= 6 && currentMonth <= 8) {
      // Q1 of financial year (Jul-Sep)
      fromMonth = 6; // July
      toMonth = 8; // September
    } else {
      // Q2 of financial year (Oct-Dec)
      fromMonth = 9; // October
      toMonth = 11; // December
    }

    // Create date objects for the first and last day of the quarter
    const fromDate = new Date(fromYear, fromMonth, 1);
    const toDate = new Date(toYear, toMonth + 1, 0); // Last day of the month

    // Calculate days elapsed and total days in quarter
    const daysElapsed = Math.floor((today.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysInQuarter = Math.floor((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // Format dates as YYYY-MM-DD for Xero API
    const formatDate = (date: Date): string => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    return {
      fromDate: formatDate(fromDate),
      toDate: formatDate(toDate),
      daysElapsed,
      daysInQuarter
    };
  };

  /**
   * Calculate GST from Profit and Loss report
   * @param profitAndLoss Profit and Loss report from Xero
   * @returns Object with GST amount and account ID
   */
  private calculateGSTFromProfitAndLoss = (profitAndLoss: any): { amount: number, accountId: string } => {
    let totalSales = 0;
    let accountId = '';

    try {
      console.log('Calculating GST from Profit and Loss report');

      // Find the Reports array
      const reports = profitAndLoss.Reports || profitAndLoss.reports || [];

      if (!reports.length) {
        console.log('No reports found in Profit and Loss response');
        return { amount: 0, accountId: '' };
      }

      // Get the first report
      const report = reports[0];

      // Find the rows array
      const rows = report.Rows || report.rows || [];

      if (!rows.length) {
        console.log('No rows found in Profit and Loss report');
        return { amount: 0, accountId: '' };
      }

      // Find the Income section
      const incomeSection = rows.find((row: any) => {
        const rowType = row.RowType || row.rowType;
        const title = row.Title || row.title;
        return rowType === 'Section' && title === 'Income';
      });

      if (!incomeSection) {
        console.log('Income section not found in Profit and Loss report');
        return { amount: 0, accountId: '' };
      }

      // Get the rows in the Income section
      const incomeRows = incomeSection.Rows || incomeSection.rows || [];

      // Find the Total Income row
      const totalIncomeRow = incomeRows.find((row: any) => {
        const rowType = row.RowType || row.rowType;
        if (rowType !== 'SummaryRow') return false;

        const cells = row.Cells || row.cells || [];
        if (!cells.length) return false;

        const value = cells[0].Value || cells[0].value || '';
        return value === 'Total Income';
      });

      if (totalIncomeRow) {
        // Get the total income value
        const cells = totalIncomeRow.Cells || totalIncomeRow.cells || [];
        if (cells.length >= 2) {
          const valueCell = cells[1];
          const valueStr = valueCell.Value || valueCell.value || '0';
          totalSales = parseFloat(valueStr) || 0;
          console.log('Extracted total sales:', totalSales);

          // Try to get account ID if available
          const attributes = valueCell.Attributes || valueCell.attributes || [];
          const accountAttr = attributes.find((attr: any) => {
            return (attr.Id === 'account' || attr.id === 'account');
          });

          if (accountAttr) {
            accountId = accountAttr.Value || accountAttr.value || '';
          }
        }
      } else {
        console.log('Total Income row not found in Income section');

        // If we can't find the summary row, try to sum up all the individual income rows
        let sum = 0;
        incomeRows.forEach((row: any) => {
          const rowType = row.RowType || row.rowType;
          if (rowType === 'Row') {
            const cells = row.Cells || row.cells || [];
            if (cells.length >= 2) {
              const valueStr = cells[1].Value || cells[1].value || '0';
              const value = parseFloat(valueStr) || 0;
              sum += value;
            }
          }
        });

        if (sum > 0) {
          totalSales = sum;
          console.log('Calculated total sales by summing rows:', totalSales);
        }
      }

      // Calculate GST as 10% of total sales
      const gstAmount = totalSales * 0.1;
      console.log('Calculated GST amount (10% of total sales):', gstAmount);

      return { amount: gstAmount, accountId };
    } catch (error) {
      console.error('Error calculating GST from Profit and Loss report:', error);
      return { amount: 0, accountId: '' };
    }
  };

  /**
   * Get project settings for revenue projections
   * @returns Project settings
   * @private
   */
  private getProjectSettings = async (): Promise<any[]> => {
    try {
      // Read project settings from disk
      const fs = require('fs');
      const path = require('path');

      // Determine the data directory based on environment
      let dataDir = '';
      if (process.env.NODE_ENV === 'production') {
        dataDir = '/data';
      } else {
        dataDir = path.join(__dirname, '../../../data');
      }

      // Path to settings file
      const settingsPath = path.join(dataDir, 'project_settings.json');

      // Read settings file
      if (fs.existsSync(settingsPath)) {
        const settingsData = fs.readFileSync(settingsPath, 'utf8');
        const settings = JSON.parse(settingsData);
        console.log(`Loaded ${settings.length} project settings for projection calculations`);
        return settings;
      } else {
        console.log('No project settings file found - using empty settings array');
        return [];
      }
    } catch (error) {
      console.error('Error getting project settings:', error);
      return []; // Return empty array on error
    }
  };

  /**
   * Calculate the next GST due date based on current quarter
   * For businesses with bookkeepers, BAS is due on the 26th of the month following the month after quarter end
   */
  private calculateGSTDueDate = (): Date => {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    let dueMonth: number;
    let dueYear: number = currentYear;

    // Determine the next due date based on the current month (0-indexed)
    if (currentMonth >= 0 && currentMonth <= 2) {
      // Q3 of financial year (Jan-Mar) → due in May (4) - month after April
      dueMonth = 4; // May
    } else if (currentMonth >= 3 && currentMonth <= 5) {
      // Q4 of financial year (Apr-Jun) → due in August (7) - month after July
      dueMonth = 7; // August
    } else if (currentMonth >= 6 && currentMonth <= 8) {
      // Q1 of financial year (Jul-Sep) → due in November (10) - month after October
      dueMonth = 10; // November
    } else {
      // Q2 of financial year (Oct-Dec) → due in February (1) of next year - month after January
      dueMonth = 1; // February
      dueYear = currentYear + 1;
    }

    // Create the due date (26th of the month)
    const dueDate = new Date(dueYear, dueMonth, 26);

    return dueDate;
  };

  /**
   * Convert GST data to a custom expense
   * @param req Request
   * @param res Response
   */
  public convertGSTToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { gstData } = req.body;

      if (!gstData) {
        res.status(400).json({
          success: false,
          message: 'Missing GST data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Format the due date for display in Australian format (DD/MM/YYYY)
      const dueDate = new Date(gstData.dueDate);
      const dueDateStr = `${dueDate.getDate()}/${dueDate.getMonth() + 1}/${dueDate.getFullYear()}`;

      // Check if this is using a predicted amount
      const isUsingPredictedAmount = gstData.metadata?.usedProjectedAmount === true;

      // Create a description that includes whether this is a predicted amount
      const amountTypeDesc = isUsingPredictedAmount
        ? 'predicted amount (accrued GST plus GST from projected future invoices)'
        : 'accrued amount (current GST liability)';

      // Convert to expense format with appropriate tax type
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: `GST Payment (BAS) - Due ${dueDateStr}`,
        type: 'Taxes', // Use predefined type for tax payments
        amount: gstData.amount,
        date: new Date(gstData.dueDate),
        frequency: 'one-off', // One-off payment for this quarter
        source: XERO_SOURCES.GST,
        description: `GST payment due on ${dueDateStr} (10% of sales from Xero Profit and Loss) - Using ${amountTypeDesc}`,
        metadata: {
          usedProjectedAmount: isUsingPredictedAmount,
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Check if a similar GST expense already exists to prevent duplicates
      let existingExpenseId = null;
      try {
        // Get all expenses
        const allExpenses = expensesRepository.getAll();

        // Find an expense that matches our criteria for GST
        const existingExpense = allExpenses.find(exp => {
          // Check if it's a GST expense from Xero
          const isGSTExpense = exp.type === 'Taxes' &&
                              // First check if it's from Xero using source or metadata
                              ((exp.source && exp.source.includes('xero-gst')) ||
                               (exp.metadata?.isFromXero === true &&
                                ((exp.name.toLowerCase().includes('gst') && exp.name.toLowerCase().includes('payment')) ||
                                 (exp.name.toLowerCase().includes('bas') && exp.name.toLowerCase().includes('payment')))));

          // Check if it's for the same due date (within 1 day)
          const isSameDueDate = Math.abs(
            exp.date.getTime() - normalizedDate.getTime()
          ) < 24 * 60 * 60 * 1000; // 1 day in milliseconds

          return isGSTExpense && isSameDueDate;
        });

        if (existingExpense) {
          existingExpenseId = existingExpense.id;
          console.log(`Found existing GST expense with ID: ${existingExpenseId}`);
        }
      } catch (error) {
        console.error('Error checking for existing GST expenses:', error);
        // Continue with creating a new expense if we can't check for existing ones
      }

      let result: CustomExpense | null;

      // Update existing expense or create a new one
      if (existingExpenseId) {
        console.log(`Updating existing GST expense with ID: ${existingExpenseId}`);
        result = expensesRepository.update(existingExpenseId, customExpense);
        console.log('Successfully updated GST expense');

        // If update fails, create a new expense as fallback
        if (!result) {
          console.log('Update failed, creating new GST expense instead');
          result = expensesRepository.create(customExpense);
        }
      } else {
        console.log('Creating new GST expense');
        result = expensesRepository.create(customExpense);
        console.log('Successfully created GST expense');
      }

      // Ensure we have a valid result
      if (!result) {
        throw new Error('Failed to create or update GST expense');
      }

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Error converting GST to expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to convert GST to expense'
      });
    }
  };

  /**
   * Get balance sheet report from Xero
   * @param req Request
   * @param res Response
   */
  public getBalanceSheet = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication first
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Get date parameter from query (defaults to today)
      const date = req.query.date ? new Date(req.query.date as string) : new Date();

      // Format date for API (YYYY-MM-DD format as required by Xero)
      // Xero API requires date in YYYY-MM-DD format
      const formattedDate = date.toISOString().split('T')[0];
      console.log(`Requesting balance sheet for date: ${formattedDate}`);

      // Optional parameters
      const periods = req.query.periods ? parseInt(req.query.periods as string) : undefined;

      // Validate timeframe parameter
      let timeframe: "MONTH" | "QUARTER" | "YEAR" | undefined = undefined;
      const timeframeParam = req.query.timeframe as string;
      if (timeframeParam) {
        if (["MONTH", "QUARTER", "YEAR"].includes(timeframeParam)) {
          timeframe = timeframeParam as "MONTH" | "QUARTER" | "YEAR";
        } else {
          console.warn(`Invalid timeframe parameter: ${timeframeParam}. Must be one of: MONTH, QUARTER, YEAR. Using default.`);
        }
      }

      const standardLayout = req.query.standardLayout === 'true';

      console.log(`Balance sheet request parameters:
        - date: ${formattedDate}
        - periods: ${periods}
        - timeframe: ${timeframe}
        - standardLayout: ${standardLayout}
      `);

      // Log the parameters being sent to Xero API
      console.log(`Calling Xero API with parameters:
        - tenantId: ${tenantId}
        - date: ${formattedDate}
        - periods: ${periods}
        - timeframe: ${timeframe}
        - standardLayout: ${standardLayout}
      `);

      // Get the balance sheet report using our custom client method
      // This fixes the API method mismatch issue by using our wrapper method
      // that handles rate limiting and error detection for missing methods
      const balanceSheetResponse = await client.getBalanceSheetReport(
        tenantId,
        date,
        periods,
        timeframe,
        undefined, // trackingOptionID1
        undefined, // trackingOptionID2
        standardLayout,
        false // paymentsOnly
      );

      console.log('Successfully retrieved balance sheet from Xero');

      res.json({
        success: true,
        data: balanceSheetResponse.body,
        date: formattedDate
      });
    } catch (error) {
      console.error('Error getting balance sheet from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      // Handle API compatibility issues
      if (error instanceof Error &&
          (error.message.includes('not a function') ||
           error.message.includes('compatibility issue'))) {
        res.status(500).json({
          success: false,
          message: 'Xero API compatibility issue',
          error: error.message,
          code: 'API_COMPATIBILITY_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get balance sheet from Xero',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  };

  /**
   * Get late bills from Xero
   * @param req Request
   * @param res Response
   */
  public getLateBills = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication first
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const tenantId = xeroService.getActiveTenantId();

      if (!tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero tenant ID'
        });
        return;
      }

      // Fetch bills from Xero using the existing bill service
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get all bills
      const allBills = await xeroService.bills.getBillsForDisplay(tenantId);

      // Filter to only get bills that are due before today (late bills)
      const lateBills = allBills.filter(bill => {
        const dueDate = new Date(bill.dueDate);
        return dueDate < today;
      });

      // Calculate days past due for each bill
      const lateBillsWithDaysPastDue = lateBills.map(bill => {
        const dueDate = new Date(bill.dueDate);
        const diffTime = Math.abs(today.getTime() - dueDate.getTime());
        const daysPastDue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return {
          ...bill,
          daysPastDue
        };
      });

      // Calculate total amount
      const totalAmount = lateBillsWithDaysPastDue.reduce((sum, bill) => sum + bill.amount, 0);

      res.json({
        success: true,
        data: {
          bills: lateBillsWithDaysPastDue,
          totalAmount,
          count: lateBillsWithDaysPastDue.length
        }
      });
    } catch (error) {
      console.error('Error getting late bills from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get late bills from Xero',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  };

  /**
   * Get account information including user and organization details
   */
  public getAccountInfo = async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.session?.tokenSet) {
        res.status(401).json({ error: 'Not authenticated' });
        return;
      }

      const xeroService = getXeroService();
      const tenantId = xeroService.getActiveTenantId();

      // Ensure we have an authenticated session
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({ error: 'Not authenticated' });
        return;
      }

      // Get user info from session
      const userInfo = req.session.userInfo || {};

      // Get organization info
      let organizationInfo = {};
      try {
        const orgResponse = await xeroService.getClient().accountingApi.getOrganisations(tenantId);
        organizationInfo = orgResponse.body.organisations?.[0] || {};
      } catch (error) {
        console.error('Error fetching organization info:', error);
        // Continue with partial data if organization info fails
      }

      res.json({
        success: true,
        user: {
          name: userInfo.name || 'User',
          email: userInfo.email || 'No email provided',
          id: userInfo.sub
        },
        organization: {
          name: (organizationInfo as any).name || 'Your Organization',
          id: tenantId
        }
      });
    } catch (error) {
      console.error('Error getting account info:', error);
      res.status(500).json({
        error: 'Failed to get account information',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
}
