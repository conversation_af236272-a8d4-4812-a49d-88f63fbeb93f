/**
 * API routes for company relationships
 */

import express from 'express';
import { createCompanyRelationshipRepository, CompanyRelationshipType } from '../repositories/company-relationship-repository';
import { createContactCompanyRepository } from '../repositories/contact-company-repository';
// Using any type for Database to avoid TypeScript namespace issues

export function setupCompanyRelationshipRoutes(app: express.Express, db: any) {
  const router = express.Router();
  const companyRelationshipRepo = createCompanyRelationshipRepository(db);
  const contactCompanyRepo = createContactCompanyRepository(db);

  /**
   * Create a new company relationship
   * POST /api/company-relationships
   */
  router.post('/', (req, res) => {
    try {
      const { parentCompanyId, childCompanyId, relationshipType } = req.body;
      
      if (!parentCompanyId || !childCompanyId || !relationshipType) {
        return res.status(400).json({
          success: false,
          error: 'Missing required fields: parentCompanyId, childCompanyId, relationshipType'
        });
      }

      // Validate relationship type
      if (!Object.values(CompanyRelationshipType).includes(relationshipType)) {
        return res.status(400).json({
          success: false,
          error: `Invalid relationship type. Must be one of: ${Object.values(CompanyRelationshipType).join(', ')}`
        });
      }

      const createdBy = req.session?.user?.id || 'system';
      const relationship = companyRelationshipRepo.createRelationship(
        parentCompanyId,
        childCompanyId,
        relationshipType as CompanyRelationshipType,
        createdBy
      );

      res.json({
        success: true,
        data: relationship
      });
    } catch (error) {
      console.error('Error creating company relationship:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Delete a company relationship
   * DELETE /api/company-relationships/:parentId/:childId
   */
  router.delete('/:parentId/:childId', (req, res) => {
    try {
      const { parentId, childId } = req.params;
      
      const success = companyRelationshipRepo.deleteRelationship(parentId, childId);
      
      if (!success) {
        return res.status(404).json({
          success: false,
          error: 'Relationship not found'
        });
      }

      res.json({
        success: true,
        message: 'Relationship deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting company relationship:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Get all relationships for a company
   * GET /api/company-relationships/:companyId
   */
  router.get('/:companyId', (req, res) => {
    try {
      const { companyId } = req.params;
      
      const relationships = companyRelationshipRepo.getCompanyRelationships(companyId);
      
      res.json({
        success: true,
        data: relationships
      });
    } catch (error) {
      console.error('Error getting company relationships:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Get a company's family tree
   * GET /api/company-relationships/:companyId/tree
   */
  router.get('/:companyId/tree', (req, res) => {
    try {
      const { companyId } = req.params;
      const depthParam = req.query.depth as string;
      const depth = depthParam ? parseInt(depthParam, 10) : 3;
      
      const familyTree = companyRelationshipRepo.getCompanyFamilyTree(companyId, depth);
      
      res.json({
        success: true,
        data: familyTree
      });
    } catch (error) {
      console.error('Error getting company family tree:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Update a company relationship type
   * PATCH /api/company-relationships/:parentId/:childId
   */
  router.patch('/:parentId/:childId', (req, res) => {
    try {
      const { parentId, childId } = req.params;
      const { relationshipType } = req.body;
      
      if (!relationshipType) {
        return res.status(400).json({
          success: false,
          error: 'Missing required field: relationshipType'
        });
      }

      // Validate relationship type
      if (!Object.values(CompanyRelationshipType).includes(relationshipType)) {
        return res.status(400).json({
          success: false,
          error: `Invalid relationship type. Must be one of: ${Object.values(CompanyRelationshipType).join(', ')}`
        });
      }

      const success = companyRelationshipRepo.updateRelationshipType(
        parentId,
        childId,
        relationshipType as CompanyRelationshipType
      );
      
      if (!success) {
        return res.status(404).json({
          success: false,
          error: 'Relationship not found'
        });
      }

      res.json({
        success: true,
        message: 'Relationship updated successfully'
      });
    } catch (error) {
      console.error('Error updating company relationship:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Get all contacts for a company
   * GET /api/company-relationships/:companyId/contacts
   */
  router.get('/:companyId/contacts', (req, res) => {
    try {
      const { companyId } = req.params;
      
      const contacts = contactCompanyRepo.getCompanyContacts(companyId);
      
      res.json({
        success: true,
        data: contacts
      });
    } catch (error) {
      console.error('Error getting company contacts:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Get primary contacts for a company
   * GET /api/company-relationships/:companyId/primary-contacts
   */
  router.get('/:companyId/primary-contacts', (req, res) => {
    try {
      const { companyId } = req.params;
      
      const contacts = contactCompanyRepo.getCompanyPrimaryContacts(companyId);
      
      res.json({
        success: true,
        data: contacts
      });
    } catch (error) {
      console.error('Error getting company primary contacts:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  app.use('/api/company-relationships', router);
}