import express from 'express';
import { hubspotService } from '../services/hubspot-service';
import { DealRepository } from '../repositories/deal-repository';
import { ContactRepository } from '../repositories/contact-repository';
import { CompanyRepository } from '../repositories/company-repository';
import { NoteRepository } from '../repositories/note-repository';

const router = express.Router();
const dealRepository = new DealRepository();
const contactRepository = new ContactRepository();
const companyRepository = new CompanyRepository();
const noteRepository = new NoteRepository();

/**
 * @route GET /api/hubspot/status
 * @desc Check if HubSpot integration is configured
 * @access Private
 */
router.get('/status', (req, res) => {
  try {
    const accessToken = hubspotService.getAccessToken();
    const isConfigured = !!accessToken;

    res.json({
      success: true,
      data: {
        isConfigured
      }
    });
  } catch (error: any) {
    console.error('Error checking HubSpot status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check HubSpot status',
      message: error.message
    });
  }
});

/**
 * @route POST /api/hubspot/configure
 * @desc Configure HubSpot integration with access token
 * @access Private
 */
router.post('/configure', (req, res) => {
  try {
    const { accessToken } = req.body;

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        requiredFields: ['accessToken']
      });
    }

    // Save the access token
    const success = hubspotService.saveAccessToken(accessToken);

    if (!success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to save access token'
      });
    }

    // Initialize the HubSpot client
    const initSuccess = hubspotService.initialize(accessToken);

    if (!initSuccess) {
      return res.status(500).json({
        success: false,
        error: 'Failed to initialize HubSpot client'
      });
    }

    res.json({
      success: true,
      message: 'HubSpot integration configured successfully'
    });
  } catch (error: any) {
    console.error('Error configuring HubSpot:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to configure HubSpot',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/hubspot/configure
 * @desc Remove HubSpot integration configuration
 * @access Private
 */
router.delete('/configure', (req, res) => {
  try {
    const success = hubspotService.deleteAccessToken();

    if (!success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to delete access token'
      });
    }

    res.json({
      success: true,
      message: 'HubSpot integration removed successfully'
    });
  } catch (error: any) {
    console.error('Error removing HubSpot configuration:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to remove HubSpot configuration',
      message: error.message
    });
  }
});

/**
 * @route POST /api/hubspot/import/deals
 * @desc Import deals from HubSpot
 * @access Private
 */
router.post('/import/deals', async (req, res) => {
  try {
    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'HubSpot integration not configured'
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        return res.status(500).json({
          success: false,
          error: 'Failed to initialize HubSpot client'
        });
      }
    }

    // Import deals
    const result = await hubspotService.importDeals();

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to import deals from HubSpot',
        message: result.error
      });
    }

    res.json({
      success: true,
      data: {
        count: result.count
      },
      message: `Successfully imported ${result.count} deals from HubSpot`
    });
  } catch (error: any) {
    console.error('Error importing deals from HubSpot:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to import deals from HubSpot',
      message: error.message
    });
  }
});

/**
 * @route POST /api/hubspot/import/contacts
 * @desc Import contacts from HubSpot
 * @access Private
 */
router.post('/import/contacts', async (req, res) => {
  try {
    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'HubSpot integration not configured'
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        return res.status(500).json({
          success: false,
          error: 'Failed to initialize HubSpot client'
        });
      }
    }

    // Import contacts
    const result = await hubspotService.importContacts();

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to import contacts from HubSpot',
        message: result.error
      });
    }

    res.json({
      success: true,
      data: {
        count: result.count
      },
      message: `Successfully imported ${result.count} contacts from HubSpot`
    });
  } catch (error: any) {
    console.error('Error importing contacts from HubSpot:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to import contacts from HubSpot',
      message: error.message
    });
  }
});

/**
 * @route POST /api/hubspot/import/companies
 * @desc Import companies from HubSpot
 * @access Private
 */
router.post('/import/companies', async (req, res) => {
  try {
    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'HubSpot integration not configured'
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        return res.status(500).json({
          success: false,
          error: 'Failed to initialize HubSpot client'
        });
      }
    }

    // Import companies
    const result = await hubspotService.importCompanies();

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to import companies from HubSpot',
        message: result.error
      });
    }

    res.json({
      success: true,
      data: {
        count: result.count
      },
      message: `Successfully imported ${result.count} companies from HubSpot`
    });
  } catch (error: any) {
    console.error('Error importing companies from HubSpot:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to import companies from HubSpot',
      message: error.message
    });
  }
});

/**
 * @route GET /api/hubspot/companies
 * @desc Get all companies from HubSpot for linking purposes
 * @access Private
 */
router.get('/companies', async (req, res) => {
  try {
    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'HubSpot integration not configured'
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        return res.status(500).json({
          success: false,
          error: 'Failed to initialize HubSpot client'
        });
      }
    }

    // Get companies from HubSpot
    const companies = await hubspotService.getCompaniesForLinking();

    res.json({
      success: true,
      data: companies
    });
  } catch (error: any) {
    console.error('Error getting HubSpot companies:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get HubSpot companies',
      message: error.message
    });
  }
});

/**
 * @route POST /api/hubspot/import/all
 * @desc Import all data from HubSpot (companies first, then deals, then contacts)
 * @access Private
 */
router.post('/import/all', async (req, res) => {
  try {
    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'HubSpot integration not configured'
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        return res.status(500).json({
          success: false,
          error: 'Failed to initialize HubSpot client'
        });
      }
    }

    // Use the new progress tracking method
    const importResult = await hubspotService.importAllWithProgress();

    // Determine overall success
    const overallSuccess = importResult.results.companies.success &&
                          importResult.results.deals.success &&
                          importResult.results.contacts.success;

    res.json({
      success: overallSuccess,
      data: {
        totalCount: importResult.totalCount,
        results: importResult.results
      },
      message: overallSuccess
        ? `Successfully imported all data: ${importResult.results.companies.count} companies, ${importResult.results.deals.count} deals, ${importResult.results.contacts.count} contacts`
        : 'Import completed with some errors. Check individual results for details.'
    });
  } catch (error: any) {
    console.error('Error importing all data from HubSpot:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to import all data from HubSpot',
      message: error.message
    });
  }
});

/**
 * @route GET /api/hubspot/import/history
 * @desc Get import history
 * @access Private
 */
router.get('/import/history', (req, res) => {
  try {
    const imports = hubspotService.getImportHistory();

    res.json({
      success: true,
      data: imports
    });
  } catch (error: any) {
    console.error('Error getting import history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get import history',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/hubspot/reset-crm-data
 * @desc Delete all CRM data (deals, contacts, companies, notes, and associations)
 * @access Private
 */
router.delete('/reset-crm-data', (req, res) => {
  try {
    // Start a transaction
    dealRepository['db'].prepare('BEGIN TRANSACTION').run();

    // Delete all associations first (foreign key constraints)
    dealRepository['db'].prepare('DELETE FROM contact_role').run();

    // Delete all deal-estimate relationships if the table exists
    const dealEstimatesTableExists = dealRepository['db'].prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='deal_estimate'").get();
    if (dealEstimatesTableExists) {
      dealRepository['db'].prepare('DELETE FROM deal_estimate').run();
    }

    // Delete all notes
    const notesDeleted = dealRepository['db'].prepare('DELETE FROM note').run().changes;

    // Delete all deals
    const dealsDeleted = dealRepository['db'].prepare('DELETE FROM deal').run().changes;

    // Delete all contacts
    const contactsDeleted = dealRepository['db'].prepare('DELETE FROM contact').run().changes;

    // Delete all companies
    const companiesDeleted = dealRepository['db'].prepare('DELETE FROM company').run().changes;

    // Commit the transaction
    dealRepository['db'].prepare('COMMIT').run();

    console.log(`Successfully deleted all CRM data: ${dealsDeleted} deals, ${contactsDeleted} contacts, ${companiesDeleted} companies, ${notesDeleted} notes`);

    res.json({
      success: true,
      data: {
        dealsDeleted,
        contactsDeleted,
        companiesDeleted,
        notesDeleted
      },
      message: `Successfully deleted all CRM data: ${dealsDeleted} deals, ${contactsDeleted} contacts, ${companiesDeleted} companies, ${notesDeleted} notes`
    });
  } catch (error: any) {
    // Rollback the transaction on error
    try {
      dealRepository['db'].prepare('ROLLBACK').run();
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError);
    }

    console.error('Error deleting CRM data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete CRM data',
      message: error.message
    });
  }
});

export default router;
