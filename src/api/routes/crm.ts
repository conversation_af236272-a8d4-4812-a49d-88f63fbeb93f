import express from 'express';
import { DealRepository } from '../repositories/deal-repository';
import { CompanyRepository } from '../repositories/company-repository';
import { ContactRepository } from '../repositories/contact-repository';
import { NoteRepository } from '../repositories/note-repository';
import { ContactRoleRepository } from '../repositories/relationships/contact-role-repository';
import { DealEstimateRepository } from '../repositories/relationships/deal-estimate-repository';
import {
  DealCreate,
  DealUpdate,
  ContactCreate,
  ContactUpdate,
  NoteCreate
} from '../../frontend/types/crm-types';
import {
  CompanyCreate,
  CompanyUpdate,
  Company
} from '../../types/company-types';
import activityLogger from '../../utils/activity-logger';

const router = express.Router();
const dealRepository = new DealRepository();
const companyRepository = new CompanyRepository();
const contactRepository = new ContactRepository();
const noteRepository = new NoteRepository();
const contactRoleRepository = new ContactRoleRepository();
const dealEstimateRepository = new DealEstimateRepository();

/**
 * @route GET /api/crm/deals
 * @desc Get all deals
 * @access Private
 */
router.get('/deals', async (req, res) => {
  try {
    const deals = dealRepository.getAllDeals();

    // Add estimates to each deal
    for (const deal of deals) {
      const estimateLinks = dealEstimateRepository.getEstimatesForDeal(deal.id);
      deal.estimates = estimateLinks.map(link => ({
        id: link.estimateId, // Use estimateId as id for now
        estimateId: link.estimateId,
        estimateType: link.estimateType,
        type: link.estimateType as 'draft' | 'harvest',
        linkedAt: link.linkedAt, // Use actual linked date from database
        linkedBy: link.linkedBy || 'system' // Use actual linked by from database
      }));
    }

    res.json({
      success: true,
      data: deals
    });
  } catch (error: any) {
    console.error('Error getting deals:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get deals',
      message: error.message
    });
  }
});

/**
 * @route GET /api/crm/deals/:id
 * @desc Get a deal by ID
 * @access Private
 */
router.get('/deals/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const deal = dealRepository.getDealById(id);

    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    // Get linked estimates
    const estimateLinks = dealEstimateRepository.getEstimatesForDeal(id);
    deal.estimates = estimateLinks.map(link => ({
      id: link.estimateId, // Use estimateId as id for now
      estimateId: link.estimateId,
      estimateType: link.estimateType,
      type: link.estimateType as 'draft' | 'harvest',
      linkedAt: link.linkedAt, // Use actual linked date from database
      linkedBy: link.linkedBy || 'system' // Use actual linked by from database
    }));

    res.json({
      success: true,
      data: deal
    });
  } catch (error: any) {
    console.error('Error getting deal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get deal',
      message: error.message
    });
  }
});

/**
 * @route POST /api/crm/deals
 * @desc Create a new deal
 * @access Private
 */
router.post('/deals', async (req, res) => {
  try {
    const dealData: DealCreate = req.body;

    // Validate required fields
    if (!dealData.name || !dealData.stage) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        requiredFields: ['name', 'stage']
      });
    }

    const deal = dealRepository.createDeal(dealData);

    if (!deal) {
      return res.status(500).json({
        success: false,
        error: 'Failed to create deal'
      });
    }

    // Log deal creation activity
    try {
      await activityLogger.logDealCreated(
        deal.id,
        deal.name,
        req.session?.userInfo?.sub || 'unknown-user'
      );
    } catch (activityError) {
      console.error('Error logging deal creation activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    res.status(201).json({
      success: true,
      data: deal
    });
  } catch (error: any) {
    console.error('Error creating deal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create deal',
      message: error.message
    });
  }
});

/**
 * @route PUT /api/crm/deals/:id
 * @desc Update a deal
 * @access Private
 */
router.put('/deals/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const dealData: DealUpdate = req.body;

    const deal = dealRepository.updateDeal(id, dealData);

    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    // Log deal update activity
    try {
      await activityLogger.logDealUpdated(
        deal.id,
        deal.name,
        dealData, // Pass the update data as changes
        req.session?.userInfo?.sub || 'unknown-user'
      );
    } catch (activityError) {
      console.error('Error logging deal update activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    res.json({
      success: true,
      data: deal
    });
  } catch (error: any) {
    console.error('Error updating deal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update deal',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/crm/deals/:id
 * @desc Delete a deal
 * @access Private
 */
router.delete('/deals/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const success = dealRepository.deleteDeal(id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    res.json({
      success: true,
      message: 'Deal deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting deal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete deal',
      message: error.message
    });
  }
});

/**
 * @route GET /api/crm/contacts
 * @desc Get all contacts
 * @access Private
 */
router.get('/contacts', async (req, res) => {
  try {
    const contacts = contactRepository.getAllContacts();
    res.json({
      success: true,
      data: contacts
    });
  } catch (error: any) {
    console.error('Error getting contacts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get contacts',
      message: error.message
    });
  }
});

/**
 * @route GET /api/crm/contacts/:id
 * @desc Get a contact by ID
 * @access Private
 */
router.get('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const contact = contactRepository.getContactById(id);

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: 'Contact not found'
      });
    }

    res.json({
      success: true,
      data: contact
    });
  } catch (error: any) {
    console.error('Error getting contact:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get contact',
      message: error.message
    });
  }
});

/**
 * @route POST /api/crm/contacts
 * @desc Create a new contact
 * @access Private
 */
router.post('/contacts', async (req, res) => {
  try {
    const contactData: ContactCreate = req.body;

    // Validate required fields
    if (!contactData.firstName || !contactData.lastName) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        requiredFields: ['firstName', 'lastName']
      });
    }

    const contact = contactRepository.createContact(contactData);

    if (!contact) {
      return res.status(500).json({
        success: false,
        error: 'Failed to create contact'
      });
    }

    // Log contact creation activity
    try {
      const contactName = `${contact.firstName} ${contact.lastName}`;
      await activityLogger.logContactCreated(
        contact.id,
        contactName,
        req.session?.userInfo?.sub || 'unknown-user'
      );
    } catch (activityError) {
      console.error('Error logging contact creation activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    res.status(201).json({
      success: true,
      data: contact
    });
  } catch (error: any) {
    console.error('Error creating contact:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create contact',
      message: error.message
    });
  }
});

/**
 * @route PUT /api/crm/contacts/:id
 * @desc Update a contact
 * @access Private
 */
router.put('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const contactData: ContactUpdate = req.body;

    const contact = contactRepository.updateContact(id, contactData);

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: 'Contact not found'
      });
    }

    res.json({
      success: true,
      data: contact
    });
  } catch (error: any) {
    console.error('Error updating contact:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update contact',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/crm/contacts/:id
 * @desc Delete a contact
 * @access Private
 */
router.delete('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const success = contactRepository.deleteContact(id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Contact not found'
      });
    }

    res.json({
      success: true,
      message: 'Contact deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting contact:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete contact',
      message: error.message
    });
  }
});

/**
 * @route GET /api/crm/companies
 * @desc Get all companies
 * @access Private
 */
router.get('/companies', async (req, res) => {
  try {
    // Get all companies with deal statistics
    const companies = companyRepository.getAllCompanies();

    res.json({
      success: true,
      data: companies
    });
  } catch (error: any) {
    console.error('Error getting companies:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get companies',
      message: error.message
    });
  }
});

/**
 * @route GET /api/crm/companies/linking-status
 * @desc Get all companies with their linking status to external systems
 * @access Private
 */
router.get('/companies/linking-status', async (req, res) => {
  try {
    const companiesWithStatus = companyRepository.getAllCompaniesWithLinkingStatus();
    res.json({
      success: true,
      data: companiesWithStatus
    });
  } catch (error: any) {
    console.error('Error getting companies with linking status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get companies with linking status',
      message: error.message
    });
  }
});

/**
 * @route GET /api/crm/companies/unlinked
 * @desc Get companies that need linking between systems (legacy endpoint)
 * @access Private
 */
router.get('/companies/unlinked', async (req, res) => {
  try {
    const unlinkedCompanies = companyRepository.getUnlinkedCompanies();
    res.json({
      success: true,
      data: unlinkedCompanies
    });
  } catch (error: any) {
    console.error('Error getting unlinked companies:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get unlinked companies',
      message: error.message
    });
  }
});

/**
 * @route GET /api/crm/companies/:id
 * @desc Get a company by ID
 * @access Private
 */
router.get('/companies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const company = companyRepository.getCompanyById(id);

    if (!company) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    // Include source information in response for clarity
    const source = company.source || 'Unknown';
    const hasRadarData = company.radarState !== undefined && company.radarState !== null;

    res.json({
      success: true,
      data: company,
      meta: {
        source,
        hasRadarData
      }
    });
  } catch (error: any) {
    console.error('Error getting company:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get company',
      message: error.message
    });
  }
});

/**
 * @route POST /api/crm/companies
 * @desc Create a new company
 * @access Private
 */
router.post('/companies', async (req, res) => {
  try {
    const companyData: CompanyCreate = req.body;

    // Validate required fields
    if (!companyData.name) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        requiredFields: ['name']
      });
    }

    // Validate radar state if provided
    if (companyData.radarState &&
        !['Strategy', 'Transformation', 'BAU', 'Transition out'].includes(companyData.radarState)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid radar state',
        message: 'Radar state must be one of: Strategy, Transformation, BAU, Transition out'
      });
    }

    // Validate priority if provided
    if (companyData.priority &&
        !['High', 'Medium', 'Low', 'Qualified out'].includes(companyData.priority)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid priority',
        message: 'Priority must be one of: High, Medium, Low, Qualified out'
      });
    }

    // If no source is specified, default to 'Manual'
    if (!companyData.source) {
      companyData.source = 'Manual';
    }

    // Check if source is valid
    if (!['HubSpot', 'Harvest', 'Manual'].includes(companyData.source)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid source',
        message: 'Source must be one of: HubSpot, Harvest, Manual'
      });
    }

    const company = companyRepository.createCompany(companyData, req.body.createdBy || 'api');

    if (!company) {
      return res.status(500).json({
        success: false,
        error: 'Failed to create company'
      });
    }

    // Log company creation activity
    try {
      await activityLogger.logCompanyCreated(
        company.id,
        company.name,
        req.session?.userInfo?.sub || 'unknown-user'
      );
    } catch (activityError) {
      console.error('Error logging company creation activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    res.status(201).json({
      success: true,
      data: company
    });
  } catch (error: any) {
    console.error('Error creating company:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create company',
      message: error.message
    });
  }
});

/**
 * @route PUT /api/crm/companies/:id
 * @desc Update a company (includes both CRM and Radar fields)
 * @access Private
 */
router.put('/companies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const companyData: CompanyUpdate = req.body;

    // Validate the company data including Radar-specific fields
    const { validateCompanyData } = require('../../utils/data-validation');
    const validation = validateCompanyData(companyData);

    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid company data',
        validationErrors: validation.errors
      });
    }

    // Track updater
    companyData.updatedBy = req.session?.user?.email || 'system';

    const company = companyRepository.updateCompany(id, companyData);

    if (!company) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    // If this company has Radar fields, invalidate the Radar companies cache
    if (company.radarState || companyData.radarState) {
      // This helps ensure Radar view stays in sync
      res.locals.invalidateCache = res.locals.invalidateCache || [];
      res.locals.invalidateCache.push('radarCompanies');
    }

    // Log the company update activity
    try {
      const activityLogger = await import('../../utils/activity-logger');
      
      // Check if radar fields were updated
      const hasRadarChanges = companyData.radarState !== undefined || 
                             companyData.priority !== undefined || 
                             companyData.notes !== undefined;
      
      if (hasRadarChanges) {
        // Log as radar change
        await activityLogger.default.logCompanyRadarChanged(
          id,
          company.name || 'Unknown Company',
          companyData,
          companyData.updatedBy || 'system'
        );
      } else {
        // Log as general company update
        await activityLogger.default.logCompanyUpdated(
          id,
          company.name || 'Unknown Company',
          companyData,
          companyData.updatedBy || 'system'
        );
      }
    } catch (logError) {
      console.warn('Failed to log company update activity:', logError);
      // Don't fail the request if logging fails
    }

    res.json({
      success: true,
      data: company
    });
  } catch (error: any) {
    console.error('Error updating company:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update company',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/crm/companies/:id
 * @desc Delete a company
 * @access Private
 */
router.delete('/companies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const success = companyRepository.deleteCompany(id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      message: 'Company deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting company:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete company',
      message: error.message
    });
  }
});

/**
 * @route POST /api/crm/deals/:dealId/contacts/:contactId
 * @desc Associate a contact with a deal
 * @access Private
 */
router.post('/deals/:dealId/contacts/:contactId', async (req, res) => {
  try {
    const { dealId, contactId } = req.params;
    const { role } = req.body;

    const relationship = contactRoleRepository.addContactToDeal(dealId, contactId, role);
    const success = !!relationship;

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Deal or contact not found'
      });
    }

    res.json({
      success: true,
      message: 'Contact associated with deal successfully'
    });
  } catch (error: any) {
    console.error('Error associating contact with deal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to associate contact with deal',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/crm/deals/:dealId/contacts/:contactId
 * @desc Disassociate a contact from a deal
 * @access Private
 */
router.delete('/deals/:dealId/contacts/:contactId', async (req, res) => {
  try {
    const { dealId, contactId } = req.params;

    const success = contactRoleRepository.removeContactFromDeal(dealId, contactId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Association not found'
      });
    }

    res.json({
      success: true,
      message: 'Contact disassociated from deal successfully'
    });
  } catch (error: any) {
    console.error('Error disassociating contact from deal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to disassociate contact from deal',
      message: error.message
    });
  }
});

/**
 * @route POST /api/crm/deals/:dealId/notes
 * @desc Add a note to a deal
 * @access Private
 */
router.post('/deals/:dealId/notes', async (req, res) => {
  try {
    const { dealId } = req.params;
    const { content, createdBy } = req.body;

    // Validate required fields
    if (!content) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        requiredFields: ['content']
      });
    }

    const noteData: NoteCreate = {
      dealId,
      content,
      createdBy
    };

    const note = noteRepository.createNote(noteData);

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    // Log note addition activity
    try {
      // Get deal name for the activity log
      const deal = dealRepository.getDealById(dealId);
      const dealName = deal ? deal.name : 'Unknown Deal';

      await activityLogger.logNoteAdded(
        dealId,
        dealName,
        content,
        req.session?.userInfo?.sub || 'unknown-user'
      );
    } catch (activityError) {
      console.error('Error logging note addition activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    res.status(201).json({
      success: true,
      data: note
    });
  } catch (error: any) {
    console.error('Error adding note to deal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to add note to deal',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/crm/notes/:id
 * @desc Delete a note
 * @access Private
 */
router.delete('/notes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const success = noteRepository.deleteNote(id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    res.json({
      success: true,
      message: 'Note deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting note:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete note',
      message: error.message
    });
  }
});

/**
 * @route GET /api/crm/deals/:id/estimates
 * @desc Get all estimates linked to a deal
 * @access Private
 */
router.get('/deals/:id/estimates', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if deal exists
    const deal = dealRepository.getDealById(id);
    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    const estimates = dealEstimateRepository.getEstimatesForDeal(id);

    res.json({
      success: true,
      data: estimates
    });
  } catch (error: any) {
    console.error('Error getting deal estimates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get deal estimates',
      message: error.message
    });
  }
});

/**
 * @route POST /api/crm/deals/:id/estimates
 * @desc Link an estimate to a deal
 * @access Private
 */
router.post('/deals/:id/estimates', async (req, res) => {
  try {
    const { id } = req.params;
    const { estimateId, estimateType, linkedBy } = req.body;

    // Validate required fields
    if (!estimateId || !estimateType) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        requiredFields: ['estimateId', 'estimateType']
      });
    }

    // Validate estimate type
    if (estimateType !== 'draft' && estimateType !== 'harvest') {
      return res.status(400).json({
        success: false,
        error: 'Invalid estimate type',
        validTypes: ['draft', 'harvest']
      });
    }

    // Check if deal exists
    const deal = dealRepository.getDealById(id);
    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    // Link the estimate to the deal
    try {
      dealEstimateRepository.linkDealToEstimate(id, estimateId, estimateType, linkedBy);
      // Link successful
    } catch (linkError) {
      console.error('Error linking estimate to deal:', linkError);
      return res.status(500).json({
        success: false,
        error: 'Failed to link estimate to deal',
        message: linkError.message
      });
    }

    // Update deal information based on the estimate
    try {
      let estimateData = null;

      // Get estimate data based on type
      if (estimateType === 'draft') {
        // Get draft estimate data
        const { estimateDraftsRepository } = require('../repositories/estimate-drafts-repository');
        estimateData = estimateDraftsRepository.getDraftEstimateById(estimateId);

        console.log('Draft estimate data:', JSON.stringify(estimateData, null, 2));
        console.log('Current deal data:', JSON.stringify(deal, null, 2));

        if (estimateData) {
          // Import the field ownership functions
          const { setFieldOwner } = require('../../utils/deal-tracking');

          // Prepare deal update data
          const dealUpdateData: any = {};

          // Always update estimate-controlled fields, regardless of whether they're already set
          // This ensures the deal always reflects the latest estimate data

          // Update project details
          if (estimateData.projectName) {
            // Only update name if it's generic or not set
            if (!deal.name || deal.name === 'New Deal') {
              dealUpdateData.name = estimateData.projectName;
              console.log('Setting deal name to:', estimateData.projectName);
            }
          }

          // Always update these fields from the estimate
          if (estimateData.startDate) {
            dealUpdateData.startDate = estimateData.startDate;
            console.log('Setting deal start date to:', estimateData.startDate);
            // Set field ownership to Estimate
            setFieldOwner(id, 'startDate', 'Estimate');
          }

          if (estimateData.endDate) {
            dealUpdateData.endDate = estimateData.endDate;
            console.log('Setting deal end date to:', estimateData.endDate);
            // Set field ownership to Estimate
            setFieldOwner(id, 'endDate', 'Estimate');
          }

          if (estimateData.invoiceFrequency) {
            dealUpdateData.invoiceFrequency = estimateData.invoiceFrequency;
            console.log('Setting deal invoice frequency to:', estimateData.invoiceFrequency);
            // Set field ownership to Estimate
            setFieldOwner(id, 'invoiceFrequency', 'Estimate');
          }

          if (estimateData.paymentTerms) {
            dealUpdateData.paymentTerms = estimateData.paymentTerms;
            console.log('Setting deal payment terms to:', estimateData.paymentTerms);
            // Set field ownership to Estimate
            setFieldOwner(id, 'paymentTerms', 'Estimate');
          }

          // Use the totalFees value from the estimate
          if (estimateData.totalFees) {
            dealUpdateData.value = estimateData.totalFees;
            dealUpdateData.currency = 'AUD'; // Default to AUD
            console.log('Setting deal value to estimate total fees:', estimateData.totalFees);

            // Set field ownership for the value field
            setFieldOwner(id, 'value', 'Estimate');

            // Set probability based on deal stage if it's not already set
            if (deal.probability === null || deal.probability === undefined) {
              // Helper function to get default probability based on stage
              function getDefaultProbability(stage: string): number {
                switch (stage) {
                  case "Identified":
                    return 0.1; // 10%
                  case "Qualified":
                    return 0.3; // 30%
                  case "Solution proposal":
                    return 0.5; // 50%
                  case "Solution presentation":
                    return 0.7; // 70%
                  case "Objection handling":
                    return 0.8; // 80%
                  case "Finalising terms":
                    return 0.9; // 90%
                  case "Closed won":
                    return 1.0; // 100%
                  default:
                    return 0.5; // 50% default
                }
              }

              dealUpdateData.probability = getDefaultProbability(deal.stage);
              console.log('Setting deal probability based on stage:', dealUpdateData.probability);

              // Set field ownership for the probability field
              setFieldOwner(id, 'probability', 'Estimate');
            }
          } else {
            console.warn('No total fees value found in estimate data');
          }

          // Apply the updates
          if (Object.keys(dealUpdateData).length > 0) {
            console.log('Updating deal with estimate data:', dealUpdateData);
            const updatedDeal = dealRepository.updateDeal(id, dealUpdateData, 'Estimate');
            console.log('Updated deal with estimate data result:', updatedDeal);
          } else {
            console.log('No deal updates needed');
          }
        }
      } else if (estimateType === 'harvest') {
        // Get Harvest estimate data
        const harvestService = require('../services/harvest-service');
        const harvestEstimates = await harvestService.getEstimates();
        estimateData = harvestEstimates.find((e: any) => e.id.toString() === estimateId);

        console.log('Harvest estimate data:', JSON.stringify(estimateData, null, 2));
        console.log('Current deal data:', JSON.stringify(deal, null, 2));

        if (estimateData) {
          // Import the field ownership functions
          const { setFieldOwner } = require('../../utils/deal-tracking');

          // Prepare deal update data
          const dealUpdateData: any = {};

          // Always update value from Harvest estimate
          if (estimateData.amount) {
            dealUpdateData.value = parseFloat(estimateData.amount);
            dealUpdateData.currency = 'AUD'; // Default to AUD
            console.log('Setting deal value to:', parseFloat(estimateData.amount));

            // Don't change the probability - respect the existing value
            // Only set field ownership for the value field
            setFieldOwner(id, 'value', 'Estimate');
          }

          // Update deal name if it's generic or not set
          if ((!deal.name || deal.name === 'New Deal') && estimateData.subject) {
            dealUpdateData.name = estimateData.subject;
            console.log('Setting deal name to:', estimateData.subject);
          }

          // Always update start date from Harvest estimate
          if (estimateData.issue_date) {
            dealUpdateData.startDate = estimateData.issue_date;
            console.log('Setting deal start date to:', estimateData.issue_date);
            // Set field ownership
            setFieldOwner(id, 'startDate', 'Estimate');
          }

          // Always update end date from Harvest estimate
          let endDate = null;

          if (estimateData.sent_at) {
            endDate = estimateData.sent_at;
            console.log('Setting deal end date to sent_at date:', estimateData.sent_at);
          } else if (estimateData.issue_date) {
            // Calculate a date 3 months from the issue date
            try {
              const issueDate = new Date(estimateData.issue_date);
              endDate = new Date(issueDate);
              endDate.setMonth(endDate.getMonth() + 3);
              endDate = endDate.toISOString().split('T')[0];
              console.log('Setting deal end date to 3 months from issue date:', endDate);
            } catch (err) {
              console.error('Error calculating end date from issue date:', err);
            }
          }

          if (endDate) {
            dealUpdateData.endDate = endDate;
            // Set field ownership
            setFieldOwner(id, 'endDate', 'Estimate');
          }

          // Set default invoice frequency and payment terms if not provided by Harvest
          // These are not directly available in Harvest estimates, so we set defaults
          if (!deal.invoiceFrequency) {
            dealUpdateData.invoiceFrequency = 'monthly'; // Default to monthly
            console.log('Setting default invoice frequency: monthly');
            setFieldOwner(id, 'invoiceFrequency', 'Estimate');
          }

          if (!deal.paymentTerms) {
            dealUpdateData.paymentTerms = 14; // Default to 14 days
            console.log('Setting default payment terms: 14 days');
            setFieldOwner(id, 'paymentTerms', 'Estimate');
          }

          // Apply the updates
          if (Object.keys(dealUpdateData).length > 0) {
            console.log('Updating deal with Harvest estimate data:', dealUpdateData);
            const updatedDeal = dealRepository.updateDeal(id, dealUpdateData, 'Estimate');
            console.log('Updated deal with Harvest estimate data result:', updatedDeal);
          } else {
            console.log('No deal updates needed');
          }
        } else {
          console.log('No Harvest estimate found with ID:', estimateId);
        }
      }
    } catch (updateError) {
      // Log the error but don't fail the request
      console.error('Error updating deal from estimate data:', updateError);
    }

    res.status(201).json({
      success: true,
      message: 'Estimate linked to deal successfully'
    });
  } catch (error: any) {
    console.error('Error linking estimate to deal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to link estimate to deal',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/crm/deals/:id/estimates/:estimateId
 * @desc Unlink an estimate from a deal
 * @access Private
 */
router.delete('/deals/:id/estimates/:estimateId', async (req, res) => {
  try {
    const { id, estimateId } = req.params;
    const { estimateType } = req.query;

    // Validate estimate type
    if (!estimateType || (estimateType !== 'draft' && estimateType !== 'harvest')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or missing estimate type',
        validTypes: ['draft', 'harvest']
      });
    }

    // Check if deal exists
    const deal = dealRepository.getDealById(id);
    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    const success = dealEstimateRepository.unlinkDealFromEstimate(id, estimateId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Estimate link not found'
      });
    }

    res.json({
      success: true,
      message: 'Estimate unlinked from deal successfully'
    });
  } catch (error: any) {
    console.error('Error unlinking estimate from deal:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to unlink estimate from deal',
      message: error.message
    });
  }
});

/**
 * @route GET /api/crm/estimates/:id/deals
 * @desc Get deals linked to an estimate
 * @access Private
 */
router.get('/estimates/:id/deals', async (req, res) => {
  try {
    const { id } = req.params;
    const { estimateType } = req.query;

    // Validate estimate type
    if (!estimateType || (estimateType !== 'draft' && estimateType !== 'harvest')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or missing estimate type',
        validTypes: ['draft', 'harvest']
      });
    }

    // Get deals linked to this estimate
    const dealRelationships = dealEstimateRepository.getDealsForEstimate(id);

    // If no deals are linked, return an empty array
    if (dealRelationships.length === 0) {
      return res.json({
        success: true,
        data: []
      });
    }

    // Get full deal details for each linked deal
    const deals = dealRelationships.map(relationship => {
      const deal = dealRepository.getDealById(relationship.dealId);
      // We don't need to add linkedAt property to the deal object
      // The frontend already has access to the linked estimates through the deal.estimates property
      return deal;
    }).filter(Boolean); // Remove any null values

    res.json({
      success: true,
      data: deals
    });
  } catch (error: any) {
    console.error('Error getting deals linked to estimate:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get linked deals',
      message: error.message
    });
  }
});

/**
 * @route POST /api/crm/companies/:sourceId/merge/:targetId
 * @desc Merge two companies (useful when same company exists in both systems)
 * @access Private
 */
router.post('/companies/:sourceId/merge/:targetId', async (req, res) => {
  try {
    const { sourceId, targetId } = req.params;

    if (sourceId === targetId) {
      return res.status(400).json({
        success: false,
        error: 'Cannot merge a company with itself'
      });
    }

    const userId = req.session?.user?.email || 'system';
    const mergedCompany = companyRepository.mergeCompanies(sourceId, targetId, userId);

    if (!mergedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Failed to merge companies'
      });
    }

    res.json({
      success: true,
      data: mergedCompany,
      message: 'Companies successfully merged'
    });
  } catch (error: any) {
    console.error('Error merging companies:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to merge companies',
      message: error.message
    });
  }
});

/**
 * @route POST /api/crm/companies/:id/link-hubspot
 * @desc Link a company to HubSpot
 * @access Private
 */
router.post('/companies/:id/link-hubspot', async (req, res) => {
  try {
    const { id } = req.params;
    const { hubspotId } = req.body;

    if (!hubspotId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required field: hubspotId'
      });
    }

    const userId = req.session?.user?.email || 'system';
    const updatedCompany = companyRepository.linkCompanyToHubSpot(id, hubspotId, userId);

    if (!updatedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      data: updatedCompany,
      message: 'Company successfully linked to HubSpot'
    });
  } catch (error: any) {
    console.error('Error linking company to HubSpot:', error);

    if (error.message.includes('already linked')) {
      return res.status(409).json({
        success: false,
        error: 'Conflict',
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to link company to HubSpot',
      message: error.message
    });
  }
});

/**
 * @route POST /api/crm/companies/:id/link-harvest
 * @desc Link a company to Harvest
 * @access Private
 */
router.post('/companies/:id/link-harvest', async (req, res) => {
  try {
    const { id } = req.params;
    const { harvestId } = req.body;

    if (!harvestId || typeof harvestId !== 'number') {
      return res.status(400).json({
        success: false,
        error: 'Missing or invalid required field: harvestId (must be number)'
      });
    }

    const userId = req.session?.user?.email || 'system';
    const updatedCompany = companyRepository.linkCompanyToHarvest(id, harvestId, userId);

    if (!updatedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      data: updatedCompany,
      message: 'Company successfully linked to Harvest'
    });
  } catch (error: any) {
    console.error('Error linking company to Harvest:', error);

    if (error.message.includes('already linked')) {
      return res.status(409).json({
        success: false,
        error: 'Conflict',
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to link company to Harvest',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/crm/companies/:id/link-hubspot
 * @desc Unlink a company from HubSpot
 * @access Private
 */
router.delete('/companies/:id/link-hubspot', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.session?.user?.email || 'system';

    const updatedCompany = companyRepository.unlinkCompanyFromHubSpot(id, userId);

    if (!updatedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      data: updatedCompany,
      message: 'Company successfully unlinked from HubSpot'
    });
  } catch (error: any) {
    console.error('Error unlinking company from HubSpot:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to unlink company from HubSpot',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/crm/companies/:id/link-harvest
 * @desc Unlink a company from Harvest
 * @access Private
 */
router.delete('/companies/:id/link-harvest', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.session?.user?.email || 'system';

    const updatedCompany = companyRepository.unlinkCompanyFromHarvest(id, userId);

    if (!updatedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      data: updatedCompany,
      message: 'Company successfully unlinked from Harvest'
    });
  } catch (error: any) {
    console.error('Error unlinking company from Harvest:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to unlink company from Harvest',
      message: error.message
    });
  }
});



export default router;
