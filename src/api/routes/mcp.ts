import { Router, Request, Response } from 'express';
import { randomUUID } from 'crypto';
import { getXeroService } from '../../services/xero';
import Anthropic from '@anthropic-ai/sdk';

// Extend the Express Request type to include session
declare module 'express-session' {
  interface SessionData {
    tokenSet?: any;
    tenantId?: string;
    userInfo?: {
      name?: string;
      email?: string;
      sub?: string;
    };
  }
}

const router = Router();

// Store active MCP sessions with conversation history
const sessions: Map<string, MCPSession> = new Map();

interface MCPSession {
  id: string;
  userId: string;
  lastActivity: Date;
  messages: Anthropic.MessageParam[];
}

interface Tool {
  name: string;
  description: string;
  input_schema: {
    type: string;
    properties: Record<string, any>;
    required?: string[];
  };
}

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || '',
});

// Define available MCP tools in Anthropic's expected format
const AVAILABLE_TOOLS: Tool[] = [
  {
    name: 'list_invoices',
    description: 'List invoices from Xero. Returns invoice details including number, customer, amount, and status.',
    input_schema: {
      type: 'object',
      properties: {
        page: {
          type: 'integer',
          description: 'Page number for pagination (default: 1)'
        },
        pageSize: {
          type: 'integer',
          description: 'Number of items per page (default: 10)'
        },
        status: {
          type: 'string',
          description: 'Filter by status (DRAFT, AUTHORISED, PAID)'
        }
      },
      required: []
    }
  },
  {
    name: 'list_contacts',
    description: 'List contacts (customers and suppliers) from Xero.',
    input_schema: {
      type: 'object',
      properties: {
        page: {
          type: 'integer',
          description: 'Page number for pagination (default: 1)'
        },
        pageSize: {
          type: 'integer',
          description: 'Number of items per page (default: 10)'
        }
      },
      required: []
    }
  },
  {
    name: 'list_accounts',
    description: 'List chart of accounts from Xero. Shows account codes, names, and types.',
    input_schema: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  {
    name: 'list_payments',
    description: 'List payments from Xero.',
    input_schema: {
      type: 'object',
      properties: {
        page: {
          type: 'integer',
          description: 'Page number for pagination (default: 1)'
        },
        pageSize: {
          type: 'integer',
          description: 'Number of items per page (default: 10)'
        }
      },
      required: []
    }
  },
  {
    name: 'list_bank_transactions',
    description: 'List bank transactions from Xero.',
    input_schema: {
      type: 'object',
      properties: {
        page: {
          type: 'integer',
          description: 'Page number for pagination (default: 1)'
        },
        pageSize: {
          type: 'integer',
          description: 'Number of items per page (default: 10)'
        }
      },
      required: []
    }
  },
  {
    name: 'list_bills',
    description: 'List bills (accounts payable) from Xero.',
    input_schema: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  {
    name: 'get_organisation_info',
    description: 'Get organization information from Xero.',
    input_schema: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  {
    name: 'get_balance_sheet',
    description: 'Get balance sheet report from Xero for a specific date.',
    input_schema: {
      type: 'object',
      properties: {
        date: {
          type: 'string',
          description: 'Date for the balance sheet (YYYY-MM-DD format)'
        }
      },
      required: []
    }
  },
  {
    name: 'get_profit_loss',
    description: 'Get profit and loss report from Xero for a date range.',
    input_schema: {
      type: 'object',
      properties: {
        fromDate: {
          type: 'string',
          description: 'Start date (YYYY-MM-DD format)'
        },
        toDate: {
          type: 'string',
          description: 'End date (YYYY-MM-DD format)'
        }
      },
      required: []
    }
  },
  {
    name: 'get_bank_balance',
    description: 'Get current bank account balances from Xero. Shows all bank accounts with their current balances.',
    input_schema: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  {
    name: 'list_credit_notes',
    description: 'List credit notes from Xero.',
    input_schema: {
      type: 'object',
      properties: {
        page: {
          type: 'integer',
          description: 'Page number for pagination (default: 1)'
        },
        pageSize: {
          type: 'integer',
          description: 'Number of items per page (default: 10)'
        }
      },
      required: []
    }
  },
  {
    name: 'list_items',
    description: 'List items (products/services) from Xero.',
    input_schema: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  {
    name: 'list_quotes',
    description: 'List quotes from Xero.',
    input_schema: {
      type: 'object',
      properties: {
        page: {
          type: 'integer',
          description: 'Page number for pagination (default: 1)'
        }
      },
      required: []
    }
  },
  {
    name: 'list_tax_rates',
    description: 'List tax rates from Xero.',
    input_schema: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  {
    name: 'get_trial_balance',
    description: 'Get trial balance report from Xero.',
    input_schema: {
      type: 'object',
      properties: {
        date: {
          type: 'string',
          description: 'Date for the trial balance (YYYY-MM-DD format)'
        }
      },
      required: []
    }
  },
  // Payroll Operations
  {
    name: 'list_payroll_employees',
    description: 'List payroll employees from Xero.',
    input_schema: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  {
    name: 'list_payroll_employee_leave',
    description: 'List leave records for a payroll employee.',
    input_schema: {
      type: 'object',
      properties: {
        employeeId: {
          type: 'string',
          description: 'Employee ID to get leave records for'
        }
      },
      required: ['employeeId']
    }
  },
  {
    name: 'list_payroll_employee_leave_balances',
    description: 'List leave balances for a payroll employee.',
    input_schema: {
      type: 'object',
      properties: {
        employeeId: {
          type: 'string',
          description: 'Employee ID to get leave balances for'
        }
      },
      required: ['employeeId']
    }
  },
  {
    name: 'list_payroll_employee_leave_types',
    description: 'List leave types for a payroll employee.',
    input_schema: {
      type: 'object',
      properties: {
        employeeId: {
          type: 'string',
          description: 'Employee ID to get leave types for'
        }
      },
      required: ['employeeId']
    }
  },
  {
    name: 'list_payroll_leave_periods',
    description: 'List leave periods for a payroll employee.',
    input_schema: {
      type: 'object',
      properties: {
        employeeId: {
          type: 'string',
          description: 'Employee ID to get leave periods for'
        }
      },
      required: ['employeeId']
    }
  },
  {
    name: 'list_payroll_leave_types',
    description: 'List all available leave types in Xero Payroll.',
    input_schema: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  {
    name: 'get_payroll_timesheet',
    description: 'Get a specific payroll timesheet.',
    input_schema: {
      type: 'object',
      properties: {
        timesheetId: {
          type: 'string',
          description: 'Timesheet ID to retrieve'
        }
      },
      required: ['timesheetId']
    }
  },
  // Aged Reports
  {
    name: 'list_aged_receivables_by_contact',
    description: 'Get aged receivables report for a specific contact.',
    input_schema: {
      type: 'object',
      properties: {
        contactId: {
          type: 'string',
          description: 'Contact ID to get aged receivables for'
        },
        date: {
          type: 'string',
          description: 'Date for the report (YYYY-MM-DD format)'
        }
      },
      required: ['contactId']
    }
  },
  {
    name: 'list_aged_payables_by_contact',
    description: 'Get aged payables report for a specific contact.',
    input_schema: {
      type: 'object',
      properties: {
        contactId: {
          type: 'string',
          description: 'Contact ID to get aged payables for'
        },
        date: {
          type: 'string',
          description: 'Date for the report (YYYY-MM-DD format)'
        }
      },
      required: ['contactId']
    }
  },
  // Contact Groups
  {
    name: 'list_contact_groups',
    description: 'List contact groups from Xero.',
    input_schema: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  // Write Operations - Create
  {
    name: 'create_contact',
    description: 'Create a new contact in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: 'Contact name'
        },
        email: {
          type: 'string',
          description: 'Contact email address'
        },
        phone: {
          type: 'string',
          description: 'Contact phone number'
        },
        isCustomer: {
          type: 'boolean',
          description: 'Whether this contact is a customer'
        },
        isSupplier: {
          type: 'boolean',
          description: 'Whether this contact is a supplier'
        }
      },
      required: ['name']
    }
  },
  {
    name: 'create_invoice',
    description: 'Create a new invoice in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        contactId: {
          type: 'string',
          description: 'Contact ID for the invoice'
        },
        type: {
          type: 'string',
          description: 'Invoice type (ACCREC for sales, ACCPAY for bills)'
        },
        lineItems: {
          type: 'array',
          description: 'Array of line items for the invoice',
          items: {
            type: 'object',
            properties: {
              description: { type: 'string' },
              quantity: { type: 'number' },
              unitAmount: { type: 'number' },
              accountCode: { type: 'string' }
            }
          }
        },
        dueDate: {
          type: 'string',
          description: 'Due date (YYYY-MM-DD format)'
        }
      },
      required: ['contactId', 'type', 'lineItems']
    }
  },
  {
    name: 'create_payment',
    description: 'Create a new payment in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        invoiceId: {
          type: 'string',
          description: 'Invoice ID to apply payment to'
        },
        accountId: {
          type: 'string',
          description: 'Bank account ID for the payment'
        },
        amount: {
          type: 'number',
          description: 'Payment amount'
        },
        date: {
          type: 'string',
          description: 'Payment date (YYYY-MM-DD format)'
        }
      },
      required: ['invoiceId', 'accountId', 'amount']
    }
  },
  {
    name: 'create_quote',
    description: 'Create a new quote in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        contactId: {
          type: 'string',
          description: 'Contact ID for the quote'
        },
        lineItems: {
          type: 'array',
          description: 'Array of line items for the quote',
          items: {
            type: 'object',
            properties: {
              description: { type: 'string' },
              quantity: { type: 'number' },
              unitAmount: { type: 'number' }
            }
          }
        },
        expiryDate: {
          type: 'string',
          description: 'Quote expiry date (YYYY-MM-DD format)'
        }
      },
      required: ['contactId', 'lineItems']
    }
  },
  {
    name: 'create_credit_note',
    description: 'Create a new credit note in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        contactId: {
          type: 'string',
          description: 'Contact ID for the credit note'
        },
        type: {
          type: 'string',
          description: 'Credit note type (ACCRECCREDIT for sales, ACCPAYCREDIT for purchases)'
        },
        lineItems: {
          type: 'array',
          description: 'Array of line items for the credit note',
          items: {
            type: 'object',
            properties: {
              description: { type: 'string' },
              quantity: { type: 'number' },
              unitAmount: { type: 'number' },
              accountCode: { type: 'string' }
            }
          }
        }
      },
      required: ['contactId', 'type', 'lineItems']
    }
  },
  // Write Operations - Update
  {
    name: 'update_contact',
    description: 'Update an existing contact in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        contactId: {
          type: 'string',
          description: 'Contact ID to update'
        },
        name: {
          type: 'string',
          description: 'Contact name'
        },
        email: {
          type: 'string',
          description: 'Contact email address'
        },
        phone: {
          type: 'string',
          description: 'Contact phone number'
        }
      },
      required: ['contactId']
    }
  },
  {
    name: 'update_invoice',
    description: 'Update an existing draft invoice in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        invoiceId: {
          type: 'string',
          description: 'Invoice ID to update'
        },
        lineItems: {
          type: 'array',
          description: 'Array of line items for the invoice',
          items: {
            type: 'object',
            properties: {
              description: { type: 'string' },
              quantity: { type: 'number' },
              unitAmount: { type: 'number' },
              accountCode: { type: 'string' }
            }
          }
        },
        dueDate: {
          type: 'string',
          description: 'Due date (YYYY-MM-DD format)'
        }
      },
      required: ['invoiceId']
    }
  },
  {
    name: 'update_quote',
    description: 'Update an existing draft quote in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        quoteId: {
          type: 'string',
          description: 'Quote ID to update'
        },
        lineItems: {
          type: 'array',
          description: 'Array of line items for the quote',
          items: {
            type: 'object',
            properties: {
              description: { type: 'string' },
              quantity: { type: 'number' },
              unitAmount: { type: 'number' }
            }
          }
        },
        expiryDate: {
          type: 'string',
          description: 'Quote expiry date (YYYY-MM-DD format)'
        }
      },
      required: ['quoteId']
    }
  },
  {
    name: 'update_credit_note',
    description: 'Update an existing draft credit note in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        creditNoteId: {
          type: 'string',
          description: 'Credit note ID to update'
        },
        lineItems: {
          type: 'array',
          description: 'Array of line items for the credit note',
          items: {
            type: 'object',
            properties: {
              description: { type: 'string' },
              quantity: { type: 'number' },
              unitAmount: { type: 'number' },
              accountCode: { type: 'string' }
            }
          }
        }
      },
      required: ['creditNoteId']
    }
  },
  // Payroll Write Operations
  {
    name: 'create_payroll_timesheet',
    description: 'Create a new payroll timesheet in Xero.',
    input_schema: {
      type: 'object',
      properties: {
        employeeId: {
          type: 'string',
          description: 'Employee ID for the timesheet'
        },
        startDate: {
          type: 'string',
          description: 'Start date for the timesheet (YYYY-MM-DD format)'
        },
        endDate: {
          type: 'string',
          description: 'End date for the timesheet (YYYY-MM-DD format)'
        }
      },
      required: ['employeeId', 'startDate', 'endDate']
    }
  },
  {
    name: 'update_payroll_timesheet_line',
    description: 'Update a line on an existing payroll timesheet.',
    input_schema: {
      type: 'object',
      properties: {
        timesheetId: {
          type: 'string',
          description: 'Timesheet ID to update'
        },
        timesheetLineId: {
          type: 'string',
          description: 'Timesheet line ID to update'
        },
        hours: {
          type: 'number',
          description: 'Number of hours worked'
        },
        date: {
          type: 'string',
          description: 'Date for the timesheet line (YYYY-MM-DD format)'
        }
      },
      required: ['timesheetId', 'timesheetLineId']
    }
  },
  {
    name: 'add_payroll_timesheet_line',
    description: 'Add a new line to an existing payroll timesheet.',
    input_schema: {
      type: 'object',
      properties: {
        timesheetId: {
          type: 'string',
          description: 'Timesheet ID to add line to'
        },
        hours: {
          type: 'number',
          description: 'Number of hours worked'
        },
        date: {
          type: 'string',
          description: 'Date for the timesheet line (YYYY-MM-DD format)'
        },
        earningsRateId: {
          type: 'string',
          description: 'Earnings rate ID for the line'
        }
      },
      required: ['timesheetId', 'hours', 'date']
    }
  },
  {
    name: 'approve_payroll_timesheet',
    description: 'Approve a payroll timesheet.',
    input_schema: {
      type: 'object',
      properties: {
        timesheetId: {
          type: 'string',
          description: 'Timesheet ID to approve'
        }
      },
      required: ['timesheetId']
    }
  },
  {
    name: 'revert_payroll_timesheet',
    description: 'Revert an approved payroll timesheet.',
    input_schema: {
      type: 'object',
      properties: {
        timesheetId: {
          type: 'string',
          description: 'Timesheet ID to revert'
        }
      },
      required: ['timesheetId']
    }
  },
  {
    name: 'delete_payroll_timesheet',
    description: 'Delete an existing payroll timesheet.',
    input_schema: {
      type: 'object',
      properties: {
        timesheetId: {
          type: 'string',
          description: 'Timesheet ID to delete'
        }
      },
      required: ['timesheetId']
    }
  }
];

// Clean up inactive sessions after 30 minutes
setInterval(() => {
  const now = new Date();
  for (const [sessionId, session] of sessions) {
    const inactiveMinutes = (now.getTime() - session.lastActivity.getTime()) / 1000 / 60;
    if (inactiveMinutes > 30) {
      console.log(`Cleaning up inactive MCP session: ${sessionId}`);
      sessions.delete(sessionId);
    }
  }
}, 5 * 60 * 1000); // Check every 5 minutes

/**
 * Execute a tool based on Claude's request
 */
async function executeTool(toolName: string, toolArgs: any, req: Request): Promise<any> {
  const xeroService = getXeroService();
  const client = xeroService.getClient();
  const tenantId = xeroService.getActiveTenantId();

  if (!tenantId) {
    throw new Error('No active Xero tenant');
  }

  let result: any;

  switch (toolName) {
    case 'list_invoices':
      const invoiceResponse = await client.accountingApi.getInvoices(
        tenantId,
        undefined, // ifModifiedSince
        toolArgs.status ? `Status=="${toolArgs.status}"` : undefined, // where
        undefined, // order
        undefined, // invoiceNumbers
        undefined, // contactIDs
        undefined, // statuses
        toolArgs.page || 1,
        undefined  // includeArchived
      );
      result = invoiceResponse.body.invoices || [];
      break;

    case 'list_contacts':
      const contactsResponse = await client.accountingApi.getContacts(
        tenantId,
        undefined, // ifModifiedSince
        undefined, // where
        undefined, // order
        undefined, // contactIDs
        toolArgs.page || 1
      );
      result = contactsResponse.body.contacts || [];
      break;

    case 'list_accounts':
      const accountsResponse = await client.accountingApi.getAccounts(
        tenantId,
        undefined, // ifModifiedSince
        undefined, // where
        undefined  // order
      );
      result = accountsResponse.body.accounts || [];
      break;

    case 'list_payments':
      const paymentsResponse = await client.accountingApi.getPayments(
        tenantId,
        undefined, // ifModifiedSince
        undefined, // where
        undefined, // order
        toolArgs.page || 1
      );
      result = paymentsResponse.body.payments || [];
      break;

    case 'list_bank_transactions':
      const transactionsResponse = await client.accountingApi.getBankTransactions(
        tenantId,
        undefined, // ifModifiedSince
        undefined, // where
        undefined, // order
        toolArgs.page || 1
      );
      result = transactionsResponse.body.bankTransactions || [];
      break;

    case 'list_bills':
      result = await xeroService.bills.getBillsForDisplay(tenantId);
      break;

    case 'get_organisation_info':
      const orgResponse = await client.accountingApi.getOrganisations(tenantId);
      result = orgResponse.body.organisations?.[0] || null;
      break;

    case 'get_balance_sheet':
      const date = toolArgs.date ? new Date(toolArgs.date) : new Date();
      const bsResponse = await client.getBalanceSheetReport(
        tenantId,
        date
      );
      result = bsResponse.body;
      break;

    case 'get_profit_loss':
      const fromDate = toolArgs.fromDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const toDate = toolArgs.toDate || new Date().toISOString().split('T')[0];

      const plResponse = await client.accountingApi.getReportProfitAndLoss(
        tenantId,
        fromDate,
        toDate
      );
      result = plResponse.body;
      break;

    case 'list_credit_notes':
      const creditNotesResponse = await client.accountingApi.getCreditNotes(
        tenantId,
        undefined, // ifModifiedSince
        undefined, // where
        undefined, // order
        toolArgs.page || 1
      );
      result = creditNotesResponse.body.creditNotes || [];
      break;

    case 'list_items':
      const itemsResponse = await client.accountingApi.getItems(
        tenantId,
        undefined, // ifModifiedSince
        undefined, // where
        undefined  // order
      );
      result = itemsResponse.body.items || [];
      break;

    case 'list_quotes':
      const quotesResponse = await client.accountingApi.getQuotes(
        tenantId,
        undefined, // ifModifiedSince
        undefined, // where
        undefined, // order
        undefined, // quoteNumber
        undefined, // contactID
        undefined, // status
        toolArgs.page || 1
      );
      result = quotesResponse.body.quotes || [];
      break;

    case 'list_tax_rates':
      const taxRatesResponse = await client.accountingApi.getTaxRates(
        tenantId,
        undefined, // where
        undefined  // order
      );
      result = taxRatesResponse.body.taxRates || [];
      break;

    case 'get_trial_balance':
      const tbDate = toolArgs.date ? toolArgs.date : new Date().toISOString().split('T')[0];
      const tbResponse = await client.accountingApi.getReportTrialBalance(
        tenantId,
        tbDate
      );
      result = tbResponse.body;
      break;

    // Payroll Operations
    case 'list_payroll_employees':
      const employeesResponse = await client.payrollAUApi.getEmployees(
        tenantId
      );
      result = employeesResponse.body.employees || [];
      break;

    case 'list_payroll_employee_leave':
    case 'list_payroll_employee_leave_balances':
    case 'list_payroll_employee_leave_types':
    case 'list_payroll_leave_periods':
    case 'list_payroll_leave_types':
      // These methods are not available in the current Xero PayrollAU API
      result = { error: 'This payroll operation is not currently supported by the Xero API' };
      break;

    case 'get_payroll_timesheet':
      const timesheetResponse = await client.payrollAUApi.getTimesheet(
        tenantId,
        toolArgs.timesheetId
      );
      result = timesheetResponse.body.timesheet;
      break;

    // Aged Reports
    case 'list_aged_receivables_by_contact':
      const agedReceivablesResponse = await client.accountingApi.getReportAgedReceivablesByContact(
        tenantId,
        toolArgs.contactId,
        toolArgs.date ? toolArgs.date : undefined
      );
      result = agedReceivablesResponse.body;
      break;

    case 'list_aged_payables_by_contact':
      const agedPayablesResponse = await client.accountingApi.getReportAgedPayablesByContact(
        tenantId,
        toolArgs.contactId,
        toolArgs.date ? toolArgs.date : undefined
      );
      result = agedPayablesResponse.body;
      break;

    // Contact Groups
    case 'list_contact_groups':
      const contactGroupsResponse = await client.accountingApi.getContactGroups(
        tenantId
      );
      result = contactGroupsResponse.body.contactGroups || [];
      break;

    // Write Operations - Create
    case 'create_contact':
      const newContact = {
        name: toolArgs.name,
        emailAddress: toolArgs.email,
        phones: toolArgs.phone ? [{ phoneType: 'DEFAULT' as any, phoneNumber: toolArgs.phone }] : undefined,
        isCustomer: toolArgs.isCustomer,
        isSupplier: toolArgs.isSupplier
      };
      const createContactResponse = await client.accountingApi.createContacts(
        tenantId,
        { contacts: [newContact] }
      );
      result = createContactResponse.body.contacts?.[0];
      break;

    case 'create_invoice':
      const newInvoice = {
        type: toolArgs.type,
        contact: { contactID: toolArgs.contactId },
        lineItems: toolArgs.lineItems.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount,
          accountCode: item.accountCode
        })),
        dueDate: toolArgs.dueDate ? toolArgs.dueDate : undefined
      };
      const createInvoiceResponse = await client.accountingApi.createInvoices(
        tenantId,
        { invoices: [newInvoice] }
      );
      result = createInvoiceResponse.body.invoices?.[0];
      break;

    case 'create_payment':
      const newPayment = {
        invoice: { invoiceID: toolArgs.invoiceId },
        account: { accountID: toolArgs.accountId },
        amount: toolArgs.amount,
        date: toolArgs.date ? toolArgs.date : new Date().toISOString().split('T')[0]
      };
      const createPaymentResponse = await client.accountingApi.createPayments(
        tenantId,
        { payments: [newPayment] }
      );
      result = createPaymentResponse.body.payments?.[0];
      break;

    case 'create_quote':
      const newQuote = {
        contact: { contactID: toolArgs.contactId },
        lineItems: toolArgs.lineItems.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount
        })),
        expiryDate: toolArgs.expiryDate ? toolArgs.expiryDate : undefined
      };
      const createQuoteResponse = await client.accountingApi.createQuotes(
        tenantId,
        { quotes: [newQuote] }
      );
      result = createQuoteResponse.body.quotes?.[0];
      break;

    case 'create_credit_note':
      const newCreditNote = {
        type: toolArgs.type,
        contact: { contactID: toolArgs.contactId },
        lineItems: toolArgs.lineItems.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount,
          accountCode: item.accountCode
        }))
      };
      const createCreditNoteResponse = await client.accountingApi.createCreditNotes(
        tenantId,
        { creditNotes: [newCreditNote] }
      );
      result = createCreditNoteResponse.body.creditNotes?.[0];
      break;

    // Write Operations - Update
    case 'update_contact':
      const updateContact = {
        contactID: toolArgs.contactId,
        name: toolArgs.name,
        emailAddress: toolArgs.email,
        phones: toolArgs.phone ? [{ phoneType: 'DEFAULT' as any, phoneNumber: toolArgs.phone }] : undefined
      };
      const updateContactResponse = await client.accountingApi.updateContact(
        tenantId,
        toolArgs.contactId,
        { contacts: [updateContact] }
      );
      result = updateContactResponse.body.contacts?.[0];
      break;

    case 'update_invoice':
      const updateInvoice = {
        invoiceID: toolArgs.invoiceId,
        lineItems: toolArgs.lineItems?.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount,
          accountCode: item.accountCode
        })),
        dueDate: toolArgs.dueDate ? toolArgs.dueDate : undefined
      };
      const updateInvoiceResponse = await client.accountingApi.updateInvoice(
        tenantId,
        toolArgs.invoiceId,
        { invoices: [updateInvoice] }
      );
      result = updateInvoiceResponse.body.invoices?.[0];
      break;

    case 'update_quote':
      const updateQuote = {
        quoteID: toolArgs.quoteId,
        lineItems: toolArgs.lineItems?.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount
        })),
        expiryDate: toolArgs.expiryDate ? toolArgs.expiryDate : undefined
      };
      const updateQuoteResponse = await client.accountingApi.updateQuote(
        tenantId,
        toolArgs.quoteId,
        { quotes: [updateQuote] }
      );
      result = updateQuoteResponse.body.quotes?.[0];
      break;

    case 'update_credit_note':
      const updateCreditNote = {
        creditNoteID: toolArgs.creditNoteId,
        lineItems: toolArgs.lineItems?.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount,
          accountCode: item.accountCode
        }))
      };
      const updateCreditNoteResponse = await client.accountingApi.updateCreditNote(
        tenantId,
        toolArgs.creditNoteId,
        { creditNotes: [updateCreditNote] }
      );
      result = updateCreditNoteResponse.body.creditNotes?.[0];
      break;

    // Payroll Write Operations
    case 'create_payroll_timesheet':
      const newTimesheet = {
        employeeID: toolArgs.employeeId,
        startDate: toolArgs.startDate,
        endDate: toolArgs.endDate
      };
      const createTimesheetResponse = await client.payrollAUApi.createTimesheet(
        tenantId,
        [newTimesheet] // Pass array directly, not wrapped in object
      );
      result = createTimesheetResponse.body.timesheets?.[0];
      break;

    case 'update_payroll_timesheet_line':
    case 'add_payroll_timesheet_line':
    case 'approve_payroll_timesheet':
    case 'revert_payroll_timesheet':
    case 'delete_payroll_timesheet':
      // These timesheet operations are not available in the current Xero PayrollAU API
      result = { error: 'This timesheet operation is not currently supported by the Xero API' };
      break;

    default:
      throw new Error(`Unknown tool: ${toolName}`);
  }

  return result;
}

/**
 * Initialize MCP session
 */
router.post('/init', async (req: Request, res: Response) => {
  try {
    const userId = req.session?.userInfo?.email || req.session?.userInfo?.sub || 'anonymous';
    const sessionId = randomUUID();

    console.log(`Initializing MCP session ${sessionId} for user ${userId}`);

    // Check if we have Xero authentication
    if (!req.session?.tokenSet) {
      return res.status(401).json({
        error: 'Xero authentication required',
        message: 'Please authenticate with Xero first'
      });
    }

    // Check if Anthropic API key is configured
    if (!process.env.ANTHROPIC_API_KEY) {
      return res.status(500).json({
        error: 'Anthropic API not configured',
        message: 'Please set ANTHROPIC_API_KEY environment variable'
      });
    }

    // Store session with empty message history
    const session: MCPSession = {
      id: sessionId,
      userId,
      lastActivity: new Date(),
      messages: []
    };
    sessions.set(sessionId, session);

    res.json({
      sessionId,
      tools: AVAILABLE_TOOLS,
      status: 'connected'
    });

  } catch (error) {
    console.error('Error initializing MCP session:', error);
    res.status(500).json({
      error: 'Failed to initialize MCP session',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Send message to Claude and get response
 */
router.post('/chat', async (req: Request, res: Response) => {
  try {
    const { sessionId, message } = req.body;

    const session = sessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Update last activity
    session.lastActivity = new Date();

    // Add user message to history
    session.messages.push({ role: 'user', content: message });

    console.log(`Processing message for session ${sessionId}: ${message}`);

    try {
      // Create the message with tools
      const response = await anthropic.messages.create({
        model: 'claude-3-5-sonnet-********',
        max_tokens: 4096,
        temperature: 0,
        system: `You are a helpful assistant integrated with Xero accounting software. You can help users access their financial data through the available tools. Be concise and helpful in your responses. Format data in a clear, readable way.`,
        messages: session.messages,
        tools: AVAILABLE_TOOLS as any,
      });

      // Process the response
      let assistantMessage = '';
      const toolCalls: any[] = [];

      for (const content of response.content) {
        if (content.type === 'text') {
          assistantMessage += content.text;
        } else if (content.type === 'tool_use') {
          console.log(`Claude wants to use tool: ${content.name} with args:`, content.input);

          try {
            // Execute the tool
            const toolResult = await executeTool(content.name, content.input, req);

            toolCalls.push({
              tool: content.name,
              arguments: content.input,
              result: toolResult
            });

            // Add tool use and result to message history
            session.messages.push({
              role: 'assistant',
              content: [
                {
                  type: 'text',
                  text: assistantMessage
                },
                {
                  type: 'tool_use',
                  id: content.id,
                  name: content.name,
                  input: content.input
                }
              ]
            });

            session.messages.push({
              role: 'user',
              content: [
                {
                  type: 'tool_result',
                  tool_use_id: content.id,
                  content: JSON.stringify(toolResult)
                }
              ]
            });

            // Get Claude's response after tool execution
            const toolResponse = await anthropic.messages.create({
              model: 'claude-3-5-sonnet-********',
              max_tokens: 4096,
              temperature: 0,
              system: `You are a helpful assistant integrated with Xero accounting software. You can help users access their financial data through the available tools. Be concise and helpful in your responses. Format data in a clear, readable way.`,
              messages: session.messages,
              tools: AVAILABLE_TOOLS as any,
            });

            // Extract the final response
            assistantMessage = '';
            for (const content of toolResponse.content) {
              if (content.type === 'text') {
                assistantMessage += content.text;
              }
            }

          } catch (toolError) {
            console.error(`Error executing tool ${content.name}:`, toolError);
            assistantMessage += `\n\nI encountered an error while accessing Xero: ${toolError instanceof Error ? toolError.message : 'Unknown error'}`;
          }
        }
      }

      // Add final assistant message to history
      session.messages.push({ role: 'assistant', content: assistantMessage });

      res.json({
        success: true,
        message: assistantMessage,
        toolCalls
      });

    } catch (error) {
      console.error('Error calling Anthropic API:', error);

      // Check for specific error types
      if (error instanceof Anthropic.APIError) {
        if (error.status === 401) {
          return res.status(401).json({
            error: 'Invalid Anthropic API key',
            message: 'Please check your ANTHROPIC_API_KEY environment variable'
          });
        } else if (error.status === 429) {
          return res.status(429).json({
            error: 'Rate limit exceeded',
            message: 'Too many requests to Anthropic API. Please try again later.'
          });
        }
      }

      res.status(500).json({
        error: 'Failed to process message',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

  } catch (error) {
    console.error('Error in chat endpoint:', error);
    res.status(500).json({
      error: 'Failed to process chat message',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get chat history
 */
router.get('/history/:sessionId', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    const session = sessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Update last activity
    session.lastActivity = new Date();

    // Convert messages to a simpler format for the frontend
    const history = session.messages.map(msg => ({
      role: msg.role,
      content: typeof msg.content === 'string' ? msg.content : msg.content
        .filter((c: any) => c.type === 'text')
        .map((c: any) => c.text)
        .join('')
    }));

    res.json({
      history
    });

  } catch (error) {
    console.error('Error getting chat history:', error);
    res.status(500).json({
      error: 'Failed to get chat history',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Close MCP session
 */
router.post('/close', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.body;

    sessions.delete(sessionId);

    res.json({
      success: true,
      message: 'Session closed'
    });

  } catch (error) {
    console.error('Error closing session:', error);
    res.status(500).json({
      error: 'Failed to close session',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Health check
 */
router.get('/health', (req: Request, res: Response) => {
  res.json({
    status: 'ok',
    activeSessions: sessions.size,
    anthropicConfigured: !!process.env.ANTHROPIC_API_KEY
  });
});

export default router;
