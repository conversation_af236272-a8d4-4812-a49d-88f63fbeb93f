/**
 * Base Repository
 * 
 * This class provides common functionality for all repositories, including:
 * - Database connection management
 * - Transaction handling
 * - Error handling
 * - Common CRUD operations
 * - Utility methods for SQL query building
 */

// Using any type for Database to avoid TypeScript namespace issues
import db from '../services/db-service';

/**
 * Base entity interface with common properties
 */
export interface BaseEntity {
  id: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  deletedAt?: string;
}

/**
 * Transaction callback function type
 */
export type TransactionCallback<T> = () => T;

/**
 * Base repository class with common functionality
 */
export class BaseRepository {
  protected db: any;
  protected tableName: string;

  /**
   * Constructor
   * @param tableName The name of the table this repository manages
   */
  constructor(tableName: string) {
    this.db = db;
    this.tableName = tableName;
  }

  /**
   * Execute a function within a transaction
   * @param callback Function to execute within the transaction
   * @returns Result of the callback function
   */
  protected transaction<T>(callback: TransactionCallback<T>): T {
    try {
      this.db.prepare('BEGIN TRANSACTION').run();
      const result = callback();
      this.db.prepare('COMMIT').run();
      return result;
    } catch (error) {
      this.db.prepare('ROLLBACK').run();
      console.error(`Transaction failed for ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Execute a query with error handling
   * @param statement SQL statement to execute
   * @param params Parameters for the statement
   * @returns Result of the query
   */
  protected executeQuery<T>(statement: any, params?: any): T {
    try {
      return statement.run(params) as unknown as T;
    } catch (error) {
      console.error(`Query execution failed for ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get all records from the table
   * @param whereClause Optional WHERE clause
   * @param params Parameters for the WHERE clause
   * @returns Array of records
   */
  protected getAll<T>(whereClause?: string, params?: any[]): T[] {
    try {
      let query = `SELECT * FROM ${this.tableName}`;
      
      if (whereClause) {
        query += ` WHERE ${whereClause}`;
      }
      
      const statement = this.db.prepare(query);
      
      if (params) {
        return statement.all(...params) as T[];
      }
      
      return statement.all() as T[];
    } catch (error) {
      console.error(`Failed to get records from ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get a record by ID
   * @param id Record ID
   * @returns Record or null if not found
   */
  protected getById<T>(id: string): T | null {
    try {
      const query = `SELECT * FROM ${this.tableName} WHERE id = ?`;
      const statement = this.db.prepare(query);
      return statement.get(id) as T || null;
    } catch (error) {
      console.error(`Failed to get record by ID from ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Delete a record by ID
   * @param id Record ID
   * @param softDelete Whether to perform a soft delete (default: true)
   * @returns Number of affected rows
   */
  protected deleteById(id: string, softDelete: boolean = true): number {
    try {
      let query: string;
      
      if (softDelete) {
        query = `UPDATE ${this.tableName} SET deleted_at = ? WHERE id = ?`;
        const now = new Date().toISOString();
        return this.db.prepare(query).run(now, id).changes;
      } else {
        query = `DELETE FROM ${this.tableName} WHERE id = ?`;
        return this.db.prepare(query).run(id).changes;
      }
    } catch (error) {
      console.error(`Failed to delete record from ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Check if a column exists in the table
   * @param columnName Column name to check
   * @returns Boolean indicating if the column exists
   */
  protected columnExists(columnName: string): boolean {
    try {
      const columnInfo = this.db.prepare(`PRAGMA table_info(${this.tableName})`).all();
      const columns = columnInfo.map((col: any) => col.name);
      return columns.includes(columnName);
    } catch (error) {
      console.error(`Failed to check if column exists in ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get all columns in the table
   * @returns Array of column names
   */
  protected getColumns(): string[] {
    try {
      const columnInfo = this.db.prepare(`PRAGMA table_info(${this.tableName})`).all();
      return columnInfo.map((col: any) => col.name);
    } catch (error) {
      console.error(`Failed to get columns for ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Build a select clause based on available columns
   * @param columnMappings Mapping of database column names to property names
   * @returns Select clause string
   */
  protected buildSelectClause(columnMappings: Record<string, string>): string {
    const columns = this.getColumns();
    
    return columns.map(col => {
      if (columnMappings[col]) {
        return `${col} as ${columnMappings[col]}`;
      }
      return col;
    }).join(', ');
  }
}
