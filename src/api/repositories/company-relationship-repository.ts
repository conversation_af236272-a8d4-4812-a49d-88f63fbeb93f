/**
 * Repository for managing company relationships
 */

// Using any type for Database to avoid TypeScript namespace issues
import { v4 as uuidv4 } from 'uuid';

// Define relationship types
export enum CompanyRelationshipType {
  PARENT = 'parent',
  SUBSIDIARY = 'subsidiary',
  PARTNER = 'partner',
  ACQUISITION = 'acquisition'
}

// Define the company relationship interface
export interface CompanyRelationship {
  parent_company_id: string;
  child_company_id: string;
  relationship_type: CompanyRelationshipType;
  created_at: string;
  created_by: string;
}

// Define the repository class
export class CompanyRelationshipRepository {
  private db: any;

  constructor(db: any) {
    this.db = db;
  }

  /**
   * Create a new company relationship
   * @param parentCompanyId The parent company ID
   * @param childCompanyId The child company ID
   * @param relationshipType The type of relationship
   * @param createdBy The user who created the relationship
   * @returns The created relationship
   */
  createRelationship(
    parentCompanyId: string,
    childCompanyId: string,
    relationshipType: CompanyRelationshipType,
    createdBy: string
  ): CompanyRelationship {
    // Verify that both companies exist
    const parentExists = this.db.prepare('SELECT id FROM company WHERE id = ?').get(parentCompanyId);
    const childExists = this.db.prepare('SELECT id FROM company WHERE id = ?').get(childCompanyId);

    if (!parentExists) {
      throw new Error(`Parent company with ID ${parentCompanyId} not found`);
    }

    if (!childExists) {
      throw new Error(`Child company with ID ${childCompanyId} not found`);
    }

    // Check if the relationship already exists
    const existingRelationship = this.db.prepare(
      'SELECT * FROM company_relationship WHERE parent_company_id = ? AND child_company_id = ?'
    ).get(parentCompanyId, childCompanyId);

    if (existingRelationship) {
      throw new Error('This relationship already exists');
    }

    // Prevent circular relationships (check if child is a parent of the parent)
    const circularRelationship = this.db.prepare(
      'SELECT * FROM company_relationship WHERE parent_company_id = ? AND child_company_id = ?'
    ).get(childCompanyId, parentCompanyId);

    if (circularRelationship) {
      throw new Error('Circular relationship detected: this would create a loop in the company hierarchy');
    }

    const now = new Date().toISOString();

    const relationship: CompanyRelationship = {
      parent_company_id: parentCompanyId,
      child_company_id: childCompanyId,
      relationship_type: relationshipType,
      created_at: now,
      created_by: createdBy
    };

    this.db.prepare(`
      INSERT INTO company_relationship (
        parent_company_id,
        child_company_id,
        relationship_type,
        created_at,
        created_by
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      relationship.parent_company_id,
      relationship.child_company_id,
      relationship.relationship_type,
      relationship.created_at,
      relationship.created_by
    );

    return relationship;
  }

  /**
   * Delete a company relationship
   * @param parentCompanyId The parent company ID
   * @param childCompanyId The child company ID
   * @returns Success flag
   */
  deleteRelationship(parentCompanyId: string, childCompanyId: string): boolean {
    const result = this.db.prepare(`
      DELETE FROM company_relationship
      WHERE parent_company_id = ? AND child_company_id = ?
    `).run(parentCompanyId, childCompanyId);

    return result.changes > 0;
  }

  /**
   * Get all relationships for a company (both as parent and child)
   * @param companyId The company ID
   * @returns Array of relationships
   */
  getCompanyRelationships(companyId: string): {
    asParent: CompanyRelationship[];
    asChild: CompanyRelationship[];
  } {
    const asParent = this.db.prepare(`
      SELECT cr.*, c.name as child_company_name
      FROM company_relationship cr
      JOIN company c ON c.id = cr.child_company_id
      WHERE cr.parent_company_id = ?
      ORDER BY c.name
    `).all(companyId);

    const asChild = this.db.prepare(`
      SELECT cr.*, c.name as parent_company_name
      FROM company_relationship cr
      JOIN company c ON c.id = cr.parent_company_id
      WHERE cr.child_company_id = ?
      ORDER BY c.name
    `).all(companyId);

    return {
      asParent,
      asChild
    };
  }

  /**
   * Get all child companies for a parent company
   * @param parentCompanyId The parent company ID
   * @returns Array of child companies with relationship details
   */
  getChildCompanies(parentCompanyId: string): Array<any> {
    return this.db.prepare(`
      SELECT c.*, cr.relationship_type
      FROM company c
      JOIN company_relationship cr ON c.id = cr.child_company_id
      WHERE cr.parent_company_id = ?
      ORDER BY c.name
    `).all(parentCompanyId);
  }

  /**
   * Get all parent companies for a child company
   * @param childCompanyId The child company ID
   * @returns Array of parent companies with relationship details
   */
  getParentCompanies(childCompanyId: string): Array<any> {
    return this.db.prepare(`
      SELECT c.*, cr.relationship_type
      FROM company c
      JOIN company_relationship cr ON c.id = cr.parent_company_id
      WHERE cr.child_company_id = ?
      ORDER BY c.name
    `).all(childCompanyId);
  }

  /**
   * Get a company's full corporate family tree (all related companies)
   * @param companyId The company ID
   * @param depth (Optional) Maximum depth to traverse the tree (defaults to 5)
   * @returns Object with full hierarchy
   */
  getCompanyFamilyTree(companyId: string, depth: number = 5): any {
    // First get the company details
    const company = this.db.prepare('SELECT * FROM company WHERE id = ?').get(companyId);
    
    if (!company) {
      throw new Error(`Company with ID ${companyId} not found`);
    }

    // Base case: if depth is 0 or less, return just the company without relationships
    if (depth <= 0) {
      return {
        ...company,
        parents: [],
        children: []
      };
    }

    // Get direct children
    const children = this.getChildCompanies(companyId).map(child => 
      this.getCompanyFamilyTree(child.id, depth - 1)
    );

    // Get direct parents
    const parents = this.getParentCompanies(companyId).map(parent => 
      this.getCompanyFamilyTree(parent.id, depth - 1)
    );

    // Return the full tree
    return {
      ...company,
      parents,
      children
    };
  }

  /**
   * Update a company relationship type
   * @param parentCompanyId The parent company ID
   * @param childCompanyId The child company ID
   * @param newRelationshipType The new relationship type
   * @returns Success flag
   */
  updateRelationshipType(
    parentCompanyId: string,
    childCompanyId: string,
    newRelationshipType: CompanyRelationshipType
  ): boolean {
    const result = this.db.prepare(`
      UPDATE company_relationship
      SET relationship_type = ?
      WHERE parent_company_id = ? AND child_company_id = ?
    `).run(newRelationshipType, parentCompanyId, childCompanyId);

    return result.changes > 0;
  }
}

// Export a factory function to create the repository
export function createCompanyRelationshipRepository(db: any): CompanyRelationshipRepository {
  return new CompanyRelationshipRepository(db);
}