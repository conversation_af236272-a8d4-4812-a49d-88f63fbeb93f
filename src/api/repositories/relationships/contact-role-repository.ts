/**
 * Repository for managing contact roles in deals
 *
 * This repository manages the contact_role table which stores the relationships
 * between contacts and deals, including the role of each contact in a deal.
 */

// Using any type for Database to avoid TypeScript namespace issues
import db from '../../services/db-service';
import { BaseRepository } from '../base-repository';

// Define common contact roles in deals
export enum ContactRole {
  DECISION_MAKER = 'decision_maker',
  INFLUENCER = 'influencer',
  TECHNICAL = 'technical',
  FINANCE = 'finance',
  LEGAL = 'legal',
  PROCUREMENT = 'procurement',
  END_USER = 'end_user',
  OTHER = 'other'
}

// Define the contact role interface
export interface ContactRoleRelationship {
  deal_id: string;
  contact_id: string;
  role: string;
  created_at: string;
  created_by: string;
}

/**
 * Repository for managing contact roles in deals
 */
export class ContactRoleRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('contact_role');
  }

  /**
   * Add a contact to a deal with a specific role
   * @param dealId The deal ID
   * @param contactId The contact ID
   * @param role The role of the contact in the deal
   * @param createdBy The user who created the association
   * @returns The created association
   */
  addContactToDeal(
    dealId: string,
    contactId: string,
    role: string,
    createdBy: string = 'system'
  ): ContactRoleRelationship {
    // Check if the contact is already associated with this deal
    const existingRole = this.db.prepare(
      'SELECT * FROM contact_role WHERE deal_id = ? AND contact_id = ?'
    ).get(dealId, contactId);

    if (existingRole) {
      // Contact already exists in this deal, update the role
      return this.updateContactRole(dealId, contactId, role);
    }

    const now = new Date().toISOString();

    const contactRole: ContactRoleRelationship = {
      deal_id: dealId,
      contact_id: contactId,
      role,
      created_at: now,
      created_by: createdBy
    };

    this.db.prepare(`
      INSERT INTO contact_role (
        deal_id,
        contact_id,
        role,
        created_at,
        created_by
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      contactRole.deal_id,
      contactRole.contact_id,
      contactRole.role,
      contactRole.created_at,
      contactRole.created_by
    );

    return contactRole;
  }

  /**
   * Add multiple contacts to a deal with the same role
   * @param dealId The deal ID
   * @param contactIds Array of contact IDs
   * @param role The role for all contacts
   * @param createdBy The user who created the associations
   * @returns Array of created associations
   */
  addContactsToDeal(
    dealId: string,
    contactIds: string[],
    role: string,
    createdBy: string = 'system'
  ): ContactRoleRelationship[] {
    return this.transaction(() => {
      return contactIds.map(contactId =>
        this.addContactToDeal(dealId, contactId, role, createdBy)
      );
    });
  }

  /**
   * Update a contact's role in a deal
   * @param dealId The deal ID
   * @param contactId The contact ID
   * @param role The new role
   * @returns The updated role association
   */
  updateContactRole(dealId: string, contactId: string, role: string): ContactRoleRelationship {
    const existingRole = this.db.prepare(
      'SELECT * FROM contact_role WHERE deal_id = ? AND contact_id = ?'
    ).get(dealId, contactId);

    if (!existingRole) {
      throw new Error('This contact is not associated with this deal');
    }

    this.db.prepare(`
      UPDATE contact_role
      SET role = ?
      WHERE deal_id = ? AND contact_id = ?
    `).run(role, dealId, contactId);

    return {
      ...existingRole,
      role
    };
  }

  /**
   * Get all contacts for a deal
   * @param dealId The deal ID
   * @returns Array of contacts with their roles
   */
  getDealContacts(dealId: string): Array<any> {
    return this.db.prepare(`
      SELECT c.*, cr.role
      FROM contact c
      JOIN contact_role cr ON c.id = cr.contact_id
      WHERE cr.deal_id = ? AND c.deleted_at IS NULL
      ORDER BY c.first_name, c.last_name
    `).all(dealId);
  }

  /**
   * Get contacts with a specific role in a deal
   * @param dealId The deal ID
   * @param role The role to filter by
   * @returns Array of contacts with the specified role
   */
  getDealContactsByRole(dealId: string, role: string): Array<any> {
    return this.db.prepare(`
      SELECT c.*, cr.role
      FROM contact c
      JOIN contact_role cr ON c.id = cr.contact_id
      WHERE cr.deal_id = ? AND cr.role = ? AND c.deleted_at IS NULL
      ORDER BY c.first_name, c.last_name
    `).all(dealId, role);
  }

  /**
   * Get all deals for a contact
   * @param contactId The contact ID
   * @returns Array of deals with the contact's role
   */
  getContactDeals(contactId: string): Array<any> {
    return this.db.prepare(`
      SELECT d.*, cr.role
      FROM deal d
      JOIN contact_role cr ON d.id = cr.deal_id
      WHERE cr.contact_id = ? AND d.deleted_at IS NULL
      ORDER BY d.updated_at DESC
    `).all(contactId);
  }

  /**
   * Get the role of a contact in a deal
   * @param dealId The deal ID
   * @param contactId The contact ID
   * @returns The role or null if not found
   */
  getContactRoleInDeal(dealId: string, contactId: string): string | null {
    const result = this.db.prepare(`
      SELECT role FROM contact_role
      WHERE deal_id = ? AND contact_id = ?
    `).get(dealId, contactId) as { role: string } | undefined;

    return result ? result.role : null;
  }

  /**
   * Check if a contact is associated with a deal
   * @param dealId The deal ID
   * @param contactId The contact ID
   * @returns Boolean indicating if the association exists
   */
  isContactInDeal(dealId: string, contactId: string): boolean {
    const result = this.db.prepare(`
      SELECT 1 FROM contact_role
      WHERE deal_id = ? AND contact_id = ?
    `).get(dealId, contactId);

    return !!result;
  }

  /**
   * Remove a contact from a deal
   * @param dealId The deal ID
   * @param contactId The contact ID
   * @returns Boolean indicating success
   */
  removeContactFromDeal(dealId: string, contactId: string): boolean {
    try {
      const result = this.db.prepare(`
        DELETE FROM contact_role
        WHERE deal_id = ? AND contact_id = ?
      `).run(dealId, contactId);

      return result.changes > 0;
    } catch (error) {
      console.error('Error removing contact from deal:', error);
      throw error;
    }
  }
}
