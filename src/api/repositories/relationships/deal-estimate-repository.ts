/**
 * Repository for managing deal-estimate relationships
 */

import { BaseRepository } from '../base-repository';
import { v4 as uuidv4 } from 'uuid';

/**
 * Deal-estimate relationship interface
 */
export interface DealEstimateRelationship {
  id: string;
  deal_id: string;
  estimate_id: string;
  estimate_type: string;
  linked_at: string;
  linked_by: string;
}

/**
 * Estimate types
 */
export enum EstimateType {
  INTERNAL = 'internal',
  HARVEST = 'harvest'
}

/**
 * Repository for managing deal-estimate relationships
 */
export class DealEstimateRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('deal_estimate');
  }

  /**
   * Link a deal to an estimate
   * @param dealId Deal ID
   * @param estimateId Estimate ID
   * @param estimateType Type of estimate ('internal' or 'harvest')
   * @param linkedBy User who created the relationship
   * @returns Created relationship
   */
  linkDealToEstimate(
    dealId: string,
    estimateId: string,
    estimateType: string = EstimateType.INTERNAL,
    linkedBy: string = 'system'
  ): DealEstimateRelationship {
    try {
      // Check if the relationship already exists
      const existingRelationship = this.db.prepare(`
        SELECT * FROM deal_estimate
        WHERE deal_id = ? AND estimate_id = ?
      `).get(dealId, estimateId);

      if (existingRelationship) {
        // Update the estimate type if it exists
        this.db.prepare(`
          UPDATE deal_estimate
          SET estimate_type = ?
          WHERE deal_id = ? AND estimate_id = ?
        `).run(estimateType, dealId, estimateId);

        return {
          ...existingRelationship,
          estimate_type: estimateType
        };
      }

      const now = new Date().toISOString();

      this.db.prepare(`
        INSERT INTO deal_estimate (
          deal_id,
          estimate_id,
          estimate_type,
          linked_at,
          linked_by
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        dealId,
        estimateId,
        estimateType,
        now,
        linkedBy
      );

      // Return the relationship without an id since the table uses composite primary key
      const relationship: DealEstimateRelationship = {
        id: `${dealId}_${estimateId}_${estimateType}`, // Composite key for interface compatibility
        deal_id: dealId,
        estimate_id: estimateId,
        estimate_type: estimateType,
        linked_at: now,
        linked_by: linkedBy
      };

      return relationship;
    } catch (error) {
      console.error('Error linking deal to estimate:', error);
      throw error;
    }
  }

  /**
   * Unlink a deal from an estimate
   * @param dealId Deal ID
   * @param estimateId Estimate ID
   * @returns Boolean indicating success
   */
  unlinkDealFromEstimate(dealId: string, estimateId: string): boolean {
    try {
      const result = this.db.prepare(`
        DELETE FROM deal_estimate
        WHERE deal_id = ? AND estimate_id = ?
      `).run(dealId, estimateId);

      return result.changes > 0;
    } catch (error) {
      console.error('Error unlinking deal from estimate:', error);
      throw error;
    }
  }

  /**
   * Get all estimates for a deal
   * @param dealId Deal ID
   * @returns Array of estimate IDs with estimate types and linking information
   */
  getEstimatesForDeal(dealId: string): Array<{ estimateId: string; estimateType: string; linkedAt: string; linkedBy: string }> {
    try {
      const estimates = this.db.prepare(`
        SELECT
          estimate_id as estimateId,
          estimate_type as estimateType,
          linked_at as linkedAt,
          linked_by as linkedBy
        FROM deal_estimate
        WHERE deal_id = ?
      `).all(dealId);

      return estimates;
    } catch (error) {
      console.error('Error getting estimates for deal:', error);
      return [];
    }
  }

  /**
   * Get internal estimates for a deal
   * @param dealId Deal ID
   * @returns Array of estimate IDs
   */
  getInternalEstimatesForDeal(dealId: string): string[] {
    try {
      const estimates = this.db.prepare(`
        SELECT estimate_id as estimateId
        FROM deal_estimate
        WHERE deal_id = ? AND estimate_type = ?
      `).all(dealId, EstimateType.INTERNAL);

      return estimates.map(e => e.estimateId);
    } catch (error) {
      console.error('Error getting internal estimates for deal:', error);
      return [];
    }
  }

  /**
   * Get harvest estimates for a deal
   * @param dealId Deal ID
   * @returns Array of estimate IDs
   */
  getHarvestEstimatesForDeal(dealId: string): string[] {
    try {
      const estimates = this.db.prepare(`
        SELECT estimate_id as estimateId
        FROM deal_estimate
        WHERE deal_id = ? AND estimate_type = ?
      `).all(dealId, EstimateType.HARVEST);

      return estimates.map(e => e.estimateId);
    } catch (error) {
      console.error('Error getting harvest estimates for deal:', error);
      return [];
    }
  }

  /**
   * Get all deals for an estimate
   * @param estimateId Estimate ID
   * @returns Array of deal IDs with estimate types
   */
  getDealsForEstimate(estimateId: string): Array<{ dealId: string; estimateType: string }> {
    try {
      const deals = this.db.prepare(`
        SELECT deal_id as dealId, estimate_type as estimateType
        FROM deal_estimate
        WHERE estimate_id = ?
      `).all(estimateId);

      return deals;
    } catch (error) {
      console.error('Error getting deals for estimate:', error);
      return [];
    }
  }

  /**
   * Check if a deal is linked to an estimate
   * @param dealId Deal ID
   * @param estimateId Estimate ID
   * @returns Boolean indicating if the link exists
   */
  isDealLinkedToEstimate(dealId: string, estimateId: string): boolean {
    try {
      const result = this.db.prepare(`
        SELECT 1
        FROM deal_estimate
        WHERE deal_id = ? AND estimate_id = ?
      `).get(dealId, estimateId);

      return !!result;
    } catch (error) {
      console.error('Error checking if deal is linked to estimate:', error);
      return false;
    }
  }
}
