/**
 * Enhanced Repository
 *
 * This file provides enhanced functionality by combining multiple repositories
 * to provide a unified interface for the enhanced CRM routes.
 */

import { DealRepository } from './deal-repository';
import { CompanyRepository } from './company-repository';
import { ContactRepository } from './contact-repository';
import { NoteRepository } from './note-repository';
import { ContactRoleRepository } from './relationships/contact-role-repository';
import { DealEstimateRepository } from './relationships/deal-estimate-repository';
import db from '../services/db-service';
import { v4 as uuidv4 } from 'uuid';

import {
  Deal,
  Contact,
  DealUpdate,
  ContactCreate,
  ContactUpdate,
  ContactCompanyAssociation,
  CompanyRelationshipCreate,
  DealContactRole,
  DealPrimaryCompany
} from '../../frontend/types/crm-types';

import {
  Company,
  CompanyCreate,
  CompanyUpdate
} from '../../types/company-types';

/**
 * Enhanced Repository class that combines functionality from multiple repositories
 */
class EnhancedRepository {
  private dealRepository: DealRepository;
  private companyRepository: CompanyRepository;
  private contactRepository: ContactRepository;
  private noteRepository: NoteRepository;
  private contactRoleRepository: ContactRoleRepository;
  private dealEstimateRepository: DealEstimateRepository;

  constructor() {
    this.dealRepository = new DealRepository();
    this.companyRepository = new CompanyRepository();
    this.contactRepository = new ContactRepository();
    this.noteRepository = new NoteRepository();
    this.contactRoleRepository = new ContactRoleRepository();
    this.dealEstimateRepository = new DealEstimateRepository();
  }

  /**
   * Get all contacts with enhanced fields and relationships
   * @param includeDeleted Whether to include soft-deleted contacts
   * @returns Array of contacts with enhanced fields
   */
  getAllContacts(includeDeleted: boolean = false): Contact[] {
    try {
      const contacts = this.contactRepository.getAllContacts();

      // Filter out deleted contacts if not requested
      const filteredContacts = includeDeleted
        ? contacts
        : contacts.filter(contact => !contact.deletedAt);

      // Enhance each contact with additional data
      return filteredContacts.map(contact => this.enhanceContact(contact));
    } catch (error) {
      console.error('Error getting all contacts with enhanced fields:', error);
      return [];
    }
  }

  /**
   * Get all soft-deleted contacts
   * @returns Array of soft-deleted contacts
   */
  getDeletedContacts(): Contact[] {
    try {
      const contacts = this.contactRepository.getAllContacts();

      // Filter to only include deleted contacts
      const deletedContacts = contacts.filter(contact => contact.deletedAt);

      // Enhance each contact with additional data
      return deletedContacts.map(contact => this.enhanceContact(contact));
    } catch (error) {
      console.error('Error getting deleted contacts:', error);
      return [];
    }
  }

  /**
   * Get a contact by ID with enhanced fields and relationships
   * @param id Contact ID
   * @param includeDeleted Whether to include soft-deleted contacts
   * @returns Enhanced contact or null if not found
   */
  getContactById(id: string, includeDeleted: boolean = false): Contact | null {
    try {
      const contact = this.contactRepository.getContactById(id);

      if (!contact) return null;

      // Check if contact is deleted and should be excluded
      if (!includeDeleted && contact.deletedAt) return null;

      // Enhance the contact with additional data
      return this.enhanceContact(contact);
    } catch (error) {
      console.error(`Error getting contact ${id} with enhanced fields:`, error);
      return null;
    }
  }

  /**
   * Create a new contact with enhanced fields and relationships
   * @param contactData Contact data
   * @param createdBy User who created the contact
   * @returns Created contact with enhanced fields
   */
  createContact(contactData: ContactCreate, createdBy: string = 'system'): Contact | null {
    try {
      // Set the created by field
      contactData.createdBy = createdBy;
      contactData.updatedBy = createdBy;

      // Create the contact
      const contact = this.contactRepository.createContact(contactData);

      if (!contact) return null;

      // If company associations are provided, create them
      if (contactData.companyAssociations && contactData.companyAssociations.length > 0) {
        for (const assoc of contactData.companyAssociations) {
          this.associateContactWithCompany(
            contact.id,
            assoc.companyId,
            assoc.role || 'employee',
            assoc.isPrimary || false,
            createdBy
          );
        }
      }

      // Enhance the contact with additional data
      return this.enhanceContact(contact);
    } catch (error) {
      console.error('Error creating contact with enhanced fields:', error);
      return null;
    }
  }

  /**
   * Update a contact with enhanced fields
   * @param id Contact ID
   * @param contactData Contact data to update
   * @param updatedBy User who updated the contact
   * @returns Updated contact with enhanced fields
   */
  updateContact(id: string, contactData: ContactUpdate, updatedBy: string = 'system'): Contact | null {
    try {
      // Set the updated by field
      contactData.updatedBy = updatedBy;

      // Update the contact
      const contact = this.contactRepository.updateContact(id, contactData);

      if (!contact) return null;

      // If company associations are provided, update them
      if (contactData.companyAssociations && contactData.companyAssociations.length > 0) {
        // First, get existing associations
        const existingAssociations = this.getContactCompanyRelationships(id);

        // Process each new association
        for (const assoc of contactData.companyAssociations) {
          const existing = existingAssociations.find(ea => ea.companyId === assoc.companyId);

          if (existing) {
            // Update existing association
            this.updateContactCompanyRelationship(
              id,
              assoc.companyId,
              {
                role: assoc.role,
                isPrimary: assoc.isPrimary
              },
              updatedBy
            );
          } else {
            // Create new association
            this.associateContactWithCompany(
              id,
              assoc.companyId,
              assoc.role || 'employee',
              assoc.isPrimary || false,
              updatedBy
            );
          }
        }
      }

      // Enhance the contact with additional data
      return this.enhanceContact(contact);
    } catch (error) {
      console.error(`Error updating contact ${id} with enhanced fields:`, error);
      return null;
    }
  }

  /**
   * Soft delete a contact
   * @param id Contact ID
   * @param deletedBy User who deleted the contact
   * @returns Boolean indicating success
   */
  softDeleteContact(id: string, deletedBy: string = 'system'): boolean {
    try {
      return this.contactRepository.deleteContact(id, true);
    } catch (error) {
      console.error(`Error soft deleting contact ${id}:`, error);
      return false;
    }
  }

  /**
   * Permanently delete a contact
   * @param id Contact ID
   * @returns Boolean indicating success
   */
  permanentlyDeleteContact(id: string): boolean {
    try {
      return this.contactRepository.deleteContact(id, false);
    } catch (error) {
      console.error(`Error permanently deleting contact ${id}:`, error);
      return false;
    }
  }

  /**
   * Restore a soft-deleted contact
   * @param id Contact ID
   * @param restoredBy User who restored the contact
   * @returns Boolean indicating success
   */
  restoreContact(id: string, restoredBy: string = 'system'): boolean {
    try {
      // Check if contact exists and is deleted
      const contact = this.contactRepository.getContactById(id);
      if (!contact || !contact.deletedAt) return false;

      // Update the contact to remove the deleted_at field
      db.prepare(`
        UPDATE contact
        SET deleted_at = NULL, updated_at = ?, updated_by = ?
        WHERE id = ?
      `).run(new Date().toISOString(), restoredBy, id);

      return true;
    } catch (error) {
      console.error(`Error restoring contact ${id}:`, error);
      return false;
    }
  }

  /**
   * Get all company relationships for a contact
   * @param contactId Contact ID
   * @returns Array of company relationships
   */
  getContactCompanyRelationships(contactId: string): ContactCompanyAssociation[] {
    try {
      // Get all company relationships for the contact
      const relationships = db.prepare(`
        SELECT
          cc.contact_id as contactId,
          cc.company_id as companyId,
          cc.role,
          cc.is_primary as isPrimary,
          c.name as companyName,
          cc.created_at as createdAt,
          cc.created_by as createdBy,
          cc.updated_at as updatedAt,
          cc.updated_by as updatedBy
        FROM contact_company cc
        JOIN company c ON cc.company_id = c.id
        WHERE cc.contact_id = ?
      `).all(contactId) as ContactCompanyAssociation[];

      return relationships;
    } catch (error) {
      console.error(`Error getting company relationships for contact ${contactId}:`, error);
      return [];
    }
  }
  /**
   * Associate a contact with a company
   * @param contactId Contact ID
   * @param companyId Company ID
   * @param role Role of the contact in the company
   * @param isPrimary Whether this is the primary company for the contact
   * @param createdBy User who created the association
   * @returns Boolean indicating success
   */
  associateContactWithCompany(
    contactId: string,
    companyId: string,
    role: string = 'employee',
    isPrimary: boolean = false,
    createdBy: string = 'system'
  ): boolean {
    try {
      // Check if contact and company exist
      const contact = this.contactRepository.getContactById(contactId);
      const company = this.companyRepository.getCompanyById(companyId);

      if (!contact || !company) return false;

      const now = new Date().toISOString();

      // Check if the relationship already exists
      const existingRelationship = db.prepare(`
        SELECT 1 FROM contact_company
        WHERE contact_id = ? AND company_id = ?
      `).get(contactId, companyId);

      if (existingRelationship) {
        // Update the existing relationship
        db.prepare(`
          UPDATE contact_company
          SET role = ?, is_primary = ?, updated_at = ?, updated_by = ?
          WHERE contact_id = ? AND company_id = ?
        `).run(role, isPrimary ? 1 : 0, now, createdBy, contactId, companyId);
      } else {
        // Create a new relationship
        db.prepare(`
          INSERT INTO contact_company (
            contact_id, company_id, role, is_primary, created_at, created_by, updated_at, updated_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          contactId,
          companyId,
          role,
          isPrimary ? 1 : 0,
          now,
          createdBy,
          now,
          createdBy
        );
      }

      // If this is the primary company, update the contact's company_id
      if (isPrimary) {
        db.prepare(`
          UPDATE contact
          SET company_id = ?, updated_at = ?, updated_by = ?
          WHERE id = ?
        `).run(companyId, now, createdBy, contactId);

        // And make sure no other company is marked as primary
        db.prepare(`
          UPDATE contact_company
          SET is_primary = 0, updated_at = ?, updated_by = ?
          WHERE contact_id = ? AND company_id != ?
        `).run(now, createdBy, contactId, companyId);
      }

      return true;
    } catch (error) {
      console.error(`Error associating contact ${contactId} with company ${companyId}:`, error);
      return false;
    }
  }

  /**
   * Update a contact-company relationship
   * @param contactId Contact ID
   * @param companyId Company ID
   * @param updates Updates to apply
   * @param updatedBy User who updated the relationship
   * @returns Boolean indicating success
   */
  updateContactCompanyRelationship(
    contactId: string,
    companyId: string,
    updates: { role?: string; isPrimary?: boolean },
    updatedBy: string = 'system'
  ): boolean {
    try {
      // Check if the relationship exists
      const existingRelationship = db.prepare(`
        SELECT 1 FROM contact_company
        WHERE contact_id = ? AND company_id = ?
      `).get(contactId, companyId);

      if (!existingRelationship) return false;

      const now = new Date().toISOString();

      // Update the relationship
      if (updates.role !== undefined) {
        db.prepare(`
          UPDATE contact_company
          SET role = ?, updated_at = ?, updated_by = ?
          WHERE contact_id = ? AND company_id = ?
        `).run(updates.role, now, updatedBy, contactId, companyId);
      }

      // Handle primary status update
      if (updates.isPrimary !== undefined) {
        if (updates.isPrimary) {
          // Make this the primary company
          db.prepare(`
            UPDATE contact
            SET company_id = ?, updated_at = ?, updated_by = ?
            WHERE id = ?
          `).run(companyId, now, updatedBy, contactId);

          // Update the relationship
          db.prepare(`
            UPDATE contact_company
            SET is_primary = 1, updated_at = ?, updated_by = ?
            WHERE contact_id = ? AND company_id = ?
          `).run(now, updatedBy, contactId, companyId);

          // Make sure no other company is marked as primary
          db.prepare(`
            UPDATE contact_company
            SET is_primary = 0, updated_at = ?, updated_by = ?
            WHERE contact_id = ? AND company_id != ?
          `).run(now, updatedBy, contactId, companyId);
        } else {
          // Remove primary status
          db.prepare(`
            UPDATE contact_company
            SET is_primary = 0, updated_at = ?, updated_by = ?
            WHERE contact_id = ? AND company_id = ?
          `).run(now, updatedBy, contactId, companyId);

          // If this was the primary company, clear the contact's company_id
          const contact = this.contactRepository.getContactById(contactId);
          if (contact && contact.companyId === companyId) {
            db.prepare(`
              UPDATE contact
              SET company_id = NULL, updated_at = ?, updated_by = ?
              WHERE id = ?
            `).run(now, updatedBy, contactId);
          }
        }
      }

      return true;
    } catch (error) {
      console.error(`Error updating relationship between contact ${contactId} and company ${companyId}:`, error);
      return false;
    }
  }

  /**
   * Disassociate a contact from a company
   * @param contactId Contact ID
   * @param companyId Company ID
   * @returns Boolean indicating success
   */
  disassociateContactFromCompany(contactId: string, companyId: string): boolean {
    try {
      // Check if the relationship exists
      const existingRelationship = db.prepare(`
        SELECT is_primary FROM contact_company
        WHERE contact_id = ? AND company_id = ?
      `).get(contactId, companyId);

      if (!existingRelationship) return false;

      const now = new Date().toISOString();

      // If this was the primary company, clear the contact's company_id
      if (existingRelationship.is_primary) {
        db.prepare(`
          UPDATE contact
          SET company_id = NULL, updated_at = ?
          WHERE id = ?
        `).run(now, contactId);
      }

      // Delete the relationship
      db.prepare(`
        DELETE FROM contact_company
        WHERE contact_id = ? AND company_id = ?
      `).run(contactId, companyId);

      return true;
    } catch (error) {
      console.error(`Error disassociating contact ${contactId} from company ${companyId}:`, error);
      return false;
    }
  }

  /**
   * Set a company as the primary company for a contact
   * @param contactId Contact ID
   * @param companyId Company ID
   * @param updatedBy User who updated the relationship
   * @returns Boolean indicating success
   */
  setContactPrimaryCompany(contactId: string, companyId: string, updatedBy: string = 'system'): boolean {
    try {
      // Check if contact and company exist
      const contact = this.contactRepository.getContactById(contactId);
      const company = this.companyRepository.getCompanyById(companyId);

      if (!contact || !company) return false;

      const now = new Date().toISOString();

      // Check if the relationship exists
      const existingRelationship = db.prepare(`
        SELECT 1 FROM contact_company
        WHERE contact_id = ? AND company_id = ?
      `).get(contactId, companyId);

      if (!existingRelationship) {
        // Create the relationship if it doesn't exist
        db.prepare(`
          INSERT INTO contact_company (
            contact_id, company_id, role, is_primary, created_at, created_by, updated_at, updated_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          contactId,
          companyId,
          'employee', // Default role
          1, // Primary
          now,
          updatedBy,
          now,
          updatedBy
        );
      } else {
        // Update the existing relationship
        db.prepare(`
          UPDATE contact_company
          SET is_primary = 1, updated_at = ?, updated_by = ?
          WHERE contact_id = ? AND company_id = ?
        `).run(now, updatedBy, contactId, companyId);
      }

      // Update the contact's company_id
      db.prepare(`
        UPDATE contact
        SET company_id = ?, updated_at = ?, updated_by = ?
        WHERE id = ?
      `).run(companyId, now, updatedBy, contactId);

      // Make sure no other company is marked as primary
      db.prepare(`
        UPDATE contact_company
        SET is_primary = 0, updated_at = ?, updated_by = ?
        WHERE contact_id = ? AND company_id != ?
      `).run(now, updatedBy, contactId, companyId);

      return true;
    } catch (error) {
      console.error(`Error setting primary company ${companyId} for contact ${contactId}:`, error);
      return false;
    }
  }

  /**
   * Batch create contact-company associations
   * @param associations Array of contact-company associations
   * @param createdBy User who created the associations
   * @returns Boolean indicating success
   */
  batchCreateContactCompanyAssociations(
    associations: ContactCompanyAssociation[],
    createdBy: string = 'system'
  ): boolean {
    try {
      // Start a transaction
      db.prepare('BEGIN TRANSACTION').run();

      for (const assoc of associations) {
        this.associateContactWithCompany(
          assoc.contactId,
          assoc.companyId,
          assoc.role || 'employee',
          assoc.isPrimary || false,
          createdBy
        );
      }

      // Commit the transaction
      db.prepare('COMMIT').run();

      return true;
    } catch (error) {
      // Rollback the transaction on error
      db.prepare('ROLLBACK').run();
      console.error('Error creating batch contact-company associations:', error);
      return false;
    }
  }

  /**
   * Get a company with all its relationships
   * @param id Company ID
   * @param includeDeleted Whether to include soft-deleted entities
   * @returns Enhanced company or null if not found
   */
  getCompanyWithRelationships(id: string, includeDeleted: boolean = false): Company | null {
    try {
      const company = this.companyRepository.getCompanyById(id);

      if (!company) return null;

      // Check if company is deleted and should be excluded
      if (!includeDeleted && company.deletedAt) return null;

      // Get contacts associated with this company
      const contacts = this.contactRepository.getContactsByCompanyId(id);

      // Filter out deleted contacts if not requested
      company.contacts = includeDeleted
        ? contacts
        : contacts.filter(contact => !contact.deletedAt);

      // Get deals associated with this company
      const deals = db.prepare(`
        SELECT * FROM deal
        WHERE company_id = ? ${includeDeleted ? '' : 'AND deleted_at IS NULL'}
      `).all(id) as Deal[];

      company.deals = deals;

      // Get company relationships
      // Note: Company interface doesn't have relationships property
      // This would need to be added to the type definition

      return company;
    } catch (error) {
      console.error(`Error getting company ${id} with relationships:`, error);
      return null;
    }
  }

  /**
   * Get company relationships
   * @param companyId Company ID
   * @param includeDeleted Whether to include soft-deleted companies
   * @returns Array of company relationships
   */
  getCompanyRelationships(companyId: string, includeDeleted: boolean = false): any[] {
    try {
      // Get all relationships where this company is the parent
      const parentRelationships = db.prepare(`
        SELECT
          cr.parent_company_id as parentCompanyId,
          cr.child_company_id as childCompanyId,
          cr.relationship_type as relationshipType,
          c.name as childCompanyName,
          cr.created_at as createdAt,
          cr.created_by as createdBy,
          cr.updated_at as updatedAt,
          cr.updated_by as updatedBy
        FROM company_relationship cr
        JOIN company c ON cr.child_company_id = c.id
        WHERE cr.parent_company_id = ?
        ${includeDeleted ? '' : 'AND c.deleted_at IS NULL'}
      `).all(companyId);

      // Get all relationships where this company is the child
      const childRelationships = db.prepare(`
        SELECT
          cr.parent_company_id as parentCompanyId,
          cr.child_company_id as childCompanyId,
          cr.relationship_type as relationshipType,
          c.name as parentCompanyName,
          cr.created_at as createdAt,
          cr.created_by as createdBy,
          cr.updated_at as updatedAt,
          cr.updated_by as updatedBy
        FROM company_relationship cr
        JOIN company c ON cr.parent_company_id = c.id
        WHERE cr.child_company_id = ?
        ${includeDeleted ? '' : 'AND c.deleted_at IS NULL'}
      `).all(companyId);

      return [
        ...parentRelationships.map(r => ({ ...r, direction: 'parent' })),
        ...childRelationships.map(r => ({ ...r, direction: 'child' }))
      ];
    } catch (error) {
      console.error(`Error getting relationships for company ${companyId}:`, error);
      return [];
    }
  }

  /**
   * Get a deal by ID with enhanced relationships
   * @param id Deal ID
   * @param includeDeleted Whether to include soft-deleted entities
   * @returns Enhanced deal or null if not found
   */
  getDealByIdEnhanced(id: string, includeDeleted: boolean = false): Deal | null {
    try {
      const deal = this.dealRepository.getDealById(id);

      if (!deal) return null;

      // Check if deal is deleted and should be excluded
      if (!includeDeleted && deal.deletedAt) return null;

      // Get linked estimates
      const estimateLinks = this.dealEstimateRepository.getEstimatesForDeal(id);
      deal.estimates = estimateLinks.map(link => ({
        id: link.estimateId,
        estimateId: link.estimateId,
        estimateType: link.estimateType,
        type: link.estimateType as 'draft' | 'harvest',
        linkedAt: link.linkedAt,
        linkedBy: link.linkedBy || 'system'
      }));

      return deal;
    } catch (error) {
      console.error(`Error getting deal ${id} with enhanced relationships:`, error);
      return null;
    }
  }

  /**
   * Update a deal with enhanced fields
   * @param id Deal ID
   * @param dealData Deal data to update
   * @param updatedBy User who updated the deal
   * @returns Updated deal with enhanced fields
   */
  updateDealEnhanced(id: string, dealData: DealUpdate, updatedBy: string = 'system'): Deal | null {
    try {
      // Set the updated by field
      dealData.updatedBy = updatedBy;

      // Update the deal
      const deal = this.dealRepository.updateDeal(id, dealData);

      if (!deal) return null;

      // Get linked estimates
      const estimateLinks = this.dealEstimateRepository.getEstimatesForDeal(id);
      deal.estimates = estimateLinks.map(link => ({
        id: link.estimateId,
        estimateId: link.estimateId,
        estimateType: link.estimateType,
        type: link.estimateType as 'draft' | 'harvest',
        linkedAt: link.linkedAt,
        linkedBy: link.linkedBy || 'system'
      }));

      return deal;
    } catch (error) {
      console.error(`Error updating deal ${id} with enhanced fields:`, error);
      return null;
    }
  }

  /**
   * Restore a soft-deleted deal
   * @param id Deal ID
   * @param restoredBy User who restored the deal
   * @returns Boolean indicating success
   */
  restoreDeal(id: string, restoredBy: string = 'system'): boolean {
    try {
      // Check if deal exists and is deleted
      const deal = this.dealRepository.getDealById(id);
      if (!deal || !deal.deletedAt) return false;

      // Update the deal to remove the deleted_at field
      db.prepare(`
        UPDATE deal
        SET deleted_at = NULL, updated_at = ?, updated_by = ?
        WHERE id = ?
      `).run(new Date().toISOString(), restoredBy, id);

      return true;
    } catch (error) {
      console.error(`Error restoring deal ${id}:`, error);
      return false;
    }
  }

  /**
   * Get contacts for a specific company
   * @param companyId Company ID
   * @param includeDeleted Whether to include deleted contacts
   * @returns Array of contacts
   */
  getContactsForCompany(companyId: string, includeDeleted: boolean = false): Contact[] {
    try {
      let query = `
        SELECT
          c.id,
          c.first_name as firstName,
          c.last_name as lastName,
          c.email,
          c.phone,
          c.job_title as jobTitle,
          c.notes,
          c.hubspot_id as hubspotId,
          c.created_at as createdAt,
          c.updated_at as updatedAt,
          c.created_by as createdBy,
          c.updated_by as updatedBy,
          cc.role,
          cc.is_primary as isPrimary
        FROM contact c
        JOIN contact_company cc ON c.id = cc.contact_id
        WHERE cc.company_id = ?
      `;

      if (!includeDeleted) {
        // Note: contact table doesn't have deleted_at column in current schema
      }

      const contacts = db.prepare(query).all(companyId) as any[];

      return contacts.map(contact => ({
        ...contact,
        companyId: companyId
      }));
    } catch (error) {
      console.error(`Error getting contacts for company ${companyId}:`, error);
      return [];
    }
  }

  /**
   * Get all company relationships
   * @param companyId Company ID
   * @returns Company relationships
   */
  getAllCompanyRelationships(companyId: string): any {
    return this.getCompanyRelationships(companyId);
  }

  /**
   * Get child companies
   * @param companyId Company ID
   * @returns Array of child companies
   */
  getChildCompanies(companyId: string): Company[] {
    try {
      const childCompanies = db.prepare(`
        SELECT c.* FROM company c
        JOIN company_relationship cr ON c.id = cr.child_company_id
        WHERE cr.parent_company_id = ?
      `).all(companyId) as Company[];

      return childCompanies;
    } catch (error) {
      console.error(`Error getting child companies for ${companyId}:`, error);
      return [];
    }
  }

  /**
   * Get parent companies
   * @param companyId Company ID
   * @returns Array of parent companies
   */
  getParentCompanies(companyId: string): Company[] {
    try {
      const parentCompanies = db.prepare(`
        SELECT c.* FROM company c
        JOIN company_relationship cr ON c.id = cr.parent_company_id
        WHERE cr.child_company_id = ?
      `).all(companyId) as Company[];

      return parentCompanies;
    } catch (error) {
      console.error(`Error getting parent companies for ${companyId}:`, error);
      return [];
    }
  }

  /**
   * Create company relationship
   * @param relationshipData Relationship data
   * @returns Created relationship
   */
  createCompanyRelationship(relationshipData: CompanyRelationshipCreate): any {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      db.prepare(`
        INSERT INTO company_relationship (
          id,
          parent_company_id,
          child_company_id,
          relationship_type,
          created_at,
          created_by
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        id,
        relationshipData.parentCompanyId,
        relationshipData.childCompanyId,
        relationshipData.relationshipType,
        now,
        'system'
      );

      return {
        id,
        ...relationshipData,
        createdAt: now,
        createdBy: 'system'
      };
    } catch (error) {
      console.error('Error creating company relationship:', error);
      throw error;
    }
  }

  /**
   * Delete company relationship
   * @param parentId Parent company ID
   * @param childId Child company ID
   * @returns Success boolean
   */
  deleteCompanyRelationship(parentId: string, childId: string): boolean {
    try {
      const result = db.prepare(`
        DELETE FROM company_relationship
        WHERE parent_company_id = ? AND child_company_id = ?
      `).run(parentId, childId);

      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting company relationship:', error);
      return false;
    }
  }

  /**
   * Batch create company relationships
   * @param relationships Array of relationships to create
   * @returns Array of created relationships
   */
  batchCreateCompanyRelationships(relationships: CompanyRelationshipCreate[]): any[] {
    const created = [];
    const transaction = db.prepare('BEGIN');
    const rollback = db.prepare('ROLLBACK');
    const commit = db.prepare('COMMIT');

    transaction.run();
    try {
      for (const relationship of relationships) {
        const result = this.createCompanyRelationship(relationship);
        created.push(result);
      }
      commit.run();
      return created;
    } catch (error) {
      rollback.run();
      throw error;
    }
  }

  /**
   * Soft delete a deal
   * @param id Deal ID
   * @param deletedBy User who deleted
   * @returns Success boolean
   */
  softDeleteDeal(id: string, deletedBy: string = 'system'): boolean {
    try {
      const now = new Date().toISOString();
      const result = db.prepare(`
        UPDATE deal
        SET deleted_at = ?, updated_at = ?, updated_by = ?
        WHERE id = ?
      `).run(now, now, deletedBy, id);

      return result.changes > 0;
    } catch (error) {
      console.error(`Error soft deleting deal ${id}:`, error);
      return false;
    }
  }

  /**
   * Update contact role in deal
   * @param dealId Deal ID
   * @param contactId Contact ID
   * @param role New role
   * @returns Success boolean
   */
  updateContactRoleInDeal(dealId: string, contactId: string, role: string): boolean {
    try {
      const result = db.prepare(`
        UPDATE contact_role
        SET role = ?
        WHERE deal_id = ? AND contact_id = ?
      `).run(role, dealId, contactId);

      return result.changes > 0;
    } catch (error) {
      console.error('Error updating contact role in deal:', error);
      return false;
    }
  }

  /**
   * Get contacts with roles for a deal
   * @param dealId Deal ID
   * @returns Array of contacts with roles
   */
  getContactsWithRolesForDeal(dealId: string): any[] {
    try {
      return db.prepare(`
        SELECT
          c.id,
          c.first_name as firstName,
          c.last_name as lastName,
          c.email,
          c.phone,
          c.job_title as jobTitle,
          cr.role
        FROM contact c
        JOIN contact_role cr ON c.id = cr.contact_id
        WHERE cr.deal_id = ?
      `).all(dealId);
    } catch (error) {
      console.error(`Error getting contacts with roles for deal ${dealId}:`, error);
      return [];
    }
  }

  /**
   * Set deal primary company
   * @param dealId Deal ID
   * @param companyId Company ID
   * @returns Success boolean
   */
  setDealPrimaryCompany(dealId: string, companyId: string): boolean {
    try {
      // Since deal table doesn't have company_id column in current schema,
      // this would need to be implemented with a relationship table
      // For now, returning false as it's not implemented
      console.warn('setDealPrimaryCompany not fully implemented - deal table lacks company_id column');
      return false;
    } catch (error) {
      console.error('Error setting deal primary company:', error);
      return false;
    }
  }

  /**
   * Batch update deal contact roles
   * @param updates Array of updates
   * @returns Success boolean
   */
  batchUpdateDealContactRoles(updates: DealContactRole[]): boolean {
    const transaction = db.prepare('BEGIN');
    const rollback = db.prepare('ROLLBACK');
    const commit = db.prepare('COMMIT');

    transaction.run();
    try {
      for (const update of updates) {
        this.updateContactRoleInDeal(update.dealId, update.contactId, update.role);
      }
      commit.run();
      return true;
    } catch (error) {
      rollback.run();
      console.error('Error in batch update deal contact roles:', error);
      return false;
    }
  }

  /**
   * Get deals by company ID
   * @param companyId Company ID
   * @returns Array of deals
   */
  getDealsByCompanyId(companyId: string): Deal[] {
    try {
      // Since deal table doesn't have company_id column,
      // this would need to be implemented with a relationship table
      // For now, returning empty array
      console.warn('getDealsByCompanyId not fully implemented - deal table lacks company_id column');
      return [];
    } catch (error) {
      console.error(`Error getting deals by company ID ${companyId}:`, error);
      return [];
    }
  }



  /**
   * Helper method to enhance a contact with additional data
   * @param contact Contact to enhance
   * @returns Enhanced contact
   */
  private enhanceContact(contact: Contact): Contact {
    // Get company relationships
    // Note: Contact interface doesn't have companyRelationships property
    // This would need to be added to the type definition
    const relationships = this.getContactCompanyRelationships(contact.id);

    // Get deals associated with this contact
    const deals = this.contactRoleRepository.getContactDeals(contact.id);

    // Map ContactCompanyAssociation to ContactCompanyRelationship format
    const companiesWithDetails = relationships.map(rel => {
      const company = this.companyRepository.getCompanyById(rel.companyId);
      return {
        company: company || { id: rel.companyId, name: 'Unknown' },
        role: rel.role,
        isPrimary: rel.isPrimary || false
      };
    });

    // Return contact with additional properties
    return {
      ...contact,
      companies: companiesWithDetails, // Use companies property which exists on Contact interface
      deals
    };
  }
}

// Create and export an instance of the EnhancedRepository
const enhancedRepository = new EnhancedRepository();
export default enhancedRepository;