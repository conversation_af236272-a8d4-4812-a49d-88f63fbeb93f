/**
 * Repository for managing deals
 */

import { v4 as uuidv4 } from 'uuid';
import { BaseRepository } from './base-repository';
import {
  Deal,
  DealCreate,
  DealUpdate,
  Contact,
  Note,
  DataSource
} from '../../frontend/types/crm-types';
import { Company } from '../../types/company-types';
import {
  validateDealCreateAndThrow,
  validateDealUpdateAndThrow,
  isDealValidForProjections
} from '../../utils/deal-validation';
import {
  trackDealChanges,
  canSourceUpdateField
} from '../../utils/deal-tracking';

/**
 * Repository for managing deals
 */
export class DealRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('deal');
  }

  /**
   * Get column mappings for the deal table
   * Maps database column names to TypeScript property names
   */
  private getColumnMappings(): Record<string, string> {
    return {
      'stage': 'stage',
      'value': 'value',
      'currency': 'currency',
      'probability': 'probability',
      'expected_close_date': 'expectedCloseDate',
      'start_date': 'startDate',
      'end_date': 'endDate',
      'invoice_frequency': 'invoiceFrequency',
      'payment_terms': 'paymentTerms',
      'company_id': 'companyId',
      'description': 'description',
      'source': 'source',
      'priority': 'priority',
      'owner': 'owner',
      'hubspot_id': 'hubspotId',
      'harvest_project_id': 'harvestProjectId',
      'status': 'status',
      'custom_fields': 'customFields',
      'include_in_projections': 'includeInProjections',
      'created_at': 'createdAt',
      'updated_at': 'updatedAt',
      'created_by': 'createdBy',
      'updated_by': 'updatedBy',
      'deleted_at': 'deletedAt'
    };
  }

  /**
   * Get all deals
   * @param includeProjectionStatus If true, adds a property indicating if the deal is valid for projections
   * @returns Array of deals
   */
  getAllDeals(includeProjectionStatus: boolean = false): Deal[] {
    try {
      // First check which columns exist in the deal table
      const columns = this.getColumns();

      // Map TypeScript property names to database column names
      const columnMappings = this.getColumnMappings();

      // Build the select clause for deal columns
      const dealSelectClause = columns.map(col => {
        if (col === 'id' || col === 'name') return `d.${col}`;
        if (columnMappings[col]) {
          return `d.${col} as ${columnMappings[col]}`;
        }
        return `d.${col}`;
      }).join(', ');

      const query = `
        SELECT
          ${dealSelectClause},
          c.id as company_id,
          c.name as company_name,
          c.industry as company_industry
        FROM deal d
        LEFT JOIN company c ON d.company_id = c.id
        WHERE d.deleted_at IS NULL
      `;

      const rows = this.db.prepare(query).all();

      // Transform the results to include company information
      const deals = rows.map((row: any) => {
        const deal = { ...row } as Deal;

        // Add company information if available
        if (row.company_id && row.company_name) {
          deal.company = {
            id: row.company_id,
            name: row.company_name,
            industry: row.company_industry,
            createdAt: '', // Required by BaseCompany but not needed for display
            updatedAt: ''  // Required by BaseCompany but not needed for display
          };
        }

        // Clean up the extra company fields from the deal object
        delete (deal as any).company_id;
        delete (deal as any).company_name;
        delete (deal as any).company_industry;

        return deal;
      });

      // Add projection status if requested
      if (includeProjectionStatus) {
        for (const deal of deals) {
          deal.validForProjections = isDealValidForProjections(deal);
        }
      }

      return deals;
    } catch (error) {
      console.error('Error fetching deals from database:', error);
      return [];
    }
  }

  /**
   * Get deal by ID
   * @param id Deal ID
   * @returns Deal or null if not found
   */
  getDealById(id: string): Deal | null {
    try {
      // First check which columns exist in the deal table
      const columns = this.getColumns();

      // Map TypeScript property names to database column names
      const columnMappings = this.getColumnMappings();

      // Build the select clause for deal columns
      const dealSelectClause = columns.map(col => {
        if (col === 'id' || col === 'name') return `d.${col}`;
        if (columnMappings[col]) {
          return `d.${col} as ${columnMappings[col]}`;
        }
        return `d.${col}`;
      }).join(', ');

      const query = `
        SELECT
          ${dealSelectClause},
          c.id as company_id,
          c.name as company_name,
          c.industry as company_industry
        FROM deal d
        LEFT JOIN company c ON d.company_id = c.id
        WHERE d.id = ?
      `;

      const row = this.db.prepare(query).get(id);

      if (!row) return null;

      // Transform the result to include company information
      const deal = { ...row } as Deal;

      // Add company information if available
      if (row.company_id && row.company_name) {
        deal.company = {
          id: row.company_id,
          name: row.company_name,
          industry: row.company_industry,
          createdAt: '', // Required by BaseCompany but not needed for display
          updatedAt: ''  // Required by BaseCompany but not needed for display
        };
      }

      // Clean up the extra company fields from the deal object
      delete (deal as any).company_id;
      delete (deal as any).company_name;
      delete (deal as any).company_industry;

      // Parse custom fields if they exist
      if (deal.customFields) {
        try {
          deal.customFields = typeof deal.customFields === 'string' ? JSON.parse(deal.customFields) : deal.customFields;
        } catch (e) {
          console.error('Error parsing custom fields:', e);
          deal.customFields = {};
        }
      } else {
        deal.customFields = {};
      }

      // Get associated contacts with their roles
      const contacts = this.db.prepare(`
        SELECT
          c.id,
          c.first_name as firstName,
          c.last_name as lastName,
          c.email,
          c.phone,
          c.job_title as jobTitle,
          c.notes,
          c.hubspot_id as hubspotId,
          c.harvest_user_id as harvestUserId,
          c.source,
          c.created_at as createdAt,
          c.updated_at as updatedAt,
          cr.role
        FROM contact c
        JOIN contact_role cr ON c.id = cr.contact_id
        WHERE cr.deal_id = ? AND c.deleted_at IS NULL
      `).all(id) as Contact[];

      deal.contacts = contacts;

      // Company information is already loaded via the JOIN query above

      // Check if note table exists before querying it
      let notes: Note[] = [];
      try {
        // Check if the note table exists
        const tableExists = this.db.prepare(`
          SELECT name FROM sqlite_master
          WHERE type='table' AND name='note'
        `).get();

        if (tableExists) {
          notes = this.db.prepare(`
            SELECT
              id, deal_id as dealId, content, created_at as createdAt, created_by as createdBy
            FROM note
            WHERE deal_id = ?
            ORDER BY created_at DESC
          `).all(id) as Note[];
        }
      } catch (noteError) {
        console.error('Error fetching notes, note table may not exist:', noteError);
        // Continue without notes
      }

      deal.notes = notes;

      // Add projection status
      deal.validForProjections = isDealValidForProjections(deal);

      return deal;
    } catch (error) {
      console.error('Error fetching deal by ID from database:', error);
      return null;
    }
  }

  /**
   * Get deal by HubSpot ID
   * @param hubspotId HubSpot ID
   * @returns Deal or null if not found
   */
  getDealByHubspotId(hubspotId: string): Deal | null {
    try {
      // First check which columns exist in the deal table
      const columns = this.getColumns();

      // Map TypeScript property names to database column names
      const columnMappings = this.getColumnMappings();

      // Build the select clause
      const selectClause = columns.map(col => {
        if (col === 'id' || col === 'name') return col;
        if (columnMappings[col]) {
          return `${col} as ${columnMappings[col]}`;
        }
        return col;
      }).join(', ');

      const query = `
        SELECT ${selectClause}
        FROM deal
        WHERE hubspot_id = ?
      `;

      const deal = this.db.prepare(query).get(hubspotId) as Deal | undefined;

      if (!deal) return null;

      // Parse custom fields if they exist
      if (deal.customFields) {
        try {
          deal.customFields = typeof deal.customFields === 'string' ? JSON.parse(deal.customFields) : deal.customFields;
        } catch (e) {
          console.error('Error parsing custom fields:', e);
          deal.customFields = {};
        }
      } else {
        deal.customFields = {};
      }

      return deal;
    } catch (error) {
      console.error('Error fetching deal by HubSpot ID from database:', error);
      return null;
    }
  }

  /**
   * Create a new deal
   * @param dealData Deal data
   * @param source Data source
   * @returns Created deal
   */
  createDeal(dealData: DealCreate, source: string = 'Manual'): Deal {
    try {
      // Validate deal data
      validateDealCreateAndThrow(dealData);

      const id = uuidv4();
      const now = new Date().toISOString();

      // Convert custom fields to JSON string if they exist
      const customFieldsJson = dealData.customFields ? JSON.stringify(dealData.customFields) : null;

      this.db.prepare(`
        INSERT INTO deal (
          id,
          name,
          stage,
          value,
          currency,
          probability,
          expected_close_date,
          start_date,
          end_date,
          invoice_frequency,
          payment_terms,
          company_id,
          description,
          source,
          priority,
          owner,
          hubspot_id,
          custom_fields,
          include_in_projections,
          created_at,
          updated_at,
          created_by,
          updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        dealData.name,
        dealData.stage,
        dealData.value || null,
        dealData.currency || null,
        dealData.probability || null,
        dealData.expectedCloseDate || null,
        dealData.startDate || null,
        dealData.endDate || null,
        dealData.invoiceFrequency || null,
        dealData.paymentTerms || null,
        dealData.companyId,
        dealData.description || null,
        source,
        dealData.priority || null,
        dealData.owner || null,
        dealData.hubspotId || null,
        customFieldsJson,
        dealData.includeInProjections !== undefined ? dealData.includeInProjections : 1,
        now,
        now,
        dealData.createdBy || 'system',
        dealData.updatedBy || 'system'
      );

      return this.getDealById(id) as Deal;
    } catch (error) {
      console.error('Error creating deal in database:', error);
      throw error;
    }
  }

  /**
   * Update a deal
   * @param id Deal ID
   * @param dealData Deal data
   * @param source Data source
   * @returns Updated deal
   */
  updateDeal(id: string, dealData: DealUpdate, source: string = 'Manual'): Deal | null {
    try {
      // Validate deal data
      validateDealUpdateAndThrow(dealData);

      const deal = this.getDealById(id);
      if (!deal) {
        console.error(`Deal with ID ${id} not found`);
        return null;
      }

      // Track changes for audit
      trackDealChanges(id, deal, dealData, source as DataSource, dealData.updatedBy);

      // Only update fields that the source is allowed to update
      const updateData: Record<string, any> = {};
      for (const key in dealData) {
        if (canSourceUpdateField(id, key, source as DataSource)) {
          updateData[key] = dealData[key as keyof DealUpdate];
        }
      }

      const now = new Date().toISOString();

      // Convert custom fields to JSON string if they exist
      let customFieldsJson = null;
      if (updateData.customFields !== undefined) {
        customFieldsJson = updateData.customFields ? JSON.stringify(updateData.customFields) : null;
      } else if (deal.customFields) {
        customFieldsJson = typeof deal.customFields === 'string' ? deal.customFields : JSON.stringify(deal.customFields);
      }

      this.db.prepare(`
        UPDATE deal
        SET
          name = ?,
          stage = ?,
          value = ?,
          currency = ?,
          probability = ?,
          expected_close_date = ?,
          start_date = ?,
          end_date = ?,
          invoice_frequency = ?,
          payment_terms = ?,
          description = ?,
          source = ?,
          priority = ?,
          owner = ?,
          hubspot_id = ?,
          custom_fields = ?,
          include_in_projections = ?,
          updated_at = ?,
          updated_by = ?
        WHERE id = ?
      `).run(
        updateData.name !== undefined ? updateData.name : deal.name,
        updateData.stage !== undefined ? updateData.stage : deal.stage,
        updateData.value !== undefined ? updateData.value : deal.value,
        updateData.currency !== undefined ? updateData.currency : deal.currency,
        updateData.probability !== undefined ? updateData.probability : deal.probability,
        updateData.expectedCloseDate !== undefined ? updateData.expectedCloseDate : deal.expectedCloseDate,
        updateData.startDate !== undefined ? updateData.startDate : deal.startDate,
        updateData.endDate !== undefined ? updateData.endDate : deal.endDate,
        updateData.invoiceFrequency !== undefined ? updateData.invoiceFrequency : deal.invoiceFrequency,
        updateData.paymentTerms !== undefined ? updateData.paymentTerms : deal.paymentTerms,
        updateData.description !== undefined ? updateData.description : deal.description,
        updateData.source !== undefined ? updateData.source : deal.source,
        updateData.priority !== undefined ? updateData.priority : deal.priority,
        updateData.owner !== undefined ? updateData.owner : deal.owner,
        updateData.hubspotId !== undefined ? updateData.hubspotId : deal.hubspotId,
        customFieldsJson,
        updateData.includeInProjections !== undefined ? updateData.includeInProjections : deal.includeInProjections,
        now,
        updateData.updatedBy || 'system',
        id
      );

      // The changes are already tracked above in trackDealChanges

      return this.getDealById(id);
    } catch (error) {
      console.error('Error updating deal in database:', error);
      throw error;
    }
  }

  /**
   * Delete a deal
   * @param id Deal ID
   * @param softDelete Whether to perform a soft delete (default: true)
   * @returns Boolean indicating success
   */
  deleteDeal(id: string, softDelete: boolean = true): boolean {
    try {
      if (softDelete) {
        const now = new Date().toISOString();
        this.db.prepare(`
          UPDATE deal
          SET deleted_at = ?
          WHERE id = ?
        `).run(now, id);
      } else {
        this.db.prepare(`
          DELETE FROM deal
          WHERE id = ?
        `).run(id);
      }
      return true;
    } catch (error) {
      console.error('Error deleting deal from database:', error);
      return false;
    }
  }

  /**
   * Record deal changes in audit log
   * @param dealId Deal ID
   * @param changes Changes to record
   * @param source Source of changes
   * @param userId User who made the changes
   */
  private recordDealChanges(dealId: string, changes: Record<string, any>, source: string, userId: string): void {
    try {
      const now = new Date().toISOString();
      const changesJson = JSON.stringify(changes);

      // Check if audit_log table exists
      const auditLogExists = this.db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='audit_log'
      `).get();

      if (!auditLogExists) {
        console.log('Audit log table does not exist, skipping audit logging');
        return;
      }

      this.db.prepare(`
        INSERT INTO audit_log (
          entity_type,
          entity_id,
          action,
          changes,
          source,
          created_at,
          created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        'deal',
        dealId,
        'update',
        changesJson,
        source,
        now,
        userId
      );
    } catch (error) {
      console.error('Error recording deal changes in audit log:', error);
      // Don't throw, just log the error
    }
  }
}
