/**
 * Repository for managing contacts
 */

import { v4 as uuidv4 } from 'uuid';
import { BaseRepository } from './base-repository';
import {
  Contact,
  ContactCreate,
  ContactUpdate
} from '../../frontend/types/crm-types';

/**
 * Repository for managing contacts
 */
export class ContactRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('contact');
  }

  /**
   * Get all contacts
   * @returns Array of contacts
   */
  getAllContacts(): Contact[] {
    try {
      const contacts = this.db.prepare(`
        SELECT
          id,
          first_name as firstName,
          last_name as lastName,
          email,
          phone,
          job_title as jobTitle,
          notes,
          hubspot_id as hubspotId,
          harvest_user_id as harvestUserId,
          source,
          created_at as createdAt,
          updated_at as updatedAt,
          created_by as createdBy,
          updated_by as updatedBy,
          deleted_at as deletedAt
        FROM contact
        WHERE deleted_at IS NULL
      `).all() as Contact[];

      return contacts;
    } catch (error) {
      console.error('Error fetching contacts from database:', error);
      return [];
    }
  }

  /**
   * Get contact by ID
   * @param id Contact ID
   * @returns Contact or null if not found
   */
  getContactById(id: string): Contact | null {
    try {
      const contact = this.db.prepare(`
        SELECT
          id,
          first_name as firstName,
          last_name as lastName,
          email,
          phone,
          job_title as jobTitle,
          notes,
          hubspot_id as hubspotId,
          harvest_user_id as harvestUserId,
          source,
          created_at as createdAt,
          updated_at as updatedAt,
          created_by as createdBy,
          updated_by as updatedBy,
          deleted_at as deletedAt
        FROM contact
        WHERE id = ?
      `).get(id) as Contact | undefined;

      return contact || null;
    } catch (error) {
      console.error('Error fetching contact by ID from database:', error);
      return null;
    }
  }

  /**
   * Get contact by HubSpot ID
   * @param hubspotId HubSpot ID
   * @returns Contact or null if not found
   */
  getContactByHubspotId(hubspotId: string): Contact | null {
    try {
      const contact = this.db.prepare(`
        SELECT
          id,
          first_name as firstName,
          last_name as lastName,
          email,
          phone,
          job_title as jobTitle,
          notes,
          hubspot_id as hubspotId,
          harvest_user_id as harvestUserId,
          source,
          created_at as createdAt,
          updated_at as updatedAt,
          created_by as createdBy,
          updated_by as updatedBy,
          deleted_at as deletedAt
        FROM contact
        WHERE hubspot_id = ?
      `).get(hubspotId) as Contact | undefined;

      return contact || null;
    } catch (error) {
      console.error('Error fetching contact by HubSpot ID from database:', error);
      return null;
    }
  }

  /**
   * Get contacts by company ID
   * @param companyId Company ID
   * @returns Array of contacts
   */
  getContactsByCompanyId(companyId: string): Contact[] {
    try {
      const contacts = this.db.prepare(`
        SELECT
          c.id,
          c.first_name as firstName,
          c.last_name as lastName,
          c.email,
          c.phone,
          c.job_title as jobTitle,
          c.notes,
          c.hubspot_id as hubspotId,
          c.harvest_user_id as harvestUserId,
          c.source,
          c.created_at as createdAt,
          c.updated_at as updatedAt,
          c.created_by as createdBy,
          c.updated_by as updatedBy,
          c.deleted_at as deletedAt,
          cc.role,
          cc.is_primary as isPrimary
        FROM contact c
        INNER JOIN contact_company cc ON c.id = cc.contact_id
        WHERE cc.company_id = ?
          AND c.deleted_at IS NULL
      `).all(companyId) as Contact[];

      return contacts;
    } catch (error) {
      console.error('Error fetching contacts by company ID from database:', error);
      return [];
    }
  }

  /**
   * Create a new contact
   * @param contactData Contact data
   * @param source Data source
   * @returns Created contact
   */
  createContact(contactData: ContactCreate, source: string = 'Manual'): Contact {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      this.db.prepare(`
        INSERT INTO contact (
          id,
          first_name,
          last_name,
          email,
          phone,
          job_title,
          notes,
          hubspot_id,
          harvest_user_id,
          source,
          created_at,
          updated_at,
          created_by,
          updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        contactData.firstName || null,
        contactData.lastName || null,
        contactData.email || null,
        contactData.phone || null,
        contactData.jobTitle || null,
        contactData.notes || null,
        contactData.hubspotId || null,
        contactData.harvestUserId || null,
        source,
        now,
        now,
        contactData.createdBy || 'system',
        contactData.updatedBy || 'system'
      );
      
      // Handle company relationships if provided
      if (contactData.companyId) {
        // For backward compatibility, treat companyId as primary company
        this.db.prepare(`
          INSERT INTO contact_company (contact_id, company_id, is_primary, created_at, created_by)
          VALUES (?, ?, 1, ?, ?)
        `).run(id, contactData.companyId, now, contactData.createdBy || 'system');
      }
      
      // Handle multiple company relationships if provided
      const companies = contactData.companies || contactData.companyAssociations;
      if (companies && companies.length > 0) {
        for (const company of companies) {
          this.db.prepare(`
            INSERT INTO contact_company (contact_id, company_id, role, is_primary, created_at, created_by)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            id, 
            company.companyId, 
            company.role || null,
            company.isPrimary ? 1 : 0,
            now,
            contactData.createdBy || 'system'
          );
        }
      }

      return this.getContactById(id) as Contact;
    } catch (error) {
      console.error('Error creating contact in database:', error);
      throw error;
    }
  }

  /**
   * Update a contact
   * @param id Contact ID
   * @param contactData Contact data
   * @returns Updated contact
   */
  updateContact(id: string, contactData: ContactUpdate): Contact | null {
    try {
      const contact = this.getContactById(id);
      if (!contact) {
        console.error(`Contact with ID ${id} not found`);
        return null;
      }

      const now = new Date().toISOString();

      this.db.prepare(`
        UPDATE contact
        SET
          first_name = ?,
          last_name = ?,
          email = ?,
          phone = ?,
          job_title = ?,
          notes = ?,
          updated_at = ?,
          updated_by = ?
        WHERE id = ?
      `).run(
        contactData.firstName !== undefined ? contactData.firstName : contact.firstName,
        contactData.lastName !== undefined ? contactData.lastName : contact.lastName,
        contactData.email !== undefined ? contactData.email : contact.email,
        contactData.phone !== undefined ? contactData.phone : contact.phone,
        contactData.jobTitle !== undefined ? contactData.jobTitle : contact.jobTitle,
        contactData.notes !== undefined ? contactData.notes : contact.notes,
        now,
        contactData.updatedBy || 'system',
        id
      );
      
      // Handle company relationship updates if provided
      if (contactData.companyId !== undefined) {
        // For backward compatibility, update primary company relationship
        // First remove existing primary company
        this.db.prepare(`
          DELETE FROM contact_company 
          WHERE contact_id = ? AND is_primary = 1
        `).run(id);
        
        // Then add new primary company if provided
        if (contactData.companyId) {
          this.db.prepare(`
            INSERT INTO contact_company (contact_id, company_id, is_primary, created_at, created_by)
            VALUES (?, ?, 1, ?, ?)
          `).run(id, contactData.companyId, now, contactData.updatedBy || 'system');
        }
      }
      
      // Handle multiple company relationships if provided
      if (contactData.companyAssociations !== undefined) {
        // Remove all existing company relationships
        this.db.prepare(`
          DELETE FROM contact_company WHERE contact_id = ?
        `).run(id);
        
        // Add new company relationships
        if (contactData.companyAssociations && contactData.companyAssociations.length > 0) {
          for (const company of contactData.companyAssociations) {
            this.db.prepare(`
              INSERT INTO contact_company (contact_id, company_id, role, is_primary, created_at, created_by)
              VALUES (?, ?, ?, ?, ?, ?)
            `).run(
              id, 
              company.companyId, 
              company.role || null,
              company.isPrimary ? 1 : 0,
              now,
              contactData.updatedBy || 'system'
            );
          }
        }
      }

      return this.getContactById(id);
    } catch (error) {
      console.error('Error updating contact in database:', error);
      throw error;
    }
  }

  /**
   * Delete a contact
   * @param id Contact ID
   * @param softDelete Whether to perform a soft delete (default: true)
   * @returns Boolean indicating success
   */
  deleteContact(id: string, softDelete: boolean = true): boolean {
    try {
      if (softDelete) {
        // Perform soft delete
        const now = new Date().toISOString();
        this.db.prepare(`
          UPDATE contact
          SET deleted_at = ?
          WHERE id = ?
        `).run(now, id);
      } else {
        // Perform hard delete
        this.db.prepare(`
          DELETE FROM contact
          WHERE id = ?
        `).run(id);
      }
      return true;
    } catch (error) {
      console.error('Error deleting contact from database:', error);
      return false;
    }
  }
}
