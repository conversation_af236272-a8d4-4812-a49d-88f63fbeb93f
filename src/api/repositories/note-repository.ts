/**
 * Repository for managing notes
 */

import { v4 as uuidv4 } from 'uuid';
import { BaseRepository } from './base-repository';
import { Note, NoteCreate } from '../../frontend/types/crm-types';

/**
 * Repository for managing notes
 */
export class NoteRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('note');
  }

  /**
   * Get all notes
   * @returns Array of notes
   */
  getAllNotes(): Note[] {
    try {
      const notes = this.db.prepare(`
        SELECT
          id,
          deal_id as dealId,
          content,
          created_at as createdAt,
          created_by as createdBy
        FROM note
        ORDER BY created_at DESC
      `).all() as Note[];

      return notes;
    } catch (error) {
      console.error('Error fetching notes from database:', error);
      return [];
    }
  }

  /**
   * Get note by ID
   * @param id Note ID
   * @returns Note or null if not found
   */
  getNoteById(id: string): Note | null {
    try {
      const note = this.db.prepare(`
        SELECT
          id,
          deal_id as dealId,
          content,
          created_at as createdAt,
          created_by as createdBy
        FROM note
        WHERE id = ?
      `).get(id) as Note | undefined;

      return note || null;
    } catch (error) {
      console.error('Error fetching note by ID from database:', error);
      return null;
    }
  }

  /**
   * Get notes by deal ID
   * @param dealId Deal ID
   * @returns Array of notes
   */
  getNotesByDealId(dealId: string): Note[] {
    try {
      const notes = this.db.prepare(`
        SELECT
          id,
          deal_id as dealId,
          content,
          created_at as createdAt,
          created_by as createdBy
        FROM note
        WHERE deal_id = ?
        ORDER BY created_at DESC
      `).all(dealId) as Note[];

      return notes;
    } catch (error) {
      console.error('Error fetching notes by deal ID from database:', error);
      return [];
    }
  }

  /**
   * Create a new note
   * @param noteData Note data
   * @returns Created note
   */
  createNote(noteData: NoteCreate): Note {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      this.db.prepare(`
        INSERT INTO note (
          id,
          deal_id,
          content,
          created_at,
          created_by
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        id,
        noteData.dealId,
        noteData.content,
        now,
        noteData.createdBy || 'system'
      );

      return this.getNoteById(id) as Note;
    } catch (error) {
      console.error('Error creating note in database:', error);
      throw error;
    }
  }

  /**
   * Update a note
   * @param id Note ID
   * @param content New content
   * @returns Updated note
   */
  updateNote(id: string, content: string): Note | null {
    try {
      const note = this.getNoteById(id);
      if (!note) {
        console.error(`Note with ID ${id} not found`);
        return null;
      }

      this.db.prepare(`
        UPDATE note
        SET content = ?
        WHERE id = ?
      `).run(content, id);

      return this.getNoteById(id);
    } catch (error) {
      console.error('Error updating note in database:', error);
      throw error;
    }
  }

  /**
   * Delete a note
   * @param id Note ID
   * @returns Boolean indicating success
   */
  deleteNote(id: string): boolean {
    try {
      this.db.prepare(`
        DELETE FROM note
        WHERE id = ?
      `).run(id);
      return true;
    } catch (error) {
      console.error('Error deleting note from database:', error);
      return false;
    }
  }

  /**
   * Delete all notes for a deal
   * @param dealId Deal ID
   * @returns Number of notes deleted
   */
  deleteNotesByDealId(dealId: string): number {
    try {
      const result = this.db.prepare(`
        DELETE FROM note
        WHERE deal_id = ?
      `).run(dealId);
      return result.changes;
    } catch (error) {
      console.error('Error deleting notes by deal ID from database:', error);
      return 0;
    }
  }
}
