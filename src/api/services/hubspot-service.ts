import { Client } from '@hubspot/api-client';
import db from './db-service';
import { v4 as uuidv4 } from 'uuid';
import { HubSpotImport, DealStage } from '../../frontend/types/crm-types';
import { DealRepository } from '../repositories/deal-repository';
import { CompanyRepository } from '../repositories/company-repository';
import { ContactRepository } from '../repositories/contact-repository';
import { DataSource } from '../../utils/deal-tracking';
import fetch from 'node-fetch';
import { EventEmitter } from 'events';
import activityLogger from '../../utils/activity-logger';

// Progress tracking types
export interface ImportProgress {
  step: 'companies' | 'deals' | 'contacts';
  current: number;
  total: number;
  currentItem?: string;
  errors: Array<{ item: string; error: string }>;
}

export interface ImportResult {
  success: boolean;
  count: number;
  errors: Array<{ item: string; error: string }>;
  updates: Array<{ item: string; changes: string[] }>;
  created: Array<{ item: string }>;
  error?: string;
}

// Define types for HubSpot objects
interface HubSpotObject {
  id: string;
  properties: {
    [key: string]: string | undefined;
  };
}

interface HubSpotPaging {
  next?: {
    after?: string;
    link?: string;
  };
}

interface HubSpotResponse {
  results: HubSpotObject[];
  paging?: HubSpotPaging;
}

/**
 * Service for interacting with the HubSpot API
 */
export class HubSpotService extends EventEmitter {
  private client: Client | null = null;
  private dealRepository: DealRepository;
  private companyRepository: CompanyRepository;
  private contactRepository: ContactRepository;
  private socketIO: any = null;

  constructor() {
    super();
    this.dealRepository = new DealRepository();
    this.companyRepository = new CompanyRepository();
    this.contactRepository = new ContactRepository();
  }

  /**
   * Set the Socket.IO instance for real-time progress updates
   */
  setSocketIO(io: any) {
    this.socketIO = io;
  }

  /**
   * Helper to compare objects and track changes
   */
  private trackChanges(original: any, updated: any, fieldsToTrack: string[]): string[] {
    const changes: string[] = [];

    for (const field of fieldsToTrack) {
      const originalValue = original[field];
      const updatedValue = updated[field];

      // Skip if both are null/undefined
      if (!originalValue && !updatedValue) continue;

      // Check if values are different
      if (originalValue !== updatedValue) {
        if (!originalValue && updatedValue) {
          changes.push(`Added ${field}: "${updatedValue}"`);
        } else if (originalValue && !updatedValue) {
          changes.push(`Removed ${field}`);
        } else {
          changes.push(`Updated ${field}: "${originalValue}" → "${updatedValue}"`);
        }
      }
    }

    return changes;
  }

  /**
   * Override emit to also send to Socket.IO
   */
  emit(event: string, ...args: any[]): boolean {
    // Emit to EventEmitter listeners
    const result = super.emit(event, ...args);

    // Also emit to Socket.IO if available
    if (this.socketIO && event === 'progress') {
      this.socketIO.emit('hubspot-import-progress', args[0]);
    }

    return result;
  }

  /**
   * Initialize the HubSpot client with an access token
   * @param accessToken The HubSpot API access token
   * @returns True if initialization was successful, false otherwise
   */
  initialize(accessToken: string): boolean {
    try {
      if (!accessToken || accessToken.trim() === '') {
        console.error('Cannot initialize HubSpot client with empty access token');
        return false;
      }

      // Create a new HubSpot client with the provided access token
      this.client = new Client({ accessToken });

      console.log('HubSpot client initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing HubSpot client:', error);
      this.client = null;
      return false;
    }
  }

  /**
   * Check if the client is initialized
   */
  isInitialized(): boolean {
    return this.client !== null;
  }

  /**
   * Ensure the HubSpot settings table exists
   */
  private ensureHubSpotSettingsTableExists(): boolean {
    try {
      // Check if the table exists
      const tableExists = db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='hubspot_settings'
      `).get();

      if (!tableExists) {
        console.log('Creating hubspot_settings table...');

        // Create the table
        db.prepare(`
          CREATE TABLE hubspot_settings (
            id TEXT PRIMARY KEY,
            key TEXT NOT NULL UNIQUE,
            value TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        `).run();

        console.log('hubspot_settings table created successfully');
      }

      return true;
    } catch (error) {
      console.error('Error ensuring hubspot_settings table exists:', error);
      return false;
    }
  }

  /**
   * Get the HubSpot access token from the database
   */
  getAccessToken(): string | null {
    try {
      // Ensure the table exists
      this.ensureHubSpotSettingsTableExists();

      const result = db.prepare('SELECT value FROM hubspot_settings WHERE key = ?').get('access_token');
      return result ? (result as { value: string }).value : null;
    } catch (error) {
      console.error('Error getting HubSpot access token:', error);
      return null;
    }
  }

  /**
   * Save the HubSpot access token to the database
   */
  saveAccessToken(accessToken: string): boolean {
    try {
      // Ensure the table exists
      this.ensureHubSpotSettingsTableExists();

      const now = new Date().toISOString();
      const id = require('uuid').v4(); // Generate a UUID for the ID

      // Check if the token already exists
      const existingToken = db.prepare('SELECT value FROM hubspot_settings WHERE key = ?').get('access_token');

      if (existingToken) {
        // Update existing token
        db.prepare('UPDATE hubspot_settings SET value = ?, updated_at = ? WHERE key = ?')
          .run(accessToken, now, 'access_token');
      } else {
        // Insert new token
        db.prepare('INSERT INTO hubspot_settings (id, key, value, created_at, updated_at) VALUES (?, ?, ?, ?, ?)')
          .run(id, 'access_token', accessToken, now, now);
      }

      return true;
    } catch (error) {
      console.error('Error saving HubSpot access token:', error);
      return false;
    }
  }

  /**
   * Delete the HubSpot access token from the database
   */
  deleteAccessToken(): boolean {
    try {
      // Ensure the table exists
      this.ensureHubSpotSettingsTableExists();

      db.prepare('DELETE FROM hubspot_settings WHERE key = ?').run('access_token');
      this.client = null;
      return true;
    } catch (error) {
      console.error('Error deleting HubSpot access token:', error);
      return false;
    }
  }

  /**
   * Get company association for a HubSpot deal
   * @param dealId HubSpot deal ID
   * @returns HubSpot company ID or null if no association found
   */
  private async getDealCompanyAssociation(dealId: string): Promise<string | null> {
    try {
      // Use direct REST API call to get deal-company associations
      // This is more reliable than the SDK's associations API
      const accessToken = this.getAccessToken();
      const url = `https://api.hubapi.com/crm/v3/objects/deals/${dealId}/associations/companies`;

      const fetchResponse = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (fetchResponse.ok) {
        const response = await fetchResponse.json();

        if (response && response.results && response.results.length > 0) {
          console.log(`Found ${response.results.length} company association(s) for deal ${dealId}`);
          // Return the first associated company ID
          return response.results[0].id;
        }
      } else {
        console.log(`Failed to get company associations for deal ${dealId}: ${fetchResponse.status} ${fetchResponse.statusText}`);
      }

      console.log(`No company associations found for deal ${dealId}`);
      return null;
    } catch (error) {
      console.error(`Error fetching company association for deal ${dealId}:`, error);
      return null;
    }
  }

  /**
   * Import deals from HubSpot with progress tracking
   */
  async importDeals(): Promise<ImportResult> {
    if (!this.client) {
      return { success: false, count: 0, errors: [], updates: [], created: [], error: 'HubSpot client not initialized' };
    }

    const errors: Array<{ item: string; error: string }> = [];
    const updates: Array<{ item: string; changes: string[] }> = [];
    const created: Array<{ item: string }> = [];

    // Log sync start activity
    activityLogger.logHubSpotSyncStarted().catch(error => {
      console.error('Error logging HubSpot sync start activity:', error);
    });

    try {
      // Get deals from HubSpot using the search API with pagination
      const properties = [
        'dealname',
        'dealstage',
        'amount',
        'closedate',
        'pipeline',
        'description',
        'hubspot_owner_id',
        'createdate',
        'hs_lastmodifieddate',
        'chance_of_win' // Custom HubSpot field for probability
      ];

      let allDeals: HubSpotObject[] = [];
      let hasMore = true;
      let after = 0;
      const pageSize = 100;
      let totalFound = 0;

      // Emit initial progress
      this.emit('progress', {
        step: 'deals',
        current: 0,
        total: 0,
        currentItem: 'Fetching deals from HubSpot...',
        errors: []
      } as ImportProgress);

      // Implement pagination to fetch all deals
      while (hasMore) {
        const publicObjectSearchRequest = {
          filterGroups: [],
          properties,
          limit: pageSize,
          after: after ? after.toString() : undefined
        };

        const response = await this.client.crm.deals.searchApi.doSearch(publicObjectSearchRequest);

        // Add the current page of results to our collection
        allDeals = [...allDeals, ...response.results];
        totalFound += response.results.length;

        // Log progress
        console.log(`Fetched page of ${response.results.length} deals. Total so far: ${totalFound}`);

        // Emit progress update
        this.emit('progress', {
          step: 'deals',
          current: 0,
          total: totalFound,
          currentItem: `Fetched ${totalFound} deals from HubSpot...`,
          errors: []
        } as ImportProgress);

        // Check if there are more results to fetch
        if (response.paging && response.paging.next && response.paging.next.after) {
          after = parseInt(response.paging.next.after);
        } else {
          hasMore = false;
        }
      }

      // Log the raw deal data for debugging
      console.log(`Found ${allDeals.length} deals in HubSpot`);

      // Count deals by stage for debugging
      const stageCount: Record<string, number> = {};
      allDeals.forEach(deal => {
        const stage = deal.properties.dealstage;
        stageCount[stage] = (stageCount[stage] || 0) + 1;
      });

      console.log('HubSpot deal stages distribution:');
      Object.entries(stageCount).forEach(([stage, count]) => {
        console.log(`- Stage "${stage}": ${count} deals`);
      });

      if (allDeals.length > 0) {
        console.log('Sample deal data from HubSpot:');
        allDeals.slice(0, 3).forEach(deal => {
          console.log(`- Deal "${deal.properties.dealname}": Stage = "${deal.properties.dealstage}", Chance of Win = "${deal.properties.chance_of_win || 'Not set'}"`);
        });
      }

      let importedCount = 0;

      // Map HubSpot deal stages to our stages
      const stageMapping: Record<string, string> = {
        // Numeric stage IDs from HubSpot (based on provided mapping)
        '592467925': 'Identified',
        '592467926': 'Qualified',
        '592467927': 'Solution proposal',
        '592467928': 'Solution presentation',
        '592467929': 'Objection handling',
        '592467930': 'Finalising terms',
        '592467931': 'Closed won',
        '592467932': 'Closed lost',
        '615547857': 'Abandoned'
      };

      // Process each deal
      for (let i = 0; i < allDeals.length; i++) {
        const deal = allDeals[i];

        // Emit progress for current deal
        this.emit('progress', {
          step: 'deals',
          current: i + 1,
          total: allDeals.length,
          currentItem: `Processing deal: ${deal.properties.dealname}`,
          errors: errors
        } as ImportProgress);

        try {
          // Map HubSpot deal to our deal format
          // Get the deal stage from HubSpot
          const hubspotStage = deal.properties.dealstage;
          const hubspotId = deal.id;

          // Try to map the stage - first try exact match, then try lowercase
          const mappedStage = stageMapping[hubspotStage] || stageMapping[hubspotStage.toLowerCase()] || 'Identified';

          // Log the stage mapping for debugging
          console.log(`Mapping HubSpot stage "${hubspotStage}" to "${mappedStage}" for deal "${deal.properties.dealname}" (HubSpot ID: ${hubspotId})`);

          // Check if the deal already exists by HubSpot ID
          let existingDeal = null;
          try {
            existingDeal = this.dealRepository.getDealByHubspotId(hubspotId);
          } catch (error) {
            // If the column doesn't exist yet, just continue with creating a new deal
            console.log('Note: Could not check for existing deal by HubSpot ID, will create new deal');
          }

        if (existingDeal) {
          // Update the existing deal
          console.log(`Updating existing deal "${existingDeal.name}" with HubSpot ID ${hubspotId}`);

          // Parse probability from chance_of_win field if available
          let probability: number | undefined = undefined;
          if (deal.properties.chance_of_win) {
            // Parse the probability value from HubSpot
            const parsedProb = parseFloat(deal.properties.chance_of_win);
            if (!isNaN(parsedProb)) {
              // HubSpot appears to store chance_of_win as a decimal (e.g., 0.95 for 95%)
              // Our app also stores probability as a decimal (0-1), so we use it directly
              // Just ensure it's within the valid range of 0-1
              probability = Math.min(1, Math.max(0, parsedProb));
              console.log(`Parsed probability for deal "${deal.properties.dealname}": ${probability * 100}% (from HubSpot value: ${deal.properties.chance_of_win})`);
            }
          }

          // Prepare custom fields - store any HubSpot properties that aren't mapped to our standard fields
          const standardFields = [
            'dealname', 'dealstage', 'amount', 'closedate', 'description', 'hubspot_owner_id', 'chance_of_win'
          ];

          // Extract custom fields from HubSpot properties
          const customFields: Record<string, any> = {};
          for (const [key, value] of Object.entries(deal.properties)) {
            if (!standardFields.includes(key) && value !== null && value !== undefined && value !== '') {
              customFields[key] = value;
            }
          }

          // Merge with existing custom fields if they exist
          const mergedCustomFields = {
            ...(existingDeal.customFields || {}),
            ...customFields
          };

          // Track what fields will change
          const newData = {
            name: deal.properties.dealname,
            stage: mappedStage as DealStage,
            value: deal.properties.amount ? parseFloat(deal.properties.amount) : undefined,
            currency: 'AUD',
            probability: probability,
            expectedCloseDate: deal.properties.closedate,
            description: deal.properties.description,
            owner: deal.properties.hubspot_owner_id
          };

          const fieldsToTrack = ['name', 'stage', 'value', 'probability', 'expectedCloseDate', 'description', 'owner'];
          const changes = this.trackChanges(existingDeal, newData, fieldsToTrack);

          const updatedDeal = this.dealRepository.updateDeal(
            existingDeal.id,
            {
              ...newData,
              customFields: mergedCustomFields
            },
            'HubSpot' // Source of the update
          );

          if (updatedDeal) {
            importedCount++;
            if (changes.length > 0) {
              updates.push({
                item: deal.properties.dealname,
                changes: changes
              });
            }
          }
        } else {
          // Create a new deal
          console.log(`Creating new deal "${deal.properties.dealname}" with HubSpot ID ${hubspotId}`);

          // Parse probability from chance_of_win field if available
          let probability: number | undefined = undefined;
          if (deal.properties.chance_of_win) {
            // Parse the probability value from HubSpot
            const parsedProb = parseFloat(deal.properties.chance_of_win);
            if (!isNaN(parsedProb)) {
              // HubSpot appears to store chance_of_win as a decimal (e.g., 0.95 for 95%)
              // Our app also stores probability as a decimal (0-1), so we use it directly
              // Just ensure it's within the valid range of 0-1
              probability = Math.min(1, Math.max(0, parsedProb));
              console.log(`Parsed probability for new deal "${deal.properties.dealname}": ${probability * 100}% (from HubSpot value: ${deal.properties.chance_of_win})`);
            }
          }

          // Prepare custom fields - store any HubSpot properties that aren't mapped to our standard fields
          const standardFields = [
            'dealname', 'dealstage', 'amount', 'closedate', 'description', 'hubspot_owner_id', 'chance_of_win'
          ];

          // Extract custom fields from HubSpot properties
          const customFields: Record<string, any> = {};
          for (const [key, value] of Object.entries(deal.properties)) {
            if (!standardFields.includes(key) && value !== null && value !== undefined && value !== '') {
              customFields[key] = value;
            }
          }

          // Get the associated company from HubSpot
          let companyId: string | null = null;

          try {
            // Get the HubSpot company association for this deal
            const hubspotCompanyId = await this.getDealCompanyAssociation(hubspotId);

            if (!hubspotCompanyId) {
              const errorMsg = `No company association found in HubSpot. Please associate this deal with a company in HubSpot.`;
              console.error(`Skipping deal "${deal.properties.dealname}" - ${errorMsg}`);
              errors.push({
                item: deal.properties.dealname || `Deal ${deal.id}`,
                error: errorMsg
              });
              continue;
            }

            // Try to find the company in our database by HubSpot ID
            const existingCompany = this.companyRepository.getCompanyByHubspotId(hubspotCompanyId);

            if (!existingCompany) {
              const errorMsg = `Company not found in database. Please import companies first.`;
              console.error(`Skipping deal "${deal.properties.dealname}" - company with HubSpot ID ${hubspotCompanyId} not found in database.`);
              errors.push({
                item: deal.properties.dealname || `Deal ${deal.id}`,
                error: errorMsg
              });
              continue;
            }

            companyId = existingCompany.id;
            console.log(`Using company "${existingCompany.name}" (ID: ${companyId}) for deal "${deal.properties.dealname}"`);

          } catch (error) {
            const errorMsg = `Unable to get company association: ${error instanceof Error ? error.message : 'Unknown error'}`;
            console.error(`Error getting company association for deal "${deal.properties.dealname}":`, error);
            errors.push({
              item: deal.properties.dealname || `Deal ${deal.id}`,
              error: errorMsg
            });
            continue;
          }

          // Skip this deal if we don't have a company ID
          if (!companyId) {
            const errorMsg = `No company ID available for deal`;
            console.error(`Skipping deal "${deal.properties.dealname}" - ${errorMsg}`);
            errors.push({
              item: deal.properties.dealname || `Deal ${deal.id}`,
              error: errorMsg
            });
            continue;
          }

          const dealData = {
            name: deal.properties.dealname,
            stage: mappedStage as DealStage, // Cast to DealStage type
            value: deal.properties.amount ? parseFloat(deal.properties.amount) : undefined,
            currency: 'AUD', // Default to AUD
            probability: probability,
            expectedCloseDate: deal.properties.closedate,
            description: deal.properties.description,
            source: 'HubSpot',
            owner: deal.properties.hubspot_owner_id,
            hubspotId: hubspotId,
            companyId: companyId, // Add the company ID
            customFields: Object.keys(customFields).length > 0 ? customFields : undefined
          };

          // Create the deal
          try {
            const createdDeal = this.dealRepository.createDeal(
              dealData,
              'HubSpot' // Source of the data
            );
            if (createdDeal) {
              importedCount++;
              created.push({
                item: deal.properties.dealname
              });
            }
          } catch (error) {
            // If the hubspot_id column doesn't exist yet, try creating without it
            console.log('Error creating deal with HubSpot ID, trying without HubSpot ID:', error);

            // Create a simplified version without the hubspot_id
            const simplifiedDealData = {
              name: deal.properties.dealname,
              stage: mappedStage as DealStage,
              value: deal.properties.amount ? parseFloat(deal.properties.amount) : undefined,
              currency: 'AUD',
              probability: probability, // Use the already parsed probability
              expectedCloseDate: deal.properties.closedate,
              description: deal.properties.description,
              source: 'HubSpot',
              owner: deal.properties.hubspot_owner_id,
              companyId: companyId, // Add the company ID
              customFields: Object.keys(customFields).length > 0 ? customFields : undefined
            };

            const createdDeal = this.dealRepository.createDeal(
              simplifiedDealData,
              'HubSpot' // Source of the data
            );
            if (createdDeal) {
              importedCount++;
              created.push({
                item: deal.properties.dealname
              });
            }
          }
        }
        } catch (dealError) {
          // Handle individual deal errors
          const errorMessage = dealError instanceof Error ? dealError.message : 'Unknown error';
          console.error(`Error processing deal "${deal.properties.dealname}":`, errorMessage);
          errors.push({
            item: deal.properties.dealname || `Deal ${deal.id}`,
            error: errorMessage
          });
        }
      }

      // Record the import in the database
      this.recordImport({
        dealsCount: importedCount,
        status: errors.length > 0 ? 'completed' : 'completed'
      });

      // Log sync completion activity
      activityLogger.logHubSpotSyncCompleted(importedCount, errors).catch(error => {
        console.error('Error logging HubSpot sync completion activity:', error);
      });

      return {
        success: true,
        count: importedCount,
        errors: errors,
        updates: updates,
        created: created
      };
    } catch (error) {
      // Enhanced error logging with more details
      console.error('Error importing deals from HubSpot:', error);

      // Extract more detailed error information if available
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;

        // Log the full error stack for debugging
        console.error('Error stack:', error.stack);

        // If it's an API error, it might have additional details
        if ('response' in error && error.response) {
          const response = error.response as Record<string, unknown>; // Type assertion for safety
          console.error('API Error Response:', response);

          // Try to extract more specific error details from the response
          if ('data' in response && response.data) {
            console.error('API Error Data:', response.data);

            // Some APIs include more detailed error messages in the response data
            if (typeof response.data === 'object' && response.data && 'message' in (response.data as Record<string, unknown>)) {
              errorMessage = `API Error: ${(response.data as Record<string, unknown>).message}`;
            }
          }
        }
      }

      // Record the failed import with the enhanced error message
      this.recordImport({
        status: 'failed',
        errorMessage
      });

      // Log sync failure activity
      activityLogger.logHubSpotSyncFailed(errorMessage).catch(error => {
        console.error('Error logging HubSpot sync failure activity:', error);
      });

      return {
        success: false,
        count: 0,
        errors: errors,
        updates: [],
        created: [],
        error: errorMessage
      };
    }
  }

  /**
   * Import contacts from HubSpot with progress tracking
   */
  async importContacts(): Promise<ImportResult> {
    if (!this.client) {
      return { success: false, count: 0, errors: [], updates: [], created: [], error: 'HubSpot client not initialized' };
    }

    const errors: Array<{ item: string; error: string }> = [];
    const updates: Array<{ item: string; changes: string[] }> = [];
    const created: Array<{ item: string }> = [];

    try {
      // Get contacts from HubSpot using the search API with pagination
      const properties = [
        'firstname',
        'lastname',
        'email',
        'phone',
        'jobtitle',
        'company',
        'createdate',
        'lastmodifieddate'
      ];

      let allContacts: HubSpotObject[] = [];
      let hasMore = true;
      let after = 0;
      const pageSize = 100;
      let totalFound = 0;

      // Emit initial progress
      this.emit('progress', {
        step: 'contacts',
        current: 0,
        total: 0,
        currentItem: 'Fetching contacts from HubSpot...',
        errors: []
      } as ImportProgress);

      // Implement pagination to fetch all contacts
      while (hasMore) {
        const publicObjectSearchRequest = {
          filterGroups: [],
          properties,
          limit: pageSize,
          after: after ? after.toString() : undefined
        };

        const response = await this.client.crm.contacts.searchApi.doSearch(publicObjectSearchRequest);

        // Add the current page of results to our collection
        allContacts = [...allContacts, ...response.results];
        totalFound += response.results.length;

        // Log progress
        console.log(`Fetched page of ${response.results.length} contacts. Total so far: ${totalFound}`);

        // Check if there are more results to fetch
        if (response.paging && response.paging.next && response.paging.next.after) {
          after = parseInt(response.paging.next.after);
        } else {
          hasMore = false;
        }
      }

      console.log(`Found ${allContacts.length} contacts in HubSpot`);

      let importedCount = 0;

      // Process each contact
      for (let i = 0; i < allContacts.length; i++) {
        const contact = allContacts[i];

        // Emit progress for current contact
        this.emit('progress', {
          step: 'contacts',
          current: i + 1,
          total: allContacts.length,
          currentItem: `Processing contact: ${contact.properties.firstname} ${contact.properties.lastname}`,
          errors: errors
        } as ImportProgress);

        try {
        // Get HubSpot ID
        const hubspotId = contact.id;

        // Check if the contact already exists by HubSpot ID
        let existingContact = null;
        try {
          existingContact = this.contactRepository.getContactByHubspotId(hubspotId);
        } catch (error) {
          // If the column doesn't exist yet, just continue with creating a new contact
          console.log('Note: Could not check for existing contact by HubSpot ID, will create new contact');
        }

        if (existingContact) {
          // Update the existing contact
          console.log(`Updating existing contact "${existingContact.firstName} ${existingContact.lastName}" with HubSpot ID ${hubspotId}`);

          // Track what fields will change
          const newData = {
            firstName: contact.properties.firstname || 'Unknown',
            lastName: contact.properties.lastname || 'Unknown',
            email: contact.properties.email,
            phone: contact.properties.phone,
            jobTitle: contact.properties.jobtitle,
            notes: existingContact.notes
          };

          const fieldsToTrack = ['firstName', 'lastName', 'email', 'phone', 'jobTitle'];
          const changes = this.trackChanges(existingContact, newData, fieldsToTrack);

          const updatedContact = this.contactRepository.updateContact(existingContact.id, newData);

          if (updatedContact) {
            importedCount++;
            if (changes.length > 0) {
              updates.push({
                item: `${contact.properties.firstname || 'Unknown'} ${contact.properties.lastname || 'Unknown'}`,
                changes: changes
              });
            }
          }
        } else {
          // Map HubSpot contact to our contact format for new contact
          const contactData = {
            firstName: contact.properties.firstname || 'Unknown',
            lastName: contact.properties.lastname || 'Unknown',
            email: contact.properties.email,
            phone: contact.properties.phone,
            jobTitle: contact.properties.jobtitle,
            notes: `Imported from HubSpot on ${new Date().toLocaleString()}`,
            hubspotId: hubspotId
          };

          // Create the contact
          console.log(`Creating new contact "${contactData.firstName} ${contactData.lastName}" with HubSpot ID ${hubspotId}`);
          try {
            const createdContact = this.contactRepository.createContact(contactData);
            if (createdContact) {
              importedCount++;
              created.push({
                item: `${contact.properties.firstname || 'Unknown'} ${contact.properties.lastname || 'Unknown'}`
              });
            }
          } catch (error) {
            // If the hubspot_id column doesn't exist yet, try creating without it
            console.log('Error creating contact with HubSpot ID, trying without HubSpot ID:', error);

            // Create a simplified version without the hubspot_id
            const simplifiedContactData = {
              firstName: contact.properties.firstname || 'Unknown',
              lastName: contact.properties.lastname || 'Unknown',
              email: contact.properties.email,
              phone: contact.properties.phone,
              jobTitle: contact.properties.jobtitle,
              notes: `Imported from HubSpot on ${new Date().toLocaleString()}`
            };

            const createdContact = this.contactRepository.createContact(simplifiedContactData);
            if (createdContact) {
              importedCount++;
              created.push({
                item: `${contact.properties.firstname || 'Unknown'} ${contact.properties.lastname || 'Unknown'}`
              });
            }
          }
        }
        } catch (contactError) {
          // Handle individual contact errors
          const errorMessage = contactError instanceof Error ? contactError.message : 'Unknown error';
          console.error(`Error processing contact "${contact.properties.firstname} ${contact.properties.lastname}":`, errorMessage);
          errors.push({
            item: `${contact.properties.firstname || 'Unknown'} ${contact.properties.lastname || 'Unknown'}`,
            error: errorMessage
          });
        }
      }

      // Record the import in the database
      this.recordImport({
        contactsCount: importedCount,
        status: 'completed'
      });

      return {
        success: true,
        count: importedCount,
        errors: errors,
        updates: updates,
        created: created
      };
    } catch (error) {
      // Enhanced error logging with more details
      console.error('Error importing contacts from HubSpot:', error);

      // Extract more detailed error information if available
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;

        // Log the full error stack for debugging
        console.error('Error stack:', error.stack);

        // If it's an API error, it might have additional details
        if ('response' in error && error.response) {
          const response = error.response as Record<string, unknown>; // Type assertion for safety
          console.error('API Error Response:', response);

          // Try to extract more specific error details from the response
          if ('data' in response && response.data) {
            console.error('API Error Data:', response.data);

            // Some APIs include more detailed error messages in the response data
            if (typeof response.data === 'object' && response.data && 'message' in (response.data as Record<string, unknown>)) {
              errorMessage = `API Error: ${(response.data as Record<string, unknown>).message}`;
            }
          }
        }
      }

      // Record the failed import with the enhanced error message
      this.recordImport({
        status: 'failed',
        errorMessage
      });

      return {
        success: false,
        count: 0,
        errors: errors,
        updates: [],
        created: [],
        error: errorMessage
      };
    }
  }

  /**
   * Get companies from HubSpot for linking purposes (lightweight version)
   */
  async getCompaniesForLinking(): Promise<Array<{ id: string; name: string; industry?: string; website?: string }>> {
    if (!this.client) {
      throw new Error('HubSpot client not initialized');
    }

    try {
      // Get companies from HubSpot using the search API with pagination
      const properties = [
        'name',
        'industry',
        'website'
      ];

      let allCompanies: HubSpotObject[] = [];
      let hasMore = true;
      let after = 0;
      const pageSize = 100;

      // Implement pagination to fetch all companies
      while (hasMore) {
        const publicObjectSearchRequest = {
          filterGroups: [],
          properties,
          limit: pageSize,
          after: after ? after.toString() : undefined
        };

        const response = await this.client.crm.companies.searchApi.doSearch(publicObjectSearchRequest);

        // Add the current page of results to our collection
        allCompanies = [...allCompanies, ...response.results];

        // Check if there are more results to fetch
        if (response.paging && response.paging.next && response.paging.next.after) {
          after = parseInt(response.paging.next.after);
        } else {
          hasMore = false;
        }
      }

      // Transform to simple format for linking
      return allCompanies.map(company => ({
        id: company.id,
        name: company.properties.name || 'Unknown Company',
        industry: company.properties.industry,
        website: company.properties.website
      }));
    } catch (error) {
      console.error('Error getting HubSpot companies for linking:', error);
      throw error;
    }
  }

  /**
   * Import companies from HubSpot with progress tracking
   */
  async importCompanies(): Promise<ImportResult> {
    if (!this.client) {
      return { success: false, count: 0, errors: [], updates: [], created: [], error: 'HubSpot client not initialized' };
    }

    const errors: Array<{ item: string; error: string }> = [];
    const updates: Array<{ item: string; changes: string[] }> = [];
    const created: Array<{ item: string }> = [];

    try {
      // Get companies from HubSpot using the search API with pagination
      const properties = [
        'name',
        'industry',
        'numberofemployees',
        'website',
        'address',
        'description',
        'createdate',
        'hs_lastmodifieddate'
      ];

      let allCompanies: HubSpotObject[] = [];
      let hasMore = true;
      let after = 0;
      const pageSize = 100;
      let totalFound = 0;

      // Emit initial progress
      this.emit('progress', {
        step: 'companies',
        current: 0,
        total: 0,
        currentItem: 'Fetching companies from HubSpot...',
        errors: []
      } as ImportProgress);

      // Implement pagination to fetch all companies
      while (hasMore) {
        const publicObjectSearchRequest = {
          filterGroups: [],
          properties,
          limit: pageSize,
          after: after ? after.toString() : undefined
        };

        const response = await this.client.crm.companies.searchApi.doSearch(publicObjectSearchRequest);

        // Add the current page of results to our collection
        allCompanies = [...allCompanies, ...response.results];
        totalFound += response.results.length;

        // Log progress
        console.log(`Fetched page of ${response.results.length} companies. Total so far: ${totalFound}`);

        // Check if there are more results to fetch
        if (response.paging && response.paging.next && response.paging.next.after) {
          after = parseInt(response.paging.next.after);
        } else {
          hasMore = false;
        }
      }

      console.log(`Found ${allCompanies.length} companies in HubSpot`);

      let importedCount = 0;

      // Process each company
      for (let i = 0; i < allCompanies.length; i++) {
        const company = allCompanies[i];

        // Emit progress for current company
        this.emit('progress', {
          step: 'companies',
          current: i + 1,
          total: allCompanies.length,
          currentItem: `Processing company: ${company.properties.name}`,
          errors: errors
        } as ImportProgress);

        try {
        // Get HubSpot ID
        const hubspotId = company.id;

        // Check if the company already exists by HubSpot ID
        let existingCompany = null;
        try {
          existingCompany = this.companyRepository.getCompanyByHubspotId(hubspotId);
        } catch (error) {
          // If the column doesn't exist yet, just continue with creating a new company
          console.log('Note: Could not check for existing company by HubSpot ID, will create new company');
        }

        if (existingCompany) {
          // Update the existing company
          console.log(`Updating existing company "${existingCompany.name}" with HubSpot ID ${hubspotId}`);

          // Track what fields will change
          const newData = {
            name: company.properties.name || 'Unknown Company',
            industry: company.properties.industry,
            size: company.properties.numberofemployees,
            website: company.properties.website,
            address: company.properties.address,
            description: company.properties.description
          };

          const fieldsToTrack = ['name', 'industry', 'size', 'website', 'address', 'description'];
          const changes = this.trackChanges(existingCompany, newData, fieldsToTrack);

          const updatedCompany = this.companyRepository.updateCompany(existingCompany.id, newData);

          if (updatedCompany) {
            importedCount++;
            if (changes.length > 0) {
              updates.push({
                item: company.properties.name || 'Unknown Company',
                changes: changes
              });
            }
          }
        } else {
          // Map HubSpot company to our company format for new company
          const companyData = {
            name: company.properties.name || 'Unknown Company',
            industry: company.properties.industry,
            size: company.properties.numberofemployees,
            website: company.properties.website,
            address: company.properties.address,
            description: company.properties.description,
            hubspotId: hubspotId
          };

          // Create the company
          console.log(`Creating new company "${companyData.name}" with HubSpot ID ${hubspotId}`);
          try {
            const createdCompany = this.companyRepository.createCompany(companyData, 'HubSpot');
            if (createdCompany) {
              importedCount++;
              created.push({
                item: company.properties.name || 'Unknown Company'
              });
            }
          } catch (error) {
            // If the hubspot_id column doesn't exist yet, try creating without it
            console.log('Error creating company with HubSpot ID, trying without HubSpot ID:', error);

            // Create a simplified version without the hubspot_id
            const simplifiedCompanyData = {
              name: company.properties.name || 'Unknown Company',
              industry: company.properties.industry,
              size: company.properties.numberofemployees,
              website: company.properties.website,
              address: company.properties.address,
              description: company.properties.description
            };

            const createdCompany = this.companyRepository.createCompany(simplifiedCompanyData, 'HubSpot');
            if (createdCompany) {
              importedCount++;
              created.push({
                item: company.properties.name || 'Unknown Company'
              });
            }
          }
        }
        } catch (companyError) {
          // Handle individual company errors
          const errorMessage = companyError instanceof Error ? companyError.message : 'Unknown error';
          console.error(`Error processing company "${company.properties.name}":`, errorMessage);
          errors.push({
            item: company.properties.name || `Company ${company.id}`,
            error: errorMessage
          });
        }
      }

      // Record the import in the database
      this.recordImport({
        companiesCount: importedCount,
        status: 'completed'
      });

      return {
        success: true,
        count: importedCount,
        errors: errors,
        updates: updates,
        created: created
      };
    } catch (error) {
      // Enhanced error logging with more details
      console.error('Error importing companies from HubSpot:', error);

      // Extract more detailed error information if available
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;

        // Log the full error stack for debugging
        console.error('Error stack:', error.stack);

        // If it's an API error, it might have additional details
        if ('response' in error && error.response) {
          const response = error.response as Record<string, unknown>; // Type assertion for safety
          console.error('API Error Response:', response);

          // Try to extract more specific error details from the response
          if ('data' in response && response.data) {
            console.error('API Error Data:', response.data);

            // Some APIs include more detailed error messages in the response data
            if (typeof response.data === 'object' && response.data && 'message' in (response.data as Record<string, unknown>)) {
              errorMessage = `API Error: ${(response.data as Record<string, unknown>).message}`;
            }
          }
        }
      }

      // Record the failed import with the enhanced error message
      this.recordImport({
        status: 'failed',
        errorMessage
      });

      return {
        success: false,
        count: 0,
        errors: errors,
        updates: [],
        created: [],
        error: errorMessage
      };
    }
  }

  /**
   * Import all data from HubSpot with progress tracking
   */
  async importAllWithProgress(): Promise<{
    totalCount: number;
    results: {
      companies: ImportResult;
      deals: ImportResult;
      contacts: ImportResult;
    };
  }> {
    const results = {
      companies: { success: false, count: 0, errors: [] } as ImportResult,
      deals: { success: false, count: 0, errors: [] } as ImportResult,
      contacts: { success: false, count: 0, errors: [] } as ImportResult
    };

    try {
      // Import companies first
      console.log('Starting HubSpot companies import...');
      results.companies = await this.importCompanies();

      if (results.companies.success) {
        console.log(`Successfully imported ${results.companies.count} companies`);

        // Import deals (which will now properly link to companies)
        console.log('Starting HubSpot deals import...');
        results.deals = await this.importDeals();

        if (results.deals.success) {
          console.log(`Successfully imported ${results.deals.count} deals`);

          // Import contacts
          console.log('Starting HubSpot contacts import...');
          results.contacts = await this.importContacts();

          if (results.contacts.success) {
            console.log(`Successfully imported ${results.contacts.count} contacts`);
          }
        }
      }

      const totalCount = results.companies.count + results.deals.count + results.contacts.count;

      return {
        totalCount,
        results
      };
    } catch (error) {
      console.error('Error during HubSpot import all:', error);
      throw error;
    }
  }

  /**
   * Record an import in the database
   */
  private recordImport(data: {
    dealsCount?: number;
    contactsCount?: number;
    companiesCount?: number;
    status: 'pending' | 'completed' | 'failed';
    errorMessage?: string;
  }): void {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      // Check if the table exists
      const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='hubspot_import'").get();
      if (!tableExists) {
        // Table doesn't exist yet, create it
        db.prepare(`
          CREATE TABLE hubspot_import (
            id TEXT PRIMARY KEY,
            import_date TEXT NOT NULL,
            created_at TEXT NOT NULL,
            status TEXT NOT NULL,
            deals_count INTEGER,
            contacts_count INTEGER,
            companies_count INTEGER,
            error_message TEXT
          )
        `).run();
      }

      // Check the table schema to ensure it has the created_at column
      const tableInfo = db.prepare("PRAGMA table_info(hubspot_import)").all();
      const columnNames = tableInfo.map((column: any) => column.name);

      // If the table doesn't have the created_at column, add it
      if (!columnNames.includes('created_at')) {
        console.log('Adding created_at column to hubspot_import table');
        db.prepare("ALTER TABLE hubspot_import ADD COLUMN created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP").run();
      }

      db.prepare(`
        INSERT INTO hubspot_import (
          id, import_date, created_at, status, deals_count, contacts_count, companies_count, error_message
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        now,
        now, // Add created_at value
        data.status,
        data.dealsCount || null,
        data.contactsCount || null,
        data.companiesCount || null,
        data.errorMessage || null
      );
    } catch (error) {
      console.error('Error recording HubSpot import:', error);
    }
  }

  /**
   * Get all import history
   */
  getImportHistory(): HubSpotImport[] {
    try {
      // Check if the table exists
      const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='hubspot_import'").get();
      if (!tableExists) {
        // Table doesn't exist yet, return empty array
        return [];
      }

      const imports = db.prepare(`
        SELECT
          id,
          import_date as importDate,
          created_at as createdAt,
          status,
          deals_count as dealsCount,
          contacts_count as contactsCount,
          companies_count as companiesCount,
          error_message as errorMessage
        FROM hubspot_import
        ORDER BY import_date DESC
      `).all() as HubSpotImport[];

      return imports;
    } catch (error) {
      console.error('Error getting HubSpot import history:', error);
      return [];
    }
  }
}

// Create and export a singleton instance
export const hubspotService = new HubSpotService();
