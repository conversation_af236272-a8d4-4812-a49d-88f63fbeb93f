import axios, { AxiosInstance, AxiosResponse } from 'axios';

/**
 * Interface for Harvest invoice
 */
// Define a more specific LineItem interface based on docs
interface HarvestLineItem {
  id: number;
  project?: { // Project is optional on a line item
    id: number;
    name: string;
  };
  kind: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
  taxed: boolean;
  taxed2: boolean;
}

export interface HarvestInvoice {
  id: number;
  // client_id: number; // Redundant, info is in client object
  // client_name: string; // Incorrect, client is an object
  client: { // Correct structure based on docs
    id: number;
    name: string;
  };
  // project_id?: number; // Incorrect, project info is in line_items
  // project_name?: string; // Incorrect, project info is in line_items
  line_items: HarvestLineItem[]; // Add line_items array
  number: string;
  purchase_order: string;
  amount: number;
  due_amount: number;
  tax: number;
  tax_amount: number;
  tax2: number;
  tax2_amount: number;
  discount: number;
  discount_amount: number;
  subject: string;
  notes: string;
  currency: string;
  state: string;
  period_start: string;
  period_end: string;
  issue_date: string;
  due_date: string;
  payment_term: string;
  sent_at: string;
  paid_at: string;
  paid_date: string;
  closed_at: string;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for Harvest project
 */
export interface HarvestProject {
  id: number;
  client_id: number;
  name: string;
  code: string;
  is_active: boolean;
  is_billable: boolean;
  is_fixed_fee: boolean;
  bill_by: string;
  budget: number;
  budget_by: string;
  budget_is_monthly: boolean;
  starts_on: string | null;
  ends_on: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for Project Budget Report result
 */
export interface ProjectBudgetResult {
  project_id: number;
  project_name: string;
  client_id: number;
  client_name: string;
  budget_is_monthly: boolean;
  budget_by: string;
  is_active: boolean;
  budget: number;
  budget_spent: number;
  budget_remaining: number;
}

/**
 * Interface for Uninvoiced Report result
 */
export interface UninvoicedResult {
  project_id: number;
  project_name: string;
  client_id: number;
  client_name: string;
  uninvoiced_amount: number;
  uninvoiced_hours: number;
}

/**
 * Interface for Harvest API response
 */
export interface HarvestApiResponse<T> {
  invoices?: T[];
  projects?: T[];
  results?: T[];
  total_pages?: number;
  total_entries?: number;
  next_page?: number;
  previous_page?: number;
  page?: number;
  links?: {
    first?: string;
    next?: string;
    previous?: string;
    last?: string;
  };
}

/**
 * Harvest client for API interactions
 */
export class HarvestClient {
  private baseUrl = 'https://api.harvestapp.com/v2';
  private axiosInstance: AxiosInstance;

  /**
   * Constructor
   */
  constructor() {
    console.log('Initializing HarvestClient...');

    // Get tokens from environment variables
    const accessToken = process.env.HARVEST_ACCESS_TOKEN;
    const accountId = process.env.HARVEST_ACCOUNT_ID;

    console.log(`Harvest credentials: Token=${accessToken ? 'Present' : 'Missing'}, Account ID=${accountId ? 'Present' : 'Missing'}`);

    if (!accessToken || !accountId) {
      console.error('Harvest credentials not configured');
      throw new Error('Harvest credentials not configured. Please set HARVEST_ACCESS_TOKEN and HARVEST_ACCOUNT_ID environment variables.');
    }

    // Create axios instance with default headers
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Harvest-Account-Id': accountId,
        'User-Agent': 'Onbord Financial Analysis (<EMAIL>)',
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Get clients from Harvest
   * @returns List of clients
   */
  async getClients(): Promise<{ id: number; name: string }[]> {
    try {
      console.log('Fetching clients from Harvest API');

      const response: AxiosResponse<{ clients?: { id: number; name: string }[] }> =
        await this.axiosInstance.get('/clients', {
          params: {
            per_page: 100 // Maximum allowed by Harvest
          }
        });

      if (response.data.clients) {
        console.log(`Found ${response.data.clients.length} clients`);
        return response.data.clients.map(client => ({
          id: client.id,
          name: client.name
        }));
      } else {
        console.warn('No clients found in response');
        return [];
      }
    } catch (error: any) {
      console.error('Error fetching clients from Harvest API:', error.response?.data || error.message);
      throw new Error(`Failed to get clients from Harvest: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get users from Harvest
   * @param isActive Only return active users if true
   * @returns List of users
   */
  async getUsers(isActive?: boolean): Promise<any[]> {
    try {
      console.log(`Fetching ${isActive ? 'active' : 'all'} users from Harvest API`);

      // Construct params object conditionally
      const params: { [key: string]: any } = {
        per_page: 100 // Maximum allowed by Harvest
      };

      // Only add is_active parameter if it's explicitly defined
      if (isActive !== undefined) {
        params.is_active = isActive;
      }

      const response: AxiosResponse<{ users?: any[] }> =
        await this.axiosInstance.get('/users', { params });

      if (response.data.users) {
        console.log(`Found ${response.data.users.length} users`);

        // For each user, try to fetch their cost rate
        const usersWithCostRates = await Promise.all(
          response.data.users.map(async (user) => {
            try {
              // Only fetch cost rates for active users to avoid unnecessary API calls
              if (user.is_active) {
                const costRates = await this.getUserCostRates(user.id);
                // Add the current cost rate to the user object if available
                if (costRates && costRates.length > 0) {
                  // Find the most recent cost rate without an end_date (current rate)
                  const currentRate = costRates.find(rate => !rate.end_date);
                  if (currentRate) {
                    user.cost_rate = currentRate.amount;
                  } else {
                    // If no current rate, use the most recent one (last in the array)
                    user.cost_rate = costRates[costRates.length - 1].amount;
                  }
                }
              }
            } catch (costRateError) {
              console.warn(`Could not fetch cost rates for user ${user.id}:`, costRateError);
              // Continue without cost rate data
            }
            return user;
          })
        );

        return usersWithCostRates;
      } else {
        console.warn('No users found in response');
        return [];
      }
    } catch (error: any) {
      console.error('Error fetching users from Harvest API:', error.response?.data || error.message);
      throw new Error(`Failed to get users from Harvest: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get cost rates for a specific user
   * @param userId The ID of the user
   * @returns List of cost rates for the user
   */
  async getUserCostRates(userId: number): Promise<any[]> {
    try {
      console.log(`Fetching cost rates for user ${userId} from Harvest API`);

      const response: AxiosResponse<{ cost_rates?: any[] }> =
        await this.axiosInstance.get(`/users/${userId}/cost_rates`, {
          params: {
            per_page: 100 // Maximum allowed by Harvest
          }
        });

      if (response.data.cost_rates) {
        console.log(`Found ${response.data.cost_rates.length} cost rates for user ${userId}`);
        return response.data.cost_rates;
      } else {
        console.warn(`No cost rates found for user ${userId}`);
        return [];
      }
    } catch (error: any) {
      // If we get a 403 Forbidden, it means the user doesn't have permission to access cost rates
      if (error.response?.status === 403) {
        console.warn(`No permission to access cost rates for user ${userId}`);
        return [];
      }

      console.error(`Error fetching cost rates for user ${userId} from Harvest API:`, error.response?.data || error.message);
      throw new Error(`Failed to get cost rates for user ${userId} from Harvest: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get the currently authenticated user from Harvest
   * @returns Current user details
   */
  async getCurrentUser(): Promise<any> {
    try {
      console.log('Fetching current user from Harvest API');

      const response: AxiosResponse<any> = await this.axiosInstance.get('/users/me');

      console.log(`Successfully fetched current user: ${response.data.first_name} ${response.data.last_name}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching current user from Harvest API:', error.response?.data || error.message);
      throw new Error(`Failed to get current user from Harvest: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Create an estimate in Harvest
   * @param estimateData The estimate data to create
   * @returns The created estimate
   */
  async createEstimate(estimateData: any): Promise<any> {
    try {
      console.log('Creating estimate in Harvest API', estimateData);

      const response: AxiosResponse<any> = await this.axiosInstance.post('/estimates', estimateData);

      console.log(`Successfully created estimate with ID: ${response.data.id}`);
      return response.data;
    }
    catch (error: any) {
      console.error('Error creating estimate in Harvest API:', error.response?.data || error.message);
      throw new Error(`Failed to create estimate in Harvest: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get estimates from Harvest
   * @param params Query parameters
   * @returns List of estimates
   */
  async getEstimates(params: {
    client_id?: number | string;
    from?: string;
    to?: string;
    state?: string;
    page?: number;
    per_page?: number;
  } = {}): Promise<any[]> {
    try {
      console.log('Fetching estimates from Harvest API with params:', params);

      // Build parameters with defaults
      const queryParams = {
        ...params,
        per_page: params.per_page || 100,
      };

      // Get estimates with pagination
      let allEstimates: any[] = [];
      let currentPage = 1;
      let totalPages = 1;

      do {
        const response: AxiosResponse<{ estimates?: any[], total_pages?: number }> =
          await this.axiosInstance.get('/estimates', {
            params: {
              ...queryParams,
              page: currentPage
            }
          });

        if (response.data.estimates && response.data.estimates.length > 0) {
          allEstimates = [...allEstimates, ...response.data.estimates];
          totalPages = response.data.total_pages || 1;
          currentPage++;
        } else {
          break;
        }
      } while (currentPage <= totalPages);

      console.log(`Successfully fetched ${allEstimates.length} estimates`);
      return allEstimates;
    }
    catch (error: any) {
      // Handle rate limiting more gracefully
      if (error.response?.status === 429) {
        console.error('Rate limit exceeded while fetching estimates. Returning partial results.');
        return []; // Return empty array rather than failing completely
      }

      console.error('Error fetching estimates from Harvest API:', error.response?.data || error.message);
      throw new Error(`Failed to fetch estimates from Harvest: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get a single estimate by ID
   * @param estimateId The ID of the estimate to retrieve
   * @returns The estimate details
   */
  async getEstimate(estimateId: number): Promise<any> {
    try {
      console.log(`Fetching estimate ${estimateId} from Harvest API`);

      const response: AxiosResponse<any> = await this.axiosInstance.get(`/estimates/${estimateId}`);

      console.log(`Successfully fetched estimate ${estimateId}`);
      return response.data;
    }
    catch (error: any) {
      console.error(`Error fetching estimate ${estimateId} from Harvest API:`, error.response?.data || error.message);
      throw new Error(`Failed to fetch estimate from Harvest: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get invoices from Harvest
   * @param params Query parameters
   * @returns Invoices
   */
  async getInvoices(params: {
    from?: string;
    to?: string;
    state?: string;
    page?: number;
    per_page?: number;
  } = {}): Promise<HarvestInvoice[]> {
    try {
      console.log('Harvest API request params:', JSON.stringify(params));

      // For debugging, let's try to get all invoices first
      console.log('Fetching all invoices without filters...');
      const allResponse: AxiosResponse<HarvestApiResponse<HarvestInvoice>> = await this.axiosInstance.get('/invoices', {
        params: {
          per_page: 100 // Maximum allowed by Harvest
        }
      });

      if (allResponse.data.invoices) {
        console.log(`Found ${allResponse.data.invoices.length} total invoices`);

        if (allResponse.data.invoices.length > 0) {
          const invoice = allResponse.data.invoices[0];
          console.log('Sample invoice:');
          console.log(`- ID: ${invoice.id}`);
          console.log(`- Number: ${invoice.number}`);
          // Access client info via nested object
          console.log(`- Client ID: ${invoice.client?.id}`);
          console.log(`- Client Name: ${invoice.client?.name}`);
          // Access project info via first line item
          console.log(`- Project ID: ${invoice.line_items?.[0]?.project?.id || 'None'}`);
          console.log(`- Project Name: ${invoice.line_items?.[0]?.project?.name || 'None'}`);
          console.log(`- Amount: ${invoice.amount}`);
          console.log(`- State: ${invoice.state}`);
          console.log(`- Issue Date: ${invoice.issue_date}`);
          console.log(`- Due Date: ${invoice.due_date}`);

          // Log if this is a "non-project" invoice (check first line item)
          if (!invoice.line_items?.[0]?.project) {
            console.log('WARNING: Found invoice without project info in first line item - this might appear as "Unknown Project"');
          }
        }

        // List all available states
        const states = new Set();
        allResponse.data.invoices.forEach(invoice => states.add(invoice.state));
        console.log('Available invoice states:', Array.from(states).join(', '));

        // If we're not filtering by state, return all invoices
        if (!params.state) {
          return allResponse.data.invoices;
        }
      }

      // Get the requested states
      const requestedStates = params.state ? params.state.split(',') : ['draft', 'open'];
      console.log(`Fetching invoices with states: ${requestedStates.join(', ')}`);

      // Create base params without state
      const baseParams = { ...params };
      delete baseParams.state;

      // Get invoices for each state
      let allInvoices: HarvestInvoice[] = [];

      for (const state of requestedStates) {
        console.log(`Fetching invoices with state=${state}...`);

        // Get invoices with pagination
        let currentPage = 1;
        let totalPages = 1;

        do {
          console.log(`Fetching page ${currentPage} for state ${state}...`);

          try {
            const response: AxiosResponse<HarvestApiResponse<HarvestInvoice>> = await this.axiosInstance.get('/invoices', {
              params: {
                ...baseParams,
                state,
                page: currentPage,
                per_page: 100 // Maximum allowed by Harvest
              }
            });

            console.log(`Response status: ${response.status}`);

            // Add invoices to result
            if (response.data.invoices) {
              console.log(`Found ${response.data.invoices.length} invoices with state=${state} on page ${currentPage}`);
              allInvoices = [...allInvoices, ...response.data.invoices];
            } else {
              console.log('No invoices property in response data');
            }

            // Update pagination info
            totalPages = response.data.total_pages || 1;
            currentPage++;
          } catch (pageError: any) {
            console.error(`Error fetching page ${currentPage} for state ${state}:`, pageError.response?.data || pageError.message);
            break;
          }

        } while (currentPage <= totalPages);
      }

      console.log(`Total invoices found across all states: ${allInvoices.length}`);
      return allInvoices;
    } catch (error: any) {
      console.error('Error fetching invoices from Harvest:', error.response?.data || error.message);
      throw new Error('Failed to fetch invoices from Harvest');
    }
  }

  /**
   * Get a single invoice by ID
   * @param invoiceId Invoice ID
   * @returns Invoice
   */
  async getInvoice(invoiceId: number): Promise<HarvestInvoice> {
    try {
      const response: AxiosResponse<HarvestInvoice> = await this.axiosInstance.get(`/invoices/${invoiceId}`);
      return response.data;
    } catch (error: any) {
      console.error(`Error fetching invoice ${invoiceId} from Harvest:`, error.response?.data || error.message);
      throw new Error(`Failed to fetch invoice ${invoiceId} from Harvest`);
    }
  }

  /**
   * Get projects from Harvest
   * @param params Query parameters
   * @returns Projects
   */
  async getProjects(params: {
    is_active?: boolean;
    client_id?: number;
    page?: number;
    per_page?: number;
  } = {}): Promise<HarvestProject[]> {
    try {
      console.log('Fetching projects from Harvest API');

      const response: AxiosResponse<HarvestApiResponse<HarvestProject>> = await this.axiosInstance.get('/projects', {
        params: {
          ...params,
          per_page: params.per_page || 100
        }
      });

      if (response.data.projects) {
        console.log(`Found ${response.data.projects.length} projects`);
        return response.data.projects;
      } else {
        console.warn('No projects found in response');
        return [];
      }
    } catch (error: any) {
      // Check for rate limiting error
      if (error.response?.status === 429 || error.response?.data?.message?.includes('throttled')) {
        const retryAfter = error.response?.data?.retry_after || 60;
        console.error(`Rate limit exceeded. Retry after ${retryAfter} seconds.`);
        console.error('Error fetching projects from Harvest API:', error.response?.data || error.message);

        // Return empty response instead of failing completely
        return [];
      } else {
        console.error('Error fetching projects from Harvest API:', error.response?.data || error.message);
        throw new Error(`Failed to get projects from Harvest`);
      }
    }
  }

  /**
   * Get project budget report from Harvest
   * @param params Query parameters
   * @returns Project budget report
   */
  async getProjectBudgetReport(params: {
    is_active?: boolean;
    page?: number;
    per_page?: number;
  } = {}): Promise<HarvestApiResponse<ProjectBudgetResult>> {
    try {
      console.log('Fetching project budget report from Harvest API');

      const response: AxiosResponse<HarvestApiResponse<ProjectBudgetResult>> = await this.axiosInstance.get('/reports/project_budget', {
        params: {
          ...params,
          per_page: params.per_page || 100
        }
      });

      console.log(`Found ${response.data.results?.length || 0} project budget results`);
      return response.data;
    } catch (error: any) {
      // Check for rate limiting error
      if (error.response?.status === 429 || error.response?.data?.message?.includes('throttled')) {
        const retryAfter = error.response?.data?.retry_after || 60;
        console.error(`Rate limit exceeded. Retry after ${retryAfter} seconds.`);
        console.error('Error fetching project budget report from Harvest API:', error.response?.data || error.message);

        // Return empty response instead of failing completely
        return {
          results: [],
          total_entries: 0,
          total_pages: 0,
          page: 1
        };
      } else {
        console.error('Error fetching project budget report from Harvest API:', error.response?.data || error.message);
        throw new Error(`Failed to get project budget report from Harvest`);
      }
    }
  }

  /**
   * Get time report from Harvest
   * @param reportType Type of time report (team, clients, projects, tasks)
   * @param params Query parameters
   * @returns Time report data
   */
  async getTimeReport(
    reportType: 'team' | 'clients' | 'projects' | 'tasks',
    params: {
      from: string; // Required parameter per Harvest API
      to: string;   // Required parameter per Harvest API
      include_fixed_fee?: boolean; // Whether to include fixed fee projects in billable amounts
      page?: number;
      per_page?: number;
    }
  ): Promise<HarvestApiResponse<any>> {
    try {
      // Validate required parameters
      if (!params.from) {
        throw new Error('The "from" parameter is required for time reports');
      }
      if (!params.to) {
        throw new Error('The "to" parameter is required for time reports');
      }

      console.log(`Fetching ${reportType} time report from Harvest API with from=${params.from} to=${params.to}`);

      const response: AxiosResponse<HarvestApiResponse<any>> = await this.axiosInstance.get(`/reports/time/${reportType}`, {
        params: {
          ...params,
          per_page: params.per_page || 100,
          include_fixed_fee: params.include_fixed_fee || false
        }
      });

      console.log(`Found ${response.data.results?.length || 0} ${reportType} time report results`);
      return response.data;
    } catch (error: any) {
      // Check for rate limiting error
      if (error.response?.status === 429 || error.response?.data?.message?.includes('throttled')) {
        const retryAfter = error.response?.data?.retry_after || 60;
        console.error(`Rate limit exceeded. Retry after ${retryAfter} seconds.`);
        console.error(`Error fetching ${reportType} time report from Harvest API:`, error.response?.data || error.message);

        // Return empty response instead of failing completely
        return {
          results: [],
          total_entries: 0,
          total_pages: 0,
          page: 1
        };
      } else if (error.response?.data?.message?.includes('Parameter is missing or the value is empty: from')) {
        console.error('Error fetching time report: Missing required "from" parameter');
        throw new Error('Failed to get time report: The "from" parameter is required (YYYY-MM-DD format)');
      } else if (error.response?.data?.message?.includes('Parameter is missing or the value is empty: to')) {
        console.error('Error fetching time report: Missing required "to" parameter');
        throw new Error('Failed to get time report: The "to" parameter is required (YYYY-MM-DD format)');
      } else {
        console.error(`Error fetching ${reportType} time report from Harvest API:`, error.response?.data || error.message);
        throw new Error(`Failed to get ${reportType} time report from Harvest: ${error.response?.data?.message || error.message}`);
      }
    }
  }

  /**
   * Get uninvoiced report from Harvest
   * @param params Query parameters
   * @param params.from Required - Start date in YYYY-MM-DD format
   * @param params.to Optional - End date in YYYY-MM-DD format
   * @param params.page Optional - Page number for pagination
   * @param params.per_page Optional - Results per page
   * @returns Uninvoiced report
   */
  /**
   * Get time entries from Harvest
   * @param params Query parameters
   * @param params.from Required - Start date in YYYY-MM-DD format
   * @param params.to Required - End date in YYYY-MM-DD format
   * @param params.user_id Optional - Filter by user ID
   * @param params.page Optional - Page number for pagination
   * @param params.per_page Optional - Results per page
   * @returns Time entries
   */
  async getTimeEntries(params: {
    from: string;
    to: string;
    user_id?: number;
    page?: number;
    per_page?: number;
  }): Promise<any[]> {
    try {
      // Validate required parameters
      if (!params.from) {
        throw new Error('The "from" parameter is required for time entries');
      }
      if (!params.to) {
        throw new Error('The "to" parameter is required for time entries');
      }

      console.log(`Fetching time entries from Harvest API with from=${params.from} to=${params.to}`);

      // Initialize an empty array to store all time entries
      let allTimeEntries: any[] = [];
      let currentPage = 1;
      let totalPages = 1;

      // Fetch all pages of time entries
      do {
        const response: AxiosResponse<any> = await this.axiosInstance.get('/time_entries', {
          params: {
            ...params,
            page: currentPage,
            per_page: params.per_page || 100
          }
        });

        if (response.data.time_entries) {
          allTimeEntries = [...allTimeEntries, ...response.data.time_entries];
        }

        totalPages = response.data.total_pages || 1;
        currentPage++;
      } while (currentPage <= totalPages);

      console.log(`Found ${allTimeEntries.length} time entries`);
      return allTimeEntries;
    } catch (error: any) {
      console.error('Error fetching time entries from Harvest API:', error.response?.data || error.message);
      throw new Error(`Failed to get time entries from Harvest: ${error.response?.data?.message || error.message}`);
    }
  }

  async getUninvoicedReport(params: {
    from: string; // Required parameter per Harvest API
    to: string;   // Required parameter per Harvest API
    page?: number;
    per_page?: number;
  }): Promise<HarvestApiResponse<UninvoicedResult>> {
    try {
      // Validate required parameters
      if (!params.from) {
        throw new Error('The "from" parameter is required for the uninvoiced report API');
      }
      if (!params.to) {
        throw new Error('The "to" parameter is required for the uninvoiced report API');
      }

      console.log(`Fetching uninvoiced report from Harvest API with from=${params.from} to=${params.to}`);

      const response: AxiosResponse<HarvestApiResponse<UninvoicedResult>> = await this.axiosInstance.get('/reports/uninvoiced', {
        params: {
          ...params,
          per_page: params.per_page || 100
        }
      });

      console.log(`Found ${response.data.results?.length || 0} uninvoiced results`);
      return response.data;
    } catch (error: any) {
      // Check for rate limiting error
      if (error.response?.status === 429 || error.response?.data?.message?.includes('throttled')) {
        const retryAfter = error.response?.data?.retry_after || 60;
        console.error(`Rate limit exceeded. Retry after ${retryAfter} seconds.`);
        console.error('Error fetching uninvoiced report from Harvest API:', error.response?.data || error.message);

        // Return empty response instead of failing completely
        return {
          results: [],
          total_entries: 0,
          total_pages: 0,
          page: 1
        };
      } else if (error.response?.data?.message?.includes('Parameter is missing or the value is empty: from')) {
        console.error('Error fetching uninvoiced report: Missing required "from" parameter');
        throw new Error('Failed to get uninvoiced report: The "from" parameter is required (YYYY-MM-DD format)');
      } else {
        console.error('Error fetching uninvoiced report from Harvest API:', error.response?.data || error.message);
        throw new Error(`Failed to get uninvoiced report from Harvest: ${error.response?.data?.message || error.message}`);
      }
    }
  }
}

/**
 * Create a Harvest client instance
 */
export const createHarvestClient = (): HarvestClient => {
  return new HarvestClient();
};