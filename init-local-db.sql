-- Initialize the estimate_drafts table
CREATE TABLE IF NOT EXISTS estimate_drafts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  client_id INTEGER NOT NULL,
  client_name TEXT NOT NULL,
  project_name TEXT NOT NULL,
  start_date TEXT NOT NULL,
  end_date TEXT NOT NULL,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  harvest_estimate_id INTEGER,
  user_id TEXT NOT NULL,
  notes TEXT,
  status TEXT NOT NULL DEFAULT 'draft',
  discount_type TEXT NOT NULL DEFAULT 'none',
  discount_value REAL NOT NULL DEFAULT 0,
  invoice_frequency TEXT,
  payment_terms INTEGER
);

-- Initialize the estimate_draft_allocations table with the correct schema
CREATE TABLE IF NOT EXISTS estimate_draft_allocations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  estimate_draft_uuid TEXT NOT NULL,
  internal_id TEXT UNIQUE NOT NULL,
  harvest_user_id INTEGER NOT NULL,
  first_name TEXT NOT NULL,
  last_name TEXT,
  project_role TEXT,
  level TEXT,
  onbord_target_rate_daily REAL NOT NULL,
  onbord_cost_rate_daily REAL NOT NULL,
  rate_proposed_daily REAL NOT NULL,
  FOREIGN KEY (estimate_draft_uuid) REFERENCES estimate_drafts(uuid) ON DELETE CASCADE
);

-- Initialize the estimate_draft_time_allocations table
CREATE TABLE IF NOT EXISTS estimate_draft_time_allocations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  estimate_draft_allocation_id INTEGER NOT NULL,
  week_starting TEXT NOT NULL,
  days_allocated REAL NOT NULL,
  FOREIGN KEY (estimate_draft_allocation_id) REFERENCES estimate_draft_allocations(id) ON DELETE CASCADE
);

-- Verify the schema was created successfully
PRAGMA table_info(estimate_drafts);
PRAGMA table_info(estimate_draft_allocations);
PRAGMA table_info(estimate_draft_time_allocations);
