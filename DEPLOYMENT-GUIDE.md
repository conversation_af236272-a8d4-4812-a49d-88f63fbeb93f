# Deployment Guide

This guide provides instructions for deploying the Onbord Financial Dashboard to Render using pre-built branches.

## Quick Start

Deploy to preview environment:

```bash
npm run deploy:preview
```

Deploy to production environment:

```bash
npm run deploy:production
```

## Deployment Process

The deployment uses a unified script (`scripts/deploy-v2.sh`) that:

1. **Validates** your current branch and working directory
2. **Builds** the application with automatic error recovery
3. **Creates** a deployment package with minimal dependencies
4. **Pushes** to pre-built branches that trigger Render deployments

## Branch Strategy

- **Development**: `preview` (staging), `main` (production)
- **Deployment**: `pre-built-preview`, `pre-built-production`

The pre-built branches contain only compiled code and are managed automatically by the deployment script.

## Environment Configuration

### Local Development

Create a `.env` file from `.env.example` with your API credentials.

### Render Environment Variables

Set these in the Render dashboard:

- `NODE_ENV=production`
- `HARVEST_ACCESS_TOKEN`
- `HARVEST_ACCOUNT_ID`
- `XERO_CLIENT_ID`
- `XERO_CLIENT_SECRET`
- `SESSION_SECRET`

## Troubleshooting

### Build Failures

The deployment script automatically attempts to fix common issues like missing TypeScript types.

### Rollback

To rollback a deployment:

```bash
git checkout pre-built-preview  # or pre-built-production
git log --oneline -10
git reset --hard <commit-hash>
git push -f origin pre-built-preview
```

## Detailed Documentation

For comprehensive deployment information, see [docs/DEPLOYMENT-V2.md](docs/DEPLOYMENT-V2.md).
