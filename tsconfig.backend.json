{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "resolveJsonModule": true, "strict": false, "skipLibCheck": true, "noImplicitAny": false, "noImplicitThis": false, "noEmitOnError": false, "outDir": "dist", "baseUrl": ".", "sourceMap": true, "declaration": false, "paths": {"@/*": ["src/*"], "../types": ["src/types"]}, "types": ["node"]}, "include": ["src/api", "src/services", "src/database", "src/utils", "src/types", "src/modules.d.ts", "src/types.ts"], "ts-node": {"transpileOnly": true}}