# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build & Test Commands
- `npm run dev`: Start both frontend (Vite) and backend (Node.js)
- `npm run dev:frontend`: Run frontend only (Vite) on port 5173
- `npm run dev:backend`: Run backend only (Node.js) with nodemon
- `npm run lint`: Run ESLint on src directory
- `npm test`: Run all Jest tests
- `npm test:unit`: Run unit tests only
- `npm test:integration`: Run integration tests
- `npm test:real-data`: Run tests against real API data (requires API keys)
- `npm test:real-data:verbose`: Run real data tests with verbose logging
- `npm test:api-check`: Check API connection status
- `npm test -- tests/unit/path/to/test.test.ts`: Run a single test file
- `npx jest -t "test description"`: Run tests matching description
- `npm test:e2e`: Run Playwright e2e tests
- `npm test:e2e:ui`: Run Playwright tests with UI debugger
- `npm test:e2e:headed`: Run Playwright tests in headed mode
- `npm test:e2e:debug`: Run Playwright tests in debug mode
- `npm test:e2e:basic`: Run basic navigation tests only
- `npm run build`: Full production build using scripts/build-production.js
- `npm run deploy:preview`: Deploy to preview environment
- `npm run deploy:production`: Deploy to production environment
- `npm run kill-ports`: Kill processes on ports 3002 and 5173
- `npm run update-version`: Update version history from git commits

## Code Style & Architecture
- TypeScript-first with proper interfaces and type definitions
- Functional React components with hooks (no class components)
- Controllers handle request/response, services contain business logic
- PascalCase for components and types, camelCase for functions and variables
- Group imports: external first, then internal, use aliases (@/* for src/*)
- Error handling: Use utilities in `src/utils/error.ts` with specific error types
- Adaptive responsive design using container queries over device detection
- Container/Presentation pattern for React components
- Repository pattern for data access abstraction
- Service layer for business logic encapsulation
- Adapter pattern for normalizing external API data
- Cache-aside pattern for performance optimization
- CSS Architecture: Foundation layers in `src/frontend/styles/foundation.css` using Tailwind `@layer`
- Component-specific styles using container queries (`@container`) for responsive behavior
- Variant system in `src/frontend/utils/component-variants.ts` for consistent styling

## Project-Specific Conventions
- **Financial Color Standards**:
  - Green ONLY for income/positive transactions
  - Red/accent ONLY for expenses/negative values
  - Category colors: Payroll=blue, Software=purple, Taxes=indigo
- **Smart Forecast System**: Refer to DEVELOPMENT-GUIDE.md for projection logic rules
- **Testing Strategy**: Unit tests with Jest, React Testing Library for components, E2E with Playwright
- **Performance Optimizations**:
  - Memoization for expensive calculations
  - Efficient data transformation using map/reduce
  - Cache invalidation strategies by service
  - Environment-specific caching TTLs

## Git & PR Guidelines
- **ALWAYS use the "preview" branch** for changes unless instructed otherwise
- Never push directly to main branch without explicit confirmation
- PR descriptions should be clear, reference issues, include screenshots for UI changes

## Database Schema & Data Model
- SQLite database for persistence with structured schema
- Field ownership tracking for external integrations (HubSpot/Xero/Harvest)
- Deal-Estimate relationship through linking tables
- Audit trail for changes with detailed logging
- Estimate allocation structure for staff time tracking
- Uses migrations system for schema changes located in `src/database/migrations/`
- **Repository Pattern**: All data access goes through repository classes in `src/api/repositories/`
- **Company Linking System**: Upstream database as source of truth with external system linking
- **External ID Management**: Companies can be linked to HubSpot (`hubspot_id`) and Harvest (`harvest_id`)
- **Data Management Interface**: Centralized UI for managing company links between systems (CRM > Data Management)
- **Company Merge Functionality**: Handles duplicate companies from different sources via `mergeCompanies()` method
- **Linking Status Tracking**: Four states: `both`, `hubspot_only`, `harvest_only`, `none`
- **IMPORTANT**: Always check if tables exist before querying (especially `note` table)
- Recent refactoring (May 2025) implemented unified data model with contact_role relationships
- Run `node scripts/fix-data-model.js` if encountering missing table errors
- Database initialization: `node scripts/initialize-fresh-database.js` for fresh setup
- Schema validation: `node scripts/validate-data-model.js` to check database integrity

## Key Libraries
### React & Frontend
- Uses functional components with hooks (useState, useEffect, useMemo, useContext)
- React Query for data fetching with proper error handling and loading states
- TailwindCSS for styling with project-specific custom components
- Recharts for financial visualizations with consistent tooltip and responsive components

### Backend & API Integration
- Express for RESTful API endpoints with consistent response structures
- Xero API for accounting data with proper OAuth2 authentication
- Harvest API for time tracking and project management integration
- Cache responses appropriately to minimize external API calls
- Robust error handling with exponential backoff for API requests
- Rate limiting management for external APIs
- Parallel data fetching for performance optimization
- Circuit breaker pattern for external service failures
- Caching mechanism with configurable TTL per service
- Field mapping between external systems and internal data model

## Core Application Features
### Smart Forecast System
The Smart Forecast system is a central feature that automatically generates income projections based on:
- Remaining project budgets from Harvest
- Uninvoiced work already completed
- Project-specific invoice frequencies and payment terms
- Retrospective billing model for invoice projections
- Three key filtering rules: payment terms, real invoice duplicate, uninvoiced work
- Projected vs. real invoice distinction to prevent double-counting
- Key files: `src/services/cashflow/projection-filter-service.ts`, `src/services/cashflow/daily-cashflow-service.ts`

### Cashflow Projection
Visualizes financial projections with:
- Interactive chart with scenarios (worst-case, expected, best-case)
- Color-coded transaction types (income/expense)
- Projection auditing with detailed decision tracking
- Key components: `src/frontend/components/ForwardProjection/CashflowChart.tsx`, `src/frontend/components/ForwardProjection/TransactionsList.tsx`

### CRM & Deal Management
- Kanban board for deal pipeline visualization
- Deal editing with custom fields and estimate linking
- Deal probability visualization for financial forecasting
- Key components: `src/frontend/components/CRM/Board/`, `src/frontend/components/CRM/DealEdit/`

### Integrations
- **Xero**: Authentication via OAuth2, handles rate limiting with retry logic
- **Harvest**: Projects, invoices, time tracking with API token authentication
- **HubSpot**: CRM data synchronization with bidirectional field mapping
- All integrations have robust error handling and rate limiting management
- Key files: `src/api/integrations/xero.ts`, `src/api/integrations/harvest.ts`, `src/api/integrations/base.ts`

### Xero MCP (Model Context Protocol) Integration
- Chat-based interaction with Xero data via `/api/mcp` endpoint
- Comprehensive command set documented in `docs/xero-mcp-*.md`
- Uses `@modelcontextprotocol/sdk` and `@anthropic-ai/sdk`
- Enables natural language queries for Xero accounting data

## Deployment & Environment
- Render.com deployment with persistent disk
- SQLite database mounted at /data in production
- Branch structure: main (production), preview (testing)
- Pre-built branches: pre-built-preview, pre-built-production
- Unified deployment script: `scripts/deploy-v2.sh [preview|production]`
- Build script: `scripts/build-production.js` handles full build process
- Deployment URLs:
  - Preview: `https://upstream-preview.onbord.au`
  - Production: `https://upstream.onbord.au`
- See `docs/DEPLOYMENT-V2.md` for detailed deployment instructions

## Important Development Notes
- API keys required for Xero and Harvest - see README.md for setup
- Fixed ports are used: API on 3002, Frontend on 5173
- Running `npm run dev` starts both frontend and backend
- Update version history with `npm run update-version`
- The app has undergone recent refactoring (May 2025) to implement unified data model and repository pattern
- Deployment process simplified (May 2025) with new unified scripts
- See `DEVELOPMENT-GUIDE.md` for detailed architectural information and troubleshooting
- Check `docs/technical/data-model/fixes-applied.md` for recent data model fixes and migration info
- Version history system uses git commits with prefixes (`feat:`, `fix:`, `docs:`, `chore:`) for automatic categorization
- Real data testing config in `tests/real-data-config.js` for live API integration testing

## Database Recovery & Troubleshooting
- If repository errors occur, run diagnostic scripts in `scripts/` directory
- `node scripts/fix-data-model.js`: Creates missing tables (especially `note` table)
- Repository classes include table existence checks to prevent runtime errors
- All repositories extend base repository pattern for consistent error handling

## Important Instruction Reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
