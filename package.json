{"name": "onbord-financial-dashboard-preview", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "nodemon", "dev:frontend": "vite --port 5173", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration --setupFilesAfterEnv=<rootDir>/tests/setup/setup.int.ts", "test:real-data": "jest --config=tests/real-data-config.js", "test:real-data:verbose": "VERBOSE=true jest --config=tests/real-data-config.js", "test:api-check": "ts-node -r tsconfig-paths/register tests/real-data/api-connection.test.ts", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:basic": "playwright test tests/e2e/basic-navigation.spec.ts", "build": "node scripts/build-production.js", "build:backend": "tsc --project tsconfig.backend.json", "build:frontend": "node scripts/build-frontend.js", "copy:public": "node scripts/copy-public.js", "create:types": "node scripts/create-types.js", "deploy:preview": "bash scripts/deploy-v2.sh preview", "deploy:production": "bash scripts/deploy-v2.sh production", "deploy": "bash scripts/deploy-v2.sh", "lint": "eslint src --ext ts,tsx", "start": "node dist/api/server.js", "start:pre-built": "node scripts/build-detect.js || true && npm run start", "kill-ports": "bash -c \"lsof -ti:3002,5173 | xargs kill -9 2>/dev/null || echo 'No processes found on ports 3002 and 5173'\"", "preinstall": "npm config set legacy-peer-deps true", "update-version": "node scripts/update-version-history.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@heroicons/react": "^2.2.0", "@hubspot/api-client": "^12.1.0", "@modelcontextprotocol/sdk": "^1.12.0", "@types/cors": "^2.8.17", "@types/d3": "^7.4.3", "@types/react-datepicker": "^4.19.5", "@types/react-dom": "^18.2.0", "@types/socket.io": "^3.0.2", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.2.1", "axios": "^1.6.7", "better-sqlite3": "^11.10.0", "blueimp-md5": "^2.19.0", "cmdk": "^1.1.1", "connect-redis": "^8.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^4.3.0", "d3": "^7.8.5", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "express": "^4.18.2", "express-session": "^1.18.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.511.0", "node": "^24.0.1", "node-fetch": "^2.7.0", "postcss": "^8.5.3", "react": "^18.2.0", "react-datepicker": "^4.25.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-query": "^3.39.3", "react-router-dom": "^7.5.3", "recharts": "^2.12.1", "redis": "^4.7.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17", "uuid": "^11.1.0", "xero-node": "^10.0.0"}, "devDependencies": {"@playwright/test": "^1.41.2", "@tailwindcss/container-queries": "^0.1.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/better-sqlite3": "^7.6.12", "@types/blueimp-md5": "^2.18.2", "@types/cookie-parser": "^1.4.8", "@types/d3": "^7.4.3", "@types/express": "^4.17.22", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.12", "@types/node": "^22.15.21", "@types/react": "^19.1.2", "@types/react-datepicker": "^4.19.5", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "axios-mock-adapter": "^2.1.0", "concurrently": "^8.2.2", "eslint": "^8.56.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "markdownlint-rule-search-replace": "^1.2.0", "nodemon": "^3.1.9", "postcss": "^8.5.3", "puppeteer": "^22.6.1", "supertest": "^6.3.4", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vite": "^5.4.17"}}